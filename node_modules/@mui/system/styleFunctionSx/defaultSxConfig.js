"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _spacing = require("../spacing");
var _borders = require("../borders");
var _cssGrid = require("../cssGrid");
var _palette = require("../palette");
var _sizing = require("../sizing");
const defaultSxConfig = {
  // borders
  border: {
    themeKey: 'borders',
    transform: _borders.borderTransform
  },
  borderTop: {
    themeKey: 'borders',
    transform: _borders.borderTransform
  },
  borderRight: {
    themeKey: 'borders',
    transform: _borders.borderTransform
  },
  borderBottom: {
    themeKey: 'borders',
    transform: _borders.borderTransform
  },
  borderLeft: {
    themeKey: 'borders',
    transform: _borders.borderTransform
  },
  borderColor: {
    themeKey: 'palette'
  },
  borderTopColor: {
    themeKey: 'palette'
  },
  borderRightColor: {
    themeKey: 'palette'
  },
  borderBottomColor: {
    themeKey: 'palette'
  },
  borderLeftColor: {
    themeKey: 'palette'
  },
  outline: {
    themeKey: 'borders',
    transform: _borders.borderTransform
  },
  outlineColor: {
    themeKey: 'palette'
  },
  borderRadius: {
    themeKey: 'shape.borderRadius',
    style: _borders.borderRadius
  },
  // palette
  color: {
    themeKey: 'palette',
    transform: _palette.paletteTransform
  },
  bgcolor: {
    themeKey: 'palette',
    cssProperty: 'backgroundColor',
    transform: _palette.paletteTransform
  },
  backgroundColor: {
    themeKey: 'palette',
    transform: _palette.paletteTransform
  },
  // spacing
  p: {
    style: _spacing.padding
  },
  pt: {
    style: _spacing.padding
  },
  pr: {
    style: _spacing.padding
  },
  pb: {
    style: _spacing.padding
  },
  pl: {
    style: _spacing.padding
  },
  px: {
    style: _spacing.padding
  },
  py: {
    style: _spacing.padding
  },
  padding: {
    style: _spacing.padding
  },
  paddingTop: {
    style: _spacing.padding
  },
  paddingRight: {
    style: _spacing.padding
  },
  paddingBottom: {
    style: _spacing.padding
  },
  paddingLeft: {
    style: _spacing.padding
  },
  paddingX: {
    style: _spacing.padding
  },
  paddingY: {
    style: _spacing.padding
  },
  paddingInline: {
    style: _spacing.padding
  },
  paddingInlineStart: {
    style: _spacing.padding
  },
  paddingInlineEnd: {
    style: _spacing.padding
  },
  paddingBlock: {
    style: _spacing.padding
  },
  paddingBlockStart: {
    style: _spacing.padding
  },
  paddingBlockEnd: {
    style: _spacing.padding
  },
  m: {
    style: _spacing.margin
  },
  mt: {
    style: _spacing.margin
  },
  mr: {
    style: _spacing.margin
  },
  mb: {
    style: _spacing.margin
  },
  ml: {
    style: _spacing.margin
  },
  mx: {
    style: _spacing.margin
  },
  my: {
    style: _spacing.margin
  },
  margin: {
    style: _spacing.margin
  },
  marginTop: {
    style: _spacing.margin
  },
  marginRight: {
    style: _spacing.margin
  },
  marginBottom: {
    style: _spacing.margin
  },
  marginLeft: {
    style: _spacing.margin
  },
  marginX: {
    style: _spacing.margin
  },
  marginY: {
    style: _spacing.margin
  },
  marginInline: {
    style: _spacing.margin
  },
  marginInlineStart: {
    style: _spacing.margin
  },
  marginInlineEnd: {
    style: _spacing.margin
  },
  marginBlock: {
    style: _spacing.margin
  },
  marginBlockStart: {
    style: _spacing.margin
  },
  marginBlockEnd: {
    style: _spacing.margin
  },
  // display
  displayPrint: {
    cssProperty: false,
    transform: value => ({
      '@media print': {
        display: value
      }
    })
  },
  display: {},
  overflow: {},
  textOverflow: {},
  visibility: {},
  whiteSpace: {},
  // flexbox
  flexBasis: {},
  flexDirection: {},
  flexWrap: {},
  justifyContent: {},
  alignItems: {},
  alignContent: {},
  order: {},
  flex: {},
  flexGrow: {},
  flexShrink: {},
  alignSelf: {},
  justifyItems: {},
  justifySelf: {},
  // grid
  gap: {
    style: _cssGrid.gap
  },
  rowGap: {
    style: _cssGrid.rowGap
  },
  columnGap: {
    style: _cssGrid.columnGap
  },
  gridColumn: {},
  gridRow: {},
  gridAutoFlow: {},
  gridAutoColumns: {},
  gridAutoRows: {},
  gridTemplateColumns: {},
  gridTemplateRows: {},
  gridTemplateAreas: {},
  gridArea: {},
  // positions
  position: {},
  zIndex: {
    themeKey: 'zIndex'
  },
  top: {},
  right: {},
  bottom: {},
  left: {},
  // shadows
  boxShadow: {
    themeKey: 'shadows'
  },
  // sizing
  width: {
    transform: _sizing.sizingTransform
  },
  maxWidth: {
    style: _sizing.maxWidth
  },
  minWidth: {
    transform: _sizing.sizingTransform
  },
  height: {
    transform: _sizing.sizingTransform
  },
  maxHeight: {
    transform: _sizing.sizingTransform
  },
  minHeight: {
    transform: _sizing.sizingTransform
  },
  boxSizing: {},
  // typography
  font: {
    themeKey: 'font'
  },
  fontFamily: {
    themeKey: 'typography'
  },
  fontSize: {
    themeKey: 'typography'
  },
  fontStyle: {
    themeKey: 'typography'
  },
  fontWeight: {
    themeKey: 'typography'
  },
  letterSpacing: {},
  textTransform: {},
  lineHeight: {},
  textAlign: {},
  typography: {
    cssProperty: false,
    themeKey: 'typography'
  }
};
var _default = exports.default = defaultSxConfig;