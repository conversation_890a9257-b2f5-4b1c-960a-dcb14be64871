{"name": "@mui/system", "version": "6.4.7", "private": false, "author": "MUI Team", "description": "MUI System is a set of CSS utilities to help you build custom designs more efficiently. It makes it possible to rapidly lay out custom designs.", "main": "./index.js", "keywords": ["react", "react-component", "mui", "system"], "repository": {"type": "git", "url": "git+https://github.com/mui/material-ui.git", "directory": "packages/mui-system"}, "license": "MIT", "bugs": {"url": "https://github.com/mui/material-ui/issues"}, "homepage": "https://mui.com/system/getting-started/", "funding": {"type": "opencollective", "url": "https://opencollective.com/mui-org"}, "dependencies": {"@babel/runtime": "^7.26.0", "clsx": "^2.1.1", "csstype": "^3.1.3", "prop-types": "^15.8.1", "@mui/private-theming": "^6.4.6", "@mui/utils": "^6.4.6", "@mui/styled-engine": "^6.4.6", "@mui/types": "^7.2.21"}, "peerDependencies": {"@emotion/react": "^11.5.0", "@emotion/styled": "^11.3.0", "@types/react": "^17.0.0 || ^18.0.0 || ^19.0.0", "react": "^17.0.0 || ^18.0.0 || ^19.0.0"}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@emotion/react": {"optional": true}, "@emotion/styled": {"optional": true}}, "sideEffects": false, "publishConfig": {"access": "public", "directory": "build"}, "engines": {"node": ">=14.0.0"}, "module": "./esm/index.js", "types": "./index.d.ts"}