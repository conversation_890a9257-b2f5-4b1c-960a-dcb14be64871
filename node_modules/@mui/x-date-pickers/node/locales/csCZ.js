"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.csCZ = void 0;
var _getPickersLocalization = require("./utils/getPickersLocalization");
// maps TimeView to its translation
const timeViews = {
  hours: 'Hodiny',
  minutes: 'Minuty',
  seconds: 'Sekundy',
  meridiem: 'Odpoledne'
};
const csCZPickers = {
  // Calendar navigation
  previousMonth: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> mě<PERSON>',
  nextMonth: '<PERSON><PERSON><PERSON> m<PERSON>',
  // View navigation
  openPreviousView: 'Otevřít předchozí zobrazení',
  openNextView: 'Otevřít dalš<PERSON> zobrazení',
  calendarViewSwitchingButtonAriaLabel: view => view === 'year' ? 'ročn<PERSON> zobrazení otevřeno, přepněte do zobrazení kalendáře' : 'zobrazen<PERSON> kalendáře otevřeno, přepněte do zobrazení roku',
  // DateRange labels
  start: 'Začátek',
  end: '<PERSON>ne<PERSON>',
  startDate: 'Datum začátku',
  startTime: '<PERSON><PERSON> začátku',
  endDate: 'Datum konce',
  endTime: 'Čas konce',
  // Action bar
  cancelButtonLabel: 'Zrušit',
  clearButtonLabel: 'Vymazat',
  okButtonLabel: 'Potvrdit',
  todayButtonLabel: 'Dnes',
  // Toolbar titles
  datePickerToolbarTitle: 'Vyberte datum',
  dateTimePickerToolbarTitle: 'Vyberte datum a čas',
  timePickerToolbarTitle: 'Vyberte čas',
  dateRangePickerToolbarTitle: 'Vyberete rozmezí dat',
  // Clock labels
  clockLabelText: (view, time, utils, formattedTime) => `${timeViews[view] ?? view} vybrány. ${!formattedTime && (time === null || !utils.isValid(time)) ? 'Není vybrán čas' : `Vybraný čas je ${formattedTime ?? utils.format(time, 'fullTime')}`}`,
  hoursClockNumberText: hours => `${hours} hodin`,
  minutesClockNumberText: minutes => `${minutes} minut`,
  secondsClockNumberText: seconds => `${seconds} sekund`,
  // Digital clock labels
  selectViewText: view => `Vyberte ${timeViews[view]}`,
  // Calendar labels
  calendarWeekNumberHeaderLabel: 'Týden v roce',
  calendarWeekNumberHeaderText: '#',
  calendarWeekNumberAriaLabelText: weekNumber => `${weekNumber} týden v roce`,
  calendarWeekNumberText: weekNumber => `${weekNumber}`,
  // Open picker labels
  openDatePickerDialogue: (value, utils, formattedDate) => formattedDate || value !== null && utils.isValid(value) ? `Vyberte datum, vybrané datum je ${formattedDate ?? utils.format(value, 'fullDate')}` : 'Vyberte datum',
  openTimePickerDialogue: (value, utils, formattedTime) => formattedTime || value !== null && utils.isValid(value) ? `Vyberte čas, vybraný čas je ${formattedTime ?? utils.format(value, 'fullTime')}` : 'Vyberte čas',
  fieldClearLabel: 'Vymazat',
  // Table labels
  timeTableLabel: 'vyberte čas',
  dateTableLabel: 'vyberte datum',
  // Field section placeholders
  fieldYearPlaceholder: params => 'Y'.repeat(params.digitAmount),
  fieldMonthPlaceholder: params => params.contentType === 'letter' ? 'MMMM' : 'MM',
  fieldDayPlaceholder: () => 'DD',
  fieldWeekDayPlaceholder: params => params.contentType === 'letter' ? 'EEEE' : 'EE',
  fieldHoursPlaceholder: () => 'hh',
  fieldMinutesPlaceholder: () => 'mm',
  fieldSecondsPlaceholder: () => 'ss',
  fieldMeridiemPlaceholder: () => 'aa',
  // View names
  year: 'Rok',
  month: 'Měsíc',
  day: 'Den',
  weekDay: 'Pracovní den',
  hours: 'Hodiny',
  minutes: 'Minuty',
  seconds: 'Sekundy',
  meridiem: 'Odpoledne',
  // Common
  empty: 'Prázdný'
};
const csCZ = exports.csCZ = (0, _getPickersLocalization.getPickersLocalization)(csCZPickers);