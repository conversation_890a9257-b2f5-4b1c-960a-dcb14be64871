"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.fiFI = void 0;
var _getPickersLocalization = require("./utils/getPickersLocalization");
const views = {
  hours: 'tunnit',
  minutes: 'minuutit',
  seconds: 'sekuntit',
  meridiem: 'iltapäiv<PERSON>'
};
const fiFIPickers = {
  // Calendar navigation
  previousMonth: 'Edellinen kuukausi',
  nextMonth: 'Seuraava kuukausi',
  // View navigation
  openPreviousView: 'Avaa edellinen näkymä',
  openNextView: 'Avaa seuraava näkymä',
  calendarViewSwitchingButtonAriaLabel: view => view === 'year' ? 'vuosinäkymä on auki, vaihda kalenterinäkymään' : 'kalenterinäkymä on auki, vaihda vuosinäkymään',
  // DateRange labels
  start: 'Al<PERSON>',
  end: 'Loppu',
  startDate: 'Alkamispäivämäär<PERSON>',
  startTime: 'Alkamisai<PERSON>',
  endDate: 'Päättymispäivämäärä',
  endTime: 'Päättymisaika',
  // Action bar
  cancelButtonLabel: 'Peruuta',
  clearButtonLabel: 'Tyhjennä',
  okButtonLabel: 'OK',
  todayButtonLabel: 'Tänään',
  // Toolbar titles
  datePickerToolbarTitle: 'Valitse päivä',
  dateTimePickerToolbarTitle: 'Valitse päivä ja aika',
  timePickerToolbarTitle: 'Valitse aika',
  dateRangePickerToolbarTitle: 'Valitse aikaväli',
  // Clock labels
  clockLabelText: (view, time, utils, formattedTime) => `Valitse ${views[view]}. ${!formattedTime && (time === null || !utils.isValid(time)) ? 'Ei aikaa valittuna' : `Valittu aika on ${formattedTime ?? utils.format(time, 'fullTime')}`}`,
  hoursClockNumberText: hours => `${hours} tuntia`,
  minutesClockNumberText: minutes => `${minutes} minuuttia`,
  secondsClockNumberText: seconds => `${seconds} sekuntia`,
  // Digital clock labels
  selectViewText: view => `Valitse ${views[view]}`,
  // Calendar labels
  calendarWeekNumberHeaderLabel: 'Viikko',
  calendarWeekNumberHeaderText: '#',
  calendarWeekNumberAriaLabelText: weekNumber => `Viikko ${weekNumber}`,
  calendarWeekNumberText: weekNumber => `${weekNumber}`,
  // Open picker labels
  openDatePickerDialogue: (value, utils, formattedDate) => formattedDate || value !== null && utils.isValid(value) ? `Valitse päivä, valittu päivä on ${formattedDate ?? utils.format(value, 'fullDate')}` : 'Valitse päivä',
  openTimePickerDialogue: (value, utils, formattedTime) => formattedTime || value !== null && utils.isValid(value) ? `Valitse aika, valittu aika on ${formattedTime ?? utils.format(value, 'fullTime')}` : 'Valitse aika',
  fieldClearLabel: 'Tyhjennä arvo',
  // Table labels
  timeTableLabel: 'valitse aika',
  dateTableLabel: 'valitse päivä',
  // Field section placeholders
  fieldYearPlaceholder: params => 'V'.repeat(params.digitAmount),
  fieldMonthPlaceholder: params => params.contentType === 'letter' ? 'KKKK' : 'KK',
  fieldDayPlaceholder: () => 'PP',
  fieldWeekDayPlaceholder: params => params.contentType === 'letter' ? 'EEEE' : 'EE',
  fieldHoursPlaceholder: () => 'tt',
  fieldMinutesPlaceholder: () => 'mm',
  fieldSecondsPlaceholder: () => 'ss',
  fieldMeridiemPlaceholder: () => 'aa',
  // View names
  year: 'Vuosi',
  month: 'Kuukausi',
  day: 'Päivä',
  weekDay: 'Viikonpäivä',
  hours: 'Tunnit',
  minutes: 'Minuutit',
  seconds: 'Sekunnit',
  meridiem: 'Iltapäivä',
  // Common
  empty: 'Tyhjä'
};
const fiFI = exports.fiFI = (0, _getPickersLocalization.getPickersLocalization)(fiFIPickers);