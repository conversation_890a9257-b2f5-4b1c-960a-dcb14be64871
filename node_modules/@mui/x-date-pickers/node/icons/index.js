"use strict";

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.TimeIcon = exports.DateRangeIcon = exports.ClockIcon = exports.ClearIcon = exports.CalendarIcon = exports.ArrowRightIcon = exports.ArrowLeftIcon = exports.ArrowDropDownIcon = void 0;
var _utils = require("@mui/material/utils");
var React = _interopRequireWildcard(require("react"));
var _jsxRuntime = require("react/jsx-runtime");
/**
 * @ignore - internal component.
 */
const ArrowDropDownIcon = exports.ArrowDropDownIcon = (0, _utils.createSvgIcon)(/*#__PURE__*/(0, _jsxRuntime.jsx)("path", {
  d: "M7 10l5 5 5-5z"
}), 'ArrowDropDown');

/**
 * @ignore - internal component.
 */
const ArrowLeftIcon = exports.ArrowLeftIcon = (0, _utils.createSvgIcon)(/*#__PURE__*/(0, _jsxRuntime.jsx)("path", {
  d: "M15.41 16.59L10.83 12l4.58-4.59L14 6l-6 6 6 6 1.41-1.41z"
}), 'ArrowLeft');

/**
 * @ignore - internal component.
 */
const ArrowRightIcon = exports.ArrowRightIcon = (0, _utils.createSvgIcon)(/*#__PURE__*/(0, _jsxRuntime.jsx)("path", {
  d: "M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"
}), 'ArrowRight');

/**
 * @ignore - internal component.
 */
const CalendarIcon = exports.CalendarIcon = (0, _utils.createSvgIcon)(/*#__PURE__*/(0, _jsxRuntime.jsx)("path", {
  d: "M17 12h-5v5h5v-5zM16 1v2H8V1H6v2H5c-1.11 0-1.99.9-1.99 2L3 19c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2h-1V1h-2zm3 18H5V8h14v11z"
}), 'Calendar');

/**
 * @ignore - internal component.
 */
const ClockIcon = exports.ClockIcon = (0, _utils.createSvgIcon)(/*#__PURE__*/(0, _jsxRuntime.jsxs)(React.Fragment, {
  children: [/*#__PURE__*/(0, _jsxRuntime.jsx)("path", {
    d: "M11.99 2C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2zM12 20c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8z"
  }), /*#__PURE__*/(0, _jsxRuntime.jsx)("path", {
    d: "M12.5 7H11v6l5.25 3.15.75-1.23-4.5-2.67z"
  })]
}), 'Clock');

/**
 * @ignore - internal component.
 */
const DateRangeIcon = exports.DateRangeIcon = (0, _utils.createSvgIcon)(/*#__PURE__*/(0, _jsxRuntime.jsx)("path", {
  d: "M9 11H7v2h2v-2zm4 0h-2v2h2v-2zm4 0h-2v2h2v-2zm2-7h-1V2h-2v2H8V2H6v2H5c-1.11 0-1.99.9-1.99 2L3 20c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 16H5V9h14v11z"
}), 'DateRange');

/**
 * @ignore - internal component.
 */
const TimeIcon = exports.TimeIcon = (0, _utils.createSvgIcon)(/*#__PURE__*/(0, _jsxRuntime.jsxs)(React.Fragment, {
  children: [/*#__PURE__*/(0, _jsxRuntime.jsx)("path", {
    d: "M11.99 2C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2zM12 20c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8z"
  }), /*#__PURE__*/(0, _jsxRuntime.jsx)("path", {
    d: "M12.5 7H11v6l5.25 3.15.75-1.23-4.5-2.67z"
  })]
}), 'Time');

/**
 * @ignore - internal component.
 */
const ClearIcon = exports.ClearIcon = (0, _utils.createSvgIcon)(/*#__PURE__*/(0, _jsxRuntime.jsx)("path", {
  d: "M19 6.41 17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"
}), 'Clear');