"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.getPickersOutlinedInputUtilityClass = getPickersOutlinedInputUtilityClass;
exports.pickersOutlinedInputClasses = void 0;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var _generateUtilityClasses = _interopRequireDefault(require("@mui/utils/generateUtilityClasses"));
var _generateUtilityClass = _interopRequireDefault(require("@mui/utils/generateUtilityClass"));
var _PickersInputBase = require("../PickersInputBase");
function getPickersOutlinedInputUtilityClass(slot) {
  return (0, _generateUtilityClass.default)('MuiPickersOutlinedInput', slot);
}
const pickersOutlinedInputClasses = exports.pickersOutlinedInputClasses = (0, _extends2.default)({}, _PickersInputBase.pickersInputBaseClasses, (0, _generateUtilityClasses.default)('MuiPickersOutlinedInput', ['root', 'notchedOutline', 'input']));