"use strict";

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.renderTimeViewClock = exports.renderMultiSectionDigitalClockTimeView = exports.renderDigitalClockTimeView = void 0;
var React = _interopRequireWildcard(require("react"));
var _TimeClock = require("../TimeClock");
var _DigitalClock = require("../DigitalClock");
var _MultiSectionDigitalClock = require("../MultiSectionDigitalClock");
var _timeUtils = require("../internals/utils/time-utils");
var _jsxRuntime = require("react/jsx-runtime");
const renderTimeViewClock = ({
  view,
  onViewChange,
  focusedView,
  onFocusedViewChange,
  views,
  value,
  defaultValue,
  referenceDate,
  onChange,
  className,
  classes,
  disableFuture,
  disablePast,
  minTime,
  maxTime,
  shouldDisableTime,
  minutesStep,
  ampm,
  ampmInClock,
  slots,
  slotProps,
  readOnly,
  disabled,
  sx,
  autoFocus,
  showViewSwitcher,
  disableIgnoringDatePartForTimeValidation,
  timezone
}) => /*#__PURE__*/(0, _jsxRuntime.jsx)(_TimeClock.TimeClock, {
  view: view,
  onViewChange: onViewChange,
  focusedView: focusedView && (0, _timeUtils.isTimeView)(focusedView) ? focusedView : null,
  onFocusedViewChange: onFocusedViewChange,
  views: views.filter(_timeUtils.isTimeView),
  value: value,
  defaultValue: defaultValue,
  referenceDate: referenceDate,
  onChange: onChange,
  className: className,
  classes: classes,
  disableFuture: disableFuture,
  disablePast: disablePast,
  minTime: minTime,
  maxTime: maxTime,
  shouldDisableTime: shouldDisableTime,
  minutesStep: minutesStep,
  ampm: ampm,
  ampmInClock: ampmInClock,
  slots: slots,
  slotProps: slotProps,
  readOnly: readOnly,
  disabled: disabled,
  sx: sx,
  autoFocus: autoFocus,
  showViewSwitcher: showViewSwitcher,
  disableIgnoringDatePartForTimeValidation: disableIgnoringDatePartForTimeValidation,
  timezone: timezone
});
exports.renderTimeViewClock = renderTimeViewClock;
const renderDigitalClockTimeView = ({
  view,
  onViewChange,
  focusedView,
  onFocusedViewChange,
  views,
  value,
  defaultValue,
  referenceDate,
  onChange,
  className,
  classes,
  disableFuture,
  disablePast,
  minTime,
  maxTime,
  shouldDisableTime,
  minutesStep,
  ampm,
  slots,
  slotProps,
  readOnly,
  disabled,
  sx,
  autoFocus,
  disableIgnoringDatePartForTimeValidation,
  timeSteps,
  skipDisabled,
  timezone
}) => /*#__PURE__*/(0, _jsxRuntime.jsx)(_DigitalClock.DigitalClock, {
  view: view,
  onViewChange: onViewChange,
  focusedView: focusedView,
  onFocusedViewChange: onFocusedViewChange,
  views: views.filter(_timeUtils.isTimeView),
  value: value,
  defaultValue: defaultValue,
  referenceDate: referenceDate,
  onChange: onChange,
  className: className,
  classes: classes,
  disableFuture: disableFuture,
  disablePast: disablePast,
  minTime: minTime,
  maxTime: maxTime,
  shouldDisableTime: shouldDisableTime,
  minutesStep: minutesStep,
  ampm: ampm,
  slots: slots,
  slotProps: slotProps,
  readOnly: readOnly,
  disabled: disabled,
  sx: sx,
  autoFocus: autoFocus,
  disableIgnoringDatePartForTimeValidation: disableIgnoringDatePartForTimeValidation,
  timeStep: timeSteps?.minutes,
  skipDisabled: skipDisabled,
  timezone: timezone
});
exports.renderDigitalClockTimeView = renderDigitalClockTimeView;
const renderMultiSectionDigitalClockTimeView = ({
  view,
  onViewChange,
  focusedView,
  onFocusedViewChange,
  views,
  value,
  defaultValue,
  referenceDate,
  onChange,
  className,
  classes,
  disableFuture,
  disablePast,
  minTime,
  maxTime,
  shouldDisableTime,
  minutesStep,
  ampm,
  slots,
  slotProps,
  readOnly,
  disabled,
  sx,
  autoFocus,
  disableIgnoringDatePartForTimeValidation,
  timeSteps,
  skipDisabled,
  timezone
}) => /*#__PURE__*/(0, _jsxRuntime.jsx)(_MultiSectionDigitalClock.MultiSectionDigitalClock, {
  view: view,
  onViewChange: onViewChange,
  focusedView: focusedView,
  onFocusedViewChange: onFocusedViewChange,
  views: views.filter(_timeUtils.isTimeView),
  value: value,
  defaultValue: defaultValue,
  referenceDate: referenceDate,
  onChange: onChange,
  className: className,
  classes: classes,
  disableFuture: disableFuture,
  disablePast: disablePast,
  minTime: minTime,
  maxTime: maxTime,
  shouldDisableTime: shouldDisableTime,
  minutesStep: minutesStep,
  ampm: ampm,
  slots: slots,
  slotProps: slotProps,
  readOnly: readOnly,
  disabled: disabled,
  sx: sx,
  autoFocus: autoFocus,
  disableIgnoringDatePartForTimeValidation: disableIgnoringDatePartForTimeValidation,
  timeSteps: timeSteps,
  skipDisabled: skipDisabled,
  timezone: timezone
});
exports.renderMultiSectionDigitalClockTimeView = renderMultiSectionDigitalClockTimeView;