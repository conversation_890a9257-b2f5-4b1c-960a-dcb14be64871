import { UseDateTimeFieldProps } from './DateTimeField.types';
import { PickerValidDate } from '../models';
export declare const useDateTimeField: <TDate extends PickerValidDate, TEnableAccessibleFieldDOMStructure extends boolean, TAllProps extends UseDateTimeFieldProps<TDate, TEnableAccessibleFieldDOMStructure>>(inProps: TAllProps) => import("../internals").UseFieldResponse<TEnableAccessibleFieldDOMStructure, Omit<TAllProps & Omit<UseDateTimeFieldProps<TDate, TEnableAccessibleFieldDOMStructure>, keyof import("../internals/hooks/defaultizedFieldProps").UseDefaultizedDateTimeFieldBaseProps<any>> & Required<Pick<UseDateTimeFieldProps<TDate, TEnableAccessibleFieldDOMStructure>, keyof import("../internals/hooks/defaultizedFieldProps").UseDefaultizedDateTimeFieldBaseProps<any>>>, "disabled" | "readOnly" | "value" | "defaultValue" | "onChange" | "onError" | "format" | "minDate" | "maxDate" | "ampm" | "disableFuture" | "disablePast" | "timezone" | "referenceDate" | "formatDensity" | "shouldRespectLeadingZeros" | "selectedSections" | "onSelectedSectionsChange" | "unstableFieldRef" | "enableAccessibleFieldDOMStructure" | "minutesStep" | "shouldDisableDate" | "shouldDisableMonth" | "shouldDisableYear" | "minTime" | "maxTime" | "shouldDisableTime" | "disableIgnoringDatePartForTimeValidation" | "minDateTime" | "maxDateTime" | "dateSeparator">>;
