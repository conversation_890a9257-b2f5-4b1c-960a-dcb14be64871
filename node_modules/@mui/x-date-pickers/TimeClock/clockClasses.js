import { unstable_generateUtilityClass as generateUtilityClass, unstable_generateUtilityClasses as generateUtilityClasses } from '@mui/utils';
export function getClockUtilityClass(slot) {
  return generateUtilityClass('MuiClock', slot);
}
export const clockClasses = generateUtilityClasses('Mui<PERSON><PERSON>', ['root', 'clock', 'wrapper', 'squareMask', 'pin', 'amButton', 'pmButton', 'meridiemText', 'selected']);