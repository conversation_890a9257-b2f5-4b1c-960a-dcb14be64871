import { UseDateFieldProps } from './DateField.types';
import { PickerValidDate } from '../models';
export declare const useDateField: <TDate extends PickerValidDate, TEnableAccessibleFieldDOMStructure extends boolean, TAllProps extends UseDateFieldProps<TDate, TEnableAccessibleFieldDOMStructure>>(inProps: TAllProps) => import("../internals").UseFieldResponse<TEnableAccessibleFieldDOMStructure, Omit<TAllProps & Omit<UseDateFieldProps<TDate, TEnableAccessibleFieldDOMStructure>, keyof import("../internals/hooks/defaultizedFieldProps").UseDefaultizedDateFieldBaseProps<any>> & Required<Pick<UseDateFieldProps<TDate, TEnableAccessibleFieldDOMStructure>, keyof import("../internals/hooks/defaultizedFieldProps").UseDefaultizedDateFieldBaseProps<any>>>, "disabled" | "readOnly" | "value" | "defaultValue" | "onChange" | "onError" | "format" | "timezone" | "referenceDate" | "formatDensity" | "shouldRespectLeadingZeros" | "selectedSections" | "onSelectedSectionsChange" | "unstableFieldRef" | "enableAccessibleFieldDOMStructure" | "shouldDisableDate" | "shouldDisableMonth" | "shouldDisableYear" | keyof import("../internals").BaseDateValidationProps<any> | "dateSeparator">>;
