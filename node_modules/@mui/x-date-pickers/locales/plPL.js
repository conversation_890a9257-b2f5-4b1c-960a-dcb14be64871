import { getPickersLocalization } from "./utils/getPickersLocalization.js";
const timeViews = {
  hours: 'godzin',
  minutes: 'minut',
  seconds: 'sekund',
  meridiem: 'popołudnie'
};
const plPLPickers = {
  // Calendar navigation
  previousMonth: 'Poprzedni miesiąc',
  nextMonth: 'Następny miesiąc',
  // View navigation
  openPreviousView: 'Otwórz poprzedni widok',
  openNextView: 'Otwórz następny widok',
  calendarViewSwitchingButtonAriaLabel: view => view === 'year' ? 'otwarty jest widok roku, przełącz na widok kalendarza' : 'otwarty jest widok kalendarza, przełącz na widok roku',
  // DateRange labels
  start: 'Początek',
  end: 'Koniec',
  startDate: 'Data rozpoczęcia',
  startTime: '<PERSON><PERSON> rozpo<PERSON>',
  endDate: 'Data zakończenia',
  endTime: '<PERSON><PERSON> zakończ<PERSON>',
  // Action bar
  cancelButtonLabel: 'Anuluj',
  clearButtonLabel: 'Wyczyś<PERSON>',
  okButtonLabel: 'Zatwierdź',
  todayButtonLabel: 'Dzisiaj',
  // Toolbar titles
  datePickerToolbarTitle: 'Wybierz datę',
  dateTimePickerToolbarTitle: 'Wybierz datę i czas',
  timePickerToolbarTitle: 'Wybierz czas',
  dateRangePickerToolbarTitle: 'Wybierz zakres dat',
  // Clock labels
  clockLabelText: (view, time, utils, formattedTime) => `Wybierz ${timeViews[view]}. ${!formattedTime && (time === null || !utils.isValid(time)) ? 'Nie wybrano czasu' : `Wybrany czas to ${formattedTime ?? utils.format(time, 'fullTime')}`}`,
  hoursClockNumberText: hours => `${hours} godzin`,
  minutesClockNumberText: minutes => `${minutes} minut`,
  secondsClockNumberText: seconds => `${seconds} sekund`,
  // Digital clock labels
  selectViewText: view => `Wybierz ${timeViews[view]}`,
  // Calendar labels
  calendarWeekNumberHeaderLabel: 'Numer tygodnia',
  calendarWeekNumberHeaderText: '#',
  calendarWeekNumberAriaLabelText: weekNumber => `Tydzień ${weekNumber}`,
  calendarWeekNumberText: weekNumber => `${weekNumber}`,
  // Open picker labels
  openDatePickerDialogue: (value, utils, formattedDate) => value != null && utils.isValid(value) ? `Wybierz datę, obecnie wybrana data to ${formattedDate ?? utils.format(value, 'fullDate')}` : 'Wybierz datę',
  openTimePickerDialogue: (value, utils, formattedTime) => formattedTime || value !== null && utils.isValid(value) ? `Wybierz czas, obecnie wybrany czas to ${formattedTime ?? utils.format(value, 'fullTime')}` : 'Wybierz czas',
  fieldClearLabel: 'Wyczyść',
  // Table labels
  timeTableLabel: 'wybierz czas',
  dateTableLabel: 'wybierz datę',
  // Field section placeholders
  // fieldYearPlaceholder: params => 'Y'.repeat(params.digitAmount),
  // fieldMonthPlaceholder: params => params.contentType === 'letter' ? 'MMMM' : 'MM',
  // fieldDayPlaceholder: () => 'DD',
  // fieldWeekDayPlaceholder: params => params.contentType === 'letter' ? 'EEEE' : 'EE',
  // fieldHoursPlaceholder: () => 'hh',
  // fieldMinutesPlaceholder: () => 'mm',
  // fieldSecondsPlaceholder: () => 'ss',
  // fieldMeridiemPlaceholder: () => 'aa',

  // View names
  year: 'Rok',
  month: 'Miesiąc',
  day: 'Dzień',
  weekDay: 'Dzień tygodnia',
  hours: 'Godzin',
  minutes: 'Minut',
  seconds: 'Sekund'
  // meridiem: 'Meridiem',

  // Common
  // empty: 'Empty',
};
export const plPL = getPickersLocalization(plPLPickers);