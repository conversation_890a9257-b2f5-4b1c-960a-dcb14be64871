{"name": "@mui/x-date-pickers", "version": "7.27.3", "description": "The community edition of the Date and Time Picker components (MUI X).", "author": "MUI Team", "main": "./node/index.js", "license": "MIT", "bugs": {"url": "https://github.com/mui/mui-x/issues"}, "homepage": "https://mui.com/x/react-date-pickers/", "funding": {"type": "opencollective", "url": "https://opencollective.com/mui-org"}, "sideEffects": false, "publishConfig": {"access": "public", "directory": "build"}, "keywords": ["react", "react-component", "mui", "mui-x", "material-ui", "material design", "datepicker", "timepicker", "datetimepicker"], "repository": {"type": "git", "url": "git+https://github.com/mui/mui-x.git", "directory": "packages/x-date-pickers"}, "dependencies": {"@babel/runtime": "^7.25.7", "@mui/utils": "^5.16.6 || ^6.0.0", "@types/react-transition-group": "^4.4.11", "clsx": "^2.1.1", "prop-types": "^15.8.1", "react-transition-group": "^4.4.5", "@mui/x-internals": "7.26.0"}, "peerDependencies": {"@emotion/react": "^11.9.0", "@emotion/styled": "^11.8.1", "@mui/material": "^5.15.14 || ^6.0.0", "@mui/system": "^5.15.14 || ^6.0.0", "date-fns": "^2.25.0 || ^3.2.0 || ^4.0.0", "date-fns-jalali": "^2.13.0-0 || ^3.2.0-0 || ^4.0.0-0", "dayjs": "^1.10.7", "luxon": "^3.0.2", "moment": "^2.29.4", "moment-hijri": "^2.1.2 || ^3.0.0", "moment-jalaali": "^0.7.4 || ^0.8.0 || ^0.9.0 || ^0.10.0", "react": "^17.0.0 || ^18.0.0 || ^19.0.0", "react-dom": "^17.0.0 || ^18.0.0 || ^19.0.0"}, "peerDependenciesMeta": {"@emotion/react": {"optional": true}, "@emotion/styled": {"optional": true}, "date-fns": {"optional": true}, "date-fns-jalali": {"optional": true}, "dayjs": {"optional": true}, "luxon": {"optional": true}, "moment": {"optional": true}, "moment-hijri": {"optional": true}, "moment-jalaali": {"optional": true}}, "engines": {"node": ">=14.0.0"}, "private": false, "module": "./index.js", "types": "./index.d.ts"}