import { getPickersLocalization } from "./utils/getPickersLocalization.js";
// maps TimeView to its translation
const timeViews = {
  hours: '<PERSON>ra',
  minutes: 'Perc',
  seconds: '<PERSON><PERSON>odper<PERSON>',
  meridiem: '<PERSON><PERSON><PERSON><PERSON>'
};
const huHUPickers = {
  // Calendar navigation
  previousMonth: '<PERSON><PERSON><PERSON><PERSON> hónap',
  nextMonth: 'Következő hónap',
  // View navigation
  openPreviousView: '<PERSON><PERSON><PERSON>ő nézet megnyitása',
  openNextView: '<PERSON>övetkez<PERSON> nézet megnyitása',
  calendarViewSwitchingButtonAriaLabel: view => view === 'year' ? 'az évválasztó már nyitva, váltson a naptárnézetre' : 'a naptárnézet már nyitva, váltson az évválasztóra',
  // DateRange labels
  start: '<PERSON><PERSON><PERSON><PERSON> dátum',
  end: '<PERSON><PERSON><PERSON><PERSON> d<PERSON>',
  startDate: '<PERSON><PERSON><PERSON><PERSON> dátum',
  startTime: '<PERSON><PERSON><PERSON><PERSON> idő',
  endDate: '<PERSON><PERSON><PERSON><PERSON> dátum',
  endTime: '<PERSON><PERSON><PERSON><PERSON> idő',
  // Action bar
  cancelButtonLabel: 'Mégse',
  clearButtonLabel: 'Törlés',
  okButtonLabel: 'OK',
  todayButtonLabel: 'Ma',
  // Toolbar titles
  datePickerToolbarTitle: 'Dátum kiválasztása',
  dateTimePickerToolbarTitle: 'Dátum és idő kiválasztása',
  timePickerToolbarTitle: 'Idő kiválasztása',
  dateRangePickerToolbarTitle: 'Dátumhatárok kiválasztása',
  // Clock labels
  clockLabelText: (view, time, utils, formattedTime) => `${timeViews[view] ?? view} kiválasztása. ${!formattedTime && (time === null || !utils.isValid(time)) ? 'Nincs kiválasztva idő' : `A kiválasztott idő ${formattedTime ?? utils.format(time, 'fullTime')}`}`,
  hoursClockNumberText: hours => `${hours} ${timeViews.hours.toLowerCase()}`,
  minutesClockNumberText: minutes => `${minutes} ${timeViews.minutes.toLowerCase()}`,
  secondsClockNumberText: seconds => `${seconds}  ${timeViews.seconds.toLowerCase()}`,
  // Digital clock labels
  selectViewText: view => `${timeViews[view]} kiválasztása`,
  // Calendar labels
  calendarWeekNumberHeaderLabel: 'Hét',
  calendarWeekNumberHeaderText: '#',
  calendarWeekNumberAriaLabelText: weekNumber => `${weekNumber}. hét`,
  calendarWeekNumberText: weekNumber => `${weekNumber}`,
  // Open picker labels
  openDatePickerDialogue: (value, utils, formattedDate) => formattedDate || value !== null && utils.isValid(value) ? `Válasszon dátumot, a kiválasztott dátum: ${formattedDate ?? utils.format(value, 'fullDate')}` : 'Válasszon dátumot',
  openTimePickerDialogue: (value, utils, formattedTime) => formattedTime || value !== null && utils.isValid(value) ? `Válasszon időt, a kiválasztott idő: ${formattedTime ?? utils.format(value, 'fullTime')}` : 'Válasszon időt',
  fieldClearLabel: 'Tartalom ürítése',
  // Table labels
  timeTableLabel: 'válasszon időt',
  dateTableLabel: 'válasszon dátumot',
  // Field section placeholders
  fieldYearPlaceholder: params => 'É'.repeat(params.digitAmount),
  fieldMonthPlaceholder: params => params.contentType === 'letter' ? 'HHHH' : 'HH',
  fieldDayPlaceholder: () => 'NN',
  fieldWeekDayPlaceholder: params => params.contentType === 'letter' ? 'NNNN' : 'NN',
  fieldHoursPlaceholder: () => 'óó',
  fieldMinutesPlaceholder: () => 'pp',
  fieldSecondsPlaceholder: () => 'mm',
  fieldMeridiemPlaceholder: () => 'dd',
  // View names
  year: 'Év',
  month: 'Hónap',
  day: 'Nap',
  weekDay: 'Hétköznap',
  hours: timeViews.hours,
  minutes: timeViews.minutes,
  seconds: timeViews.seconds,
  meridiem: timeViews.meridiem,
  // Common
  empty: 'Üres'
};
export const huHU = getPickersLocalization(huHUPickers);