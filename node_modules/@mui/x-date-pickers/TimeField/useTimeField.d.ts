import { UseTimeFieldProps } from './TimeField.types';
import { PickerValidDate } from '../models';
export declare const useTimeField: <TDate extends PickerValidDate, TEnableAccessibleFieldDOMStructure extends boolean, TAllProps extends UseTimeFieldProps<TDate, TEnableAccessibleFieldDOMStructure>>(inProps: TAllProps) => import("../internals").UseFieldResponse<TEnableAccessibleFieldDOMStructure, Omit<TAllProps & Omit<UseTimeFieldProps<TDate, TEnableAccessibleFieldDOMStructure>, keyof import("../internals/hooks/defaultizedFieldProps").UseDefaultizedTimeFieldBaseProps> & Required<Pick<UseTimeFieldProps<TDate, TEnableAccessibleFieldDOMStructure>, keyof import("../internals/hooks/defaultizedFieldProps").UseDefaultizedTimeFieldBaseProps>>, "disabled" | "readOnly" | "value" | "defaultValue" | "onChange" | "onError" | "format" | "ampm" | "timezone" | "referenceDate" | "formatDensity" | "shouldRespectLeadingZeros" | "selectedSections" | "onSelectedSectionsChange" | "unstableFieldRef" | "enableAccessibleFieldDOMStructure" | keyof import("../internals").BaseTimeValidationProps | keyof import("../internals").TimeValidationProps<any> | "dateSeparator">>;
