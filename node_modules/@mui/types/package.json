{"name": "@mui/types", "version": "7.2.21", "private": false, "author": "MUI Team", "description": "Utility types for MUI.", "types": "./index.d.ts", "files": ["index.d.ts", "OverridableComponentAugmentation.ts"], "keywords": ["react", "react-component", "mui", "types"], "repository": {"type": "git", "url": "git+https://github.com/mui/material-ui.git", "directory": "packages/mui-types"}, "license": "MIT", "bugs": {"url": "https://github.com/mui/material-ui/issues"}, "homepage": "https://github.com/mui/material-ui/tree/master/packages/mui-types", "sideEffects": false, "publishConfig": {"access": "public", "directory": "build"}, "peerDependencies": {"@types/react": "^17.0.0 || ^18.0.0 || ^19.0.0"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}