"use strict";

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
Object.defineProperty(exports, "createFilterOptions", {
  enumerable: true,
  get: function () {
    return _useAutocomplete.createFilterOptions;
  }
});
Object.defineProperty(exports, "default", {
  enumerable: true,
  get: function () {
    return _useAutocomplete.default;
  }
});
var _useAutocomplete = _interopRequireWildcard(require("./useAutocomplete"));