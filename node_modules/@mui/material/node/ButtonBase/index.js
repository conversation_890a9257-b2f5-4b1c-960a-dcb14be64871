"use strict";

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
var _exportNames = {
  buttonBaseClasses: true,
  touchRippleClasses: true
};
Object.defineProperty(exports, "buttonBaseClasses", {
  enumerable: true,
  get: function () {
    return _buttonBaseClasses.default;
  }
});
Object.defineProperty(exports, "default", {
  enumerable: true,
  get: function () {
    return _ButtonBase.default;
  }
});
Object.defineProperty(exports, "touchRippleClasses", {
  enumerable: true,
  get: function () {
    return _touchRippleClasses.default;
  }
});
var _ButtonBase = _interopRequireDefault(require("./ButtonBase"));
var _buttonBaseClasses = _interopRequireWildcard(require("./buttonBaseClasses"));
Object.keys(_buttonBaseClasses).forEach(function (key) {
  if (key === "default" || key === "__esModule") return;
  if (Object.prototype.hasOwnProperty.call(_exportNames, key)) return;
  if (key in exports && exports[key] === _buttonBaseClasses[key]) return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function () {
      return _buttonBaseClasses[key];
    }
  });
});
var _touchRippleClasses = _interopRequireWildcard(require("./touchRippleClasses"));
Object.keys(_touchRippleClasses).forEach(function (key) {
  if (key === "default" || key === "__esModule") return;
  if (Object.prototype.hasOwnProperty.call(_exportNames, key)) return;
  if (key in exports && exports[key] === _touchRippleClasses[key]) return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function () {
      return _touchRippleClasses[key];
    }
  });
});