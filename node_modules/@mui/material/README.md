<!-- markdownlint-disable-next-line -->
<p align="center">
  <a href="https://mui.com/material-ui/" rel="noopener" target="_blank"><img width="150" height="133" src="https://mui.com/static/logo.svg" alt="Material UI logo"></a>
</p>

<h1 align="center">Material UI</h1>

Material UI is an open-source React component library that implements Google's [Material Design](https://m2.material.io/design/introduction/). It's comprehensive and can be used in production out of the box.

## Installation

Install the package in your project directory with:

<!-- #default-branch-switch -->

```bash
npm install @mui/material @emotion/react @emotion/styled
```

## Documentation

Visit [https://mui.com/material-ui/](https://mui.com/material-ui/) to view the full documentation.

## Questions

For how-to questions that don't involve making changes to the code base, please use [Stack Overflow](https://stackoverflow.com/questions/tagged/material-ui) instead of GitHub issues.
Use the "material-ui" tag on Stack Overflow to make it easier for the community to find your question.

## Examples

Our documentation features [a collection of example projects using Material UI](https://mui.com/material-ui/getting-started/example-projects/).

## Contributing

Read the [contributing guide](/CONTRIBUTING.md) to learn about our development process, how to propose bug fixes and improvements, and how to build and test your changes.

Contributing to Material UI is about more than just issues and pull requests!
There are many other ways to [support Material UI](https://mui.com/material-ui/getting-started/faq/#mui-is-awesome-how-can-i-support-the-project) beyond contributing to the code base.

## Changelog

The [changelog](https://github.com/mui/material-ui/releases) is regularly updated to reflect what's changed in each new release.

## Roadmap

Future plans and high-priority features and enhancements can be found in the [roadmap](https://mui.com/material-ui/discover-more/roadmap/).

## License

This project is licensed under the terms of the
[MIT license](/LICENSE).

## Security

For details of supported versions and contact details for reporting security issues, please refer to the [security policy](https://github.com/mui/material-ui/security/policy).
