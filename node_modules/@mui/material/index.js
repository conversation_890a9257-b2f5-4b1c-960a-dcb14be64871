/**
 * @mui/material v6.4.7
 *
 * @license MIT
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
/* eslint-disable import/export */
import * as colors from "./colors/index.js";
export { colors };
export * from "./styles/index.js";

// TODO remove, import directly from Base UI or create one folder per module
export * from "./utils/index.js";
export { default as Accordion } from "./Accordion/index.js";
export * from "./Accordion/index.js";
export { default as AccordionActions } from "./AccordionActions/index.js";
export * from "./AccordionActions/index.js";
export { default as AccordionDetails } from "./AccordionDetails/index.js";
export * from "./AccordionDetails/index.js";
export { default as AccordionSummary } from "./AccordionSummary/index.js";
export * from "./AccordionSummary/index.js";
export { default as Alert } from "./Alert/index.js";
export * from "./Alert/index.js";
export { default as AlertTitle } from "./AlertTitle/index.js";
export * from "./AlertTitle/index.js";
export { default as AppBar } from "./AppBar/index.js";
export * from "./AppBar/index.js";
export { default as Autocomplete } from "./Autocomplete/index.js";
export * from "./Autocomplete/index.js";
export { default as Avatar } from "./Avatar/index.js";
export * from "./Avatar/index.js";
export { default as AvatarGroup } from "./AvatarGroup/index.js";
export * from "./AvatarGroup/index.js";
export { default as Backdrop } from "./Backdrop/index.js";
export * from "./Backdrop/index.js";
export { default as Badge } from "./Badge/index.js";
export * from "./Badge/index.js";
export { default as BottomNavigation } from "./BottomNavigation/index.js";
export * from "./BottomNavigation/index.js";
export { default as BottomNavigationAction } from "./BottomNavigationAction/index.js";
export * from "./BottomNavigationAction/index.js";
export { default as Box } from "./Box/index.js";
export * from "./Box/index.js";
export { default as Breadcrumbs } from "./Breadcrumbs/index.js";
export * from "./Breadcrumbs/index.js";
export { default as Button } from "./Button/index.js";
export * from "./Button/index.js";
export { default as ButtonBase } from "./ButtonBase/index.js";
export * from "./ButtonBase/index.js";
export { default as ButtonGroup } from "./ButtonGroup/index.js";
export * from "./ButtonGroup/index.js";
export { default as Card } from "./Card/index.js";
export * from "./Card/index.js";
export { default as CardActionArea } from "./CardActionArea/index.js";
export * from "./CardActionArea/index.js";
export { default as CardActions } from "./CardActions/index.js";
export * from "./CardActions/index.js";
export { default as CardContent } from "./CardContent/index.js";
export * from "./CardContent/index.js";
export { default as CardHeader } from "./CardHeader/index.js";
export * from "./CardHeader/index.js";
export { default as CardMedia } from "./CardMedia/index.js";
export * from "./CardMedia/index.js";
export { default as Checkbox } from "./Checkbox/index.js";
export * from "./Checkbox/index.js";
export { default as Chip } from "./Chip/index.js";
export * from "./Chip/index.js";
export { default as CircularProgress } from "./CircularProgress/index.js";
export * from "./CircularProgress/index.js";
export { default as ClickAwayListener } from "./ClickAwayListener/index.js";
export * from "./ClickAwayListener/index.js";
export { default as Collapse } from "./Collapse/index.js";
export * from "./Collapse/index.js";
export { default as Container } from "./Container/index.js";
export * from "./Container/index.js";
export { default as CssBaseline } from "./CssBaseline/index.js";
export * from "./CssBaseline/index.js";
export { default as darkScrollbar } from "./darkScrollbar/index.js";
export * from "./darkScrollbar/index.js";
export { default as Dialog } from "./Dialog/index.js";
export * from "./Dialog/index.js";
export { default as DialogActions } from "./DialogActions/index.js";
export * from "./DialogActions/index.js";
export { default as DialogContent } from "./DialogContent/index.js";
export * from "./DialogContent/index.js";
export { default as DialogContentText } from "./DialogContentText/index.js";
export * from "./DialogContentText/index.js";
export { default as DialogTitle } from "./DialogTitle/index.js";
export * from "./DialogTitle/index.js";
export { default as Divider } from "./Divider/index.js";
export * from "./Divider/index.js";
export { default as Drawer } from "./Drawer/index.js";
export * from "./Drawer/index.js";
export { default as Fab } from "./Fab/index.js";
export * from "./Fab/index.js";
export { default as Fade } from "./Fade/index.js";
export * from "./Fade/index.js";
export { default as FilledInput } from "./FilledInput/index.js";
export * from "./FilledInput/index.js";
export { default as FormControl } from "./FormControl/index.js";
export * from "./FormControl/index.js";
export { default as FormControlLabel } from "./FormControlLabel/index.js";
export * from "./FormControlLabel/index.js";
export { default as FormGroup } from "./FormGroup/index.js";
export * from "./FormGroup/index.js";
export { default as FormHelperText } from "./FormHelperText/index.js";
export * from "./FormHelperText/index.js";
export { default as FormLabel } from "./FormLabel/index.js";
export * from "./FormLabel/index.js";
export { default as Grid } from "./Grid/index.js";
export { default as Grid2 } from "./Grid2/index.js";
export * from "./Grid2/index.js";
export { default as Grow } from "./Grow/index.js";
export * from "./Grow/index.js";
export { default as Hidden } from "./Hidden/index.js";
export * from "./Hidden/index.js";
export { default as Icon } from "./Icon/index.js";
export * from "./Icon/index.js";
export { default as IconButton } from "./IconButton/index.js";
export * from "./IconButton/index.js";
export { default as ImageList } from "./ImageList/index.js";
export * from "./ImageList/index.js";
export { default as ImageListItem } from "./ImageListItem/index.js";
export * from "./ImageListItem/index.js";
export { default as ImageListItemBar } from "./ImageListItemBar/index.js";
export * from "./ImageListItemBar/index.js";
export { default as Input } from "./Input/index.js";
export * from "./Input/index.js";
export { default as InputAdornment } from "./InputAdornment/index.js";
export * from "./InputAdornment/index.js";
export { default as InputBase } from "./InputBase/index.js";
export * from "./InputBase/index.js";
export { default as InputLabel } from "./InputLabel/index.js";
export * from "./InputLabel/index.js";
export { default as LinearProgress } from "./LinearProgress/index.js";
export * from "./LinearProgress/index.js";
export { default as Link } from "./Link/index.js";
export * from "./Link/index.js";
export { default as List } from "./List/index.js";
export * from "./List/index.js";
export { default as ListItem } from "./ListItem/index.js";
export * from "./ListItem/index.js";
export { default as ListItemAvatar } from "./ListItemAvatar/index.js";
export * from "./ListItemAvatar/index.js";
export { default as ListItemButton } from "./ListItemButton/index.js";
export * from "./ListItemButton/index.js";
export { default as ListItemIcon } from "./ListItemIcon/index.js";
export * from "./ListItemIcon/index.js";
export { default as ListItemSecondaryAction } from "./ListItemSecondaryAction/index.js";
export * from "./ListItemSecondaryAction/index.js";
export { default as ListItemText } from "./ListItemText/index.js";
export * from "./ListItemText/index.js";
export { default as ListSubheader } from "./ListSubheader/index.js";
export * from "./ListSubheader/index.js";
export { default as Menu } from "./Menu/index.js";
export * from "./Menu/index.js";
export { default as MenuItem } from "./MenuItem/index.js";
export * from "./MenuItem/index.js";
export { default as MenuList } from "./MenuList/index.js";
export * from "./MenuList/index.js";
export { default as MobileStepper } from "./MobileStepper/index.js";
export * from "./MobileStepper/index.js";
export { default as Modal } from "./Modal/index.js";
export * from "./Modal/index.js";
export { default as NativeSelect } from "./NativeSelect/index.js";
export * from "./NativeSelect/index.js";
export { default as NoSsr } from "./NoSsr/index.js";
export * from "./NoSsr/index.js";
export { default as OutlinedInput } from "./OutlinedInput/index.js";
export * from "./OutlinedInput/index.js";
export { default as Pagination } from "./Pagination/index.js";
export * from "./Pagination/index.js";
export { default as PaginationItem } from "./PaginationItem/index.js";
export * from "./PaginationItem/index.js";
export { default as Paper } from "./Paper/index.js";
export * from "./Paper/index.js";
export { default as Popover } from "./Popover/index.js";
export * from "./Popover/index.js";
export { default as Popper } from "./Popper/index.js";
export * from "./Popper/index.js";
export { default as Portal } from "./Portal/index.js";
export * from "./Portal/index.js";
export { default as Radio } from "./Radio/index.js";
export * from "./Radio/index.js";
export { default as RadioGroup } from "./RadioGroup/index.js";
export * from "./RadioGroup/index.js";
export { default as Rating } from "./Rating/index.js";
export * from "./Rating/index.js";
export { default as ScopedCssBaseline } from "./ScopedCssBaseline/index.js";
export * from "./ScopedCssBaseline/index.js";
export { default as Select } from "./Select/index.js";
export * from "./Select/index.js";
export { default as Skeleton } from "./Skeleton/index.js";
export * from "./Skeleton/index.js";
export { default as Slide } from "./Slide/index.js";
export * from "./Slide/index.js";
export { default as Slider } from "./Slider/index.js";
export * from "./Slider/index.js";
export { default as Snackbar } from "./Snackbar/index.js";
export * from "./Snackbar/index.js";
export { default as SnackbarContent } from "./SnackbarContent/index.js";
export * from "./SnackbarContent/index.js";
export { default as SpeedDial } from "./SpeedDial/index.js";
export * from "./SpeedDial/index.js";
export { default as SpeedDialAction } from "./SpeedDialAction/index.js";
export * from "./SpeedDialAction/index.js";
export { default as SpeedDialIcon } from "./SpeedDialIcon/index.js";
export * from "./SpeedDialIcon/index.js";
export { default as Stack } from "./Stack/index.js";
export * from "./Stack/index.js";
export { default as Step } from "./Step/index.js";
export * from "./Step/index.js";
export { default as StepButton } from "./StepButton/index.js";
export * from "./StepButton/index.js";
export { default as StepConnector } from "./StepConnector/index.js";
export * from "./StepConnector/index.js";
export { default as StepContent } from "./StepContent/index.js";
export * from "./StepContent/index.js";
export { default as StepIcon } from "./StepIcon/index.js";
export * from "./StepIcon/index.js";
export { default as StepLabel } from "./StepLabel/index.js";
export * from "./StepLabel/index.js";
export { default as Stepper } from "./Stepper/index.js";
export * from "./Stepper/index.js";
export { default as SvgIcon } from "./SvgIcon/index.js";
export * from "./SvgIcon/index.js";
export { default as SwipeableDrawer } from "./SwipeableDrawer/index.js";
export * from "./SwipeableDrawer/index.js";
export { default as Switch } from "./Switch/index.js";
export * from "./Switch/index.js";
export { default as Tab } from "./Tab/index.js";
export * from "./Tab/index.js";
export { default as Table } from "./Table/index.js";
export * from "./Table/index.js";
export { default as TableBody } from "./TableBody/index.js";
export * from "./TableBody/index.js";
export { default as TableCell } from "./TableCell/index.js";
export * from "./TableCell/index.js";
export { default as TableContainer } from "./TableContainer/index.js";
export * from "./TableContainer/index.js";
export { default as TableFooter } from "./TableFooter/index.js";
export * from "./TableFooter/index.js";
export { default as TableHead } from "./TableHead/index.js";
export * from "./TableHead/index.js";
export { default as TablePagination } from "./TablePagination/index.js";
export * from "./TablePagination/index.js";
export { default as TableRow } from "./TableRow/index.js";
export * from "./TableRow/index.js";
export { default as TableSortLabel } from "./TableSortLabel/index.js";
export * from "./TableSortLabel/index.js";
export { default as Tabs } from "./Tabs/index.js";
export * from "./Tabs/index.js";
export { default as TabScrollButton } from "./TabScrollButton/index.js";
export * from "./TabScrollButton/index.js";
export { default as TextField } from "./TextField/index.js";
export * from "./TextField/index.js";
export { default as TextareaAutosize } from "./TextareaAutosize/index.js";
export * from "./TextareaAutosize/index.js";
export { default as ToggleButton } from "./ToggleButton/index.js";
export * from "./ToggleButton/index.js";
export { default as ToggleButtonGroup } from "./ToggleButtonGroup/index.js";
export * from "./ToggleButtonGroup/index.js";
export { default as Toolbar } from "./Toolbar/index.js";
export * from "./Toolbar/index.js";
export { default as Tooltip } from "./Tooltip/index.js";
export * from "./Tooltip/index.js";
export { default as Typography } from "./Typography/index.js";
export * from "./Typography/index.js";
export { default as useMediaQuery } from "./useMediaQuery/index.js";
export * from "./useMediaQuery/index.js";
export { default as usePagination } from "./usePagination/index.js";
export * from "./usePagination/index.js";
export { default as useScrollTrigger } from "./useScrollTrigger/index.js";
export * from "./useScrollTrigger/index.js";
export { default as Zoom } from "./Zoom/index.js";
export * from "./Zoom/index.js";

// createFilterOptions is exported from Autocomplete
export { default as useAutocomplete } from "./useAutocomplete/index.js";
export { default as GlobalStyles } from "./GlobalStyles/index.js";
export * from "./GlobalStyles/index.js";
export { unstable_composeClasses } from '@mui/utils';
export { default as generateUtilityClass } from "./generateUtilityClass/index.js";
export * from "./generateUtilityClass/index.js";
export { default as generateUtilityClasses } from "./generateUtilityClasses/index.js";
export { default as Unstable_TrapFocus } from "./Unstable_TrapFocus/index.js";
export * from "./version/index.js";