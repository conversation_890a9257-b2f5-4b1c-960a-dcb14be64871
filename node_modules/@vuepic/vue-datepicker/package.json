{"name": "@vuepic/vue-datepicker", "version": "11.0.1", "description": "Datepicker component for Vue 3", "author": "<PERSON><PERSON><PERSON>", "private": false, "license": "MIT", "homepage": "https://vue3datepicker.com", "types": "index.d.ts", "type": "module", "main": "dist/vue-datepicker.umd.cjs", "module": "dist/vue-datepicker.js", "browser": "dist/vue-datepicker.js", "unpkg": "dist/vue-datepicker.iife.js", "style": "dist/main.css", "sass": "src/VueDatePicker/style/main.scss", "files": ["dist/*", "index.d.ts", "LICENSE", "README.md", "CHANGELOG.md"], "exports": {".": {"import": "./dist/vue-datepicker.js", "require": "./dist/vue-datepicker.umd.cjs", "types": "./index.d.ts"}, "./dist/main.css": {"import": "./dist/main.css", "require": "./dist/main.css", "default": "./dist/main.css"}}, "scripts": {"dev": "vite", "build": "run-s clean typecheck build:lib build:css", "clean": "rimraf ./dist", "build:lib": "run-s build:es build:browser", "typecheck": "vue-tsc --build", "build:es": "cross-env NODE_ENV=production vite build --mode production", "build:browser": "cross-env NODE_ENV=production vite -f iife build --mode production", "build:css": "cross-env NODE_ENV=production node_modules/.bin/sass src/VueDatePicker/style/main.scss dist/main.css --style compressed", "test": "cross-env NODE_ENV=test vitest", "test:coverage": "cross-env NODE_ENV=test vitest --environment jsdom run --coverage", "lint": "run-s lint:style lint:lib", "lint:style": "stylelint --fix \"src/**/*.scss\"", "lint:lib": "eslint --fix \"src/**\"", "format": "prettier --write src/"}, "devDependencies": {"@tsconfig/node22": "^22.0.0", "@types/jsdom": "^21.1.7", "@types/minimist": "^1.2.5", "@types/node": "^22.10.5", "@vitejs/plugin-vue": "^5.2.1", "@vitest/coverage-v8": "^2.1.8", "@vue/eslint-config-prettier": "^10.1.0", "@vue/eslint-config-typescript": "^14.2.0", "@vue/test-utils": "^2.4.6", "@vue/tsconfig": "^0.7.0", "c8": "^10.1.3", "cross-env": "^7.0.3", "eslint": "^9.17.0", "eslint-plugin-vue": "^9.32.0", "jsdom": "^26.0.0", "minimist": "^1.2.8", "npm-run-all2": "^7.0.2", "postcss": "^8.4.49", "prettier": "^3.4.2", "rimraf": "^6.0.1", "sass": "^1.83.1", "sass-loader": "^16.0.4", "stylelint": "^16.12.0", "stylelint-config-standard-scss": "^14.0.0", "typescript": "^5.7.3", "vite": "^6.0.7", "vitest": "^2.1.8", "vue": "^3.5.13", "vue-tsc": "^2.2.0"}, "dependencies": {"date-fns": "^4.1.0"}, "peerDependencies": {"vue": ">=3.3.0"}, "engines": {"node": ">=18.12.0"}, "repository": {"type": "git", "url": "git+https://github.com/Vuepic/vue-datepicker.git"}, "bugs": {"url": "https://github.com/Vuepic/vue-datepicker/issues"}, "keywords": ["vue-datepicker", "vue", "datepicker", "date", "vue3-datepicker", "datetimepicker", "daterangepicker"], "browserslist": ["> 1%", "last 2 versions", "not dead"]}