(function(e,i){typeof exports=="object"&&typeof module<"u"?module.exports=i(require("vue"),require("date-fns")):typeof define=="function"&&define.amd?define(["vue","date-fns"],i):(e=typeof globalThis<"u"?globalThis:e||self,e.VueDatePicker=i(e.Vue,e.dateFns))})(this,function(e,i){"use strict";function Fe(){const t=e.useAttrs();return e.openBlock(),e.createElementBlock("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 32 32",fill:"currentColor","aria-hidden":"true",class:"dp__icon",role:"img",...t},[e.createElementVNode("path",{d:"M29.333 8c0-2.208-1.792-4-4-4h-18.667c-2.208 0-4 1.792-4 4v18.667c0 2.208 1.792 4 4 4h18.667c2.208 0 4-1.792 4-4v-18.667zM26.667 8v18.667c0 0.736-0.597 1.333-1.333 1.333 0 0-18.667 0-18.667 0-0.736 0-1.333-0.597-1.333-1.333 0 0 0-18.667 0-18.667 0-0.736 0.597-1.333 1.333-1.333 0 0 18.667 0 18.667 0 0.736 0 1.333 0.597 1.333 1.333z"}),e.createElementVNode("path",{d:"M20 2.667v5.333c0 0.736 0.597 1.333 1.333 1.333s1.333-0.597 1.333-1.333v-5.333c0-0.736-0.597-1.333-1.333-1.333s-1.333 0.597-1.333 1.333z"}),e.createElementVNode("path",{d:"M9.333 2.667v5.333c0 0.736 0.597 1.333 1.333 1.333s1.333-0.597 1.333-1.333v-5.333c0-0.736-0.597-1.333-1.333-1.333s-1.333 0.597-1.333 1.333z"}),e.createElementVNode("path",{d:"M4 14.667h24c0.736 0 1.333-0.597 1.333-1.333s-0.597-1.333-1.333-1.333h-24c-0.736 0-1.333 0.597-1.333 1.333s0.597 1.333 1.333 1.333z"})])}Fe.compatConfig={MODE:3};function on(){return e.openBlock(),e.createElementBlock("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 32 32",fill:"currentColor","aria-hidden":"true",class:"dp__icon",role:"img"},[e.createElementVNode("path",{d:"M23.057 7.057l-16 16c-0.52 0.52-0.52 1.365 0 1.885s1.365 0.52 1.885 0l16-16c0.52-0.52 0.52-1.365 0-1.885s-1.365-0.52-1.885 0z"}),e.createElementVNode("path",{d:"M7.057 8.943l16 16c0.52 0.52 1.365 0.52 1.885 0s0.52-1.365 0-1.885l-16-16c-0.52-0.52-1.365-0.52-1.885 0s-0.52 1.365 0 1.885z"})])}on.compatConfig={MODE:3};function Dt(){return e.openBlock(),e.createElementBlock("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 32 32",fill:"currentColor","aria-hidden":"true",class:"dp__icon",role:"img"},[e.createElementVNode("path",{d:"M20.943 23.057l-7.057-7.057c0 0 7.057-7.057 7.057-7.057 0.52-0.52 0.52-1.365 0-1.885s-1.365-0.52-1.885 0l-8 8c-0.521 0.521-0.521 1.365 0 1.885l8 8c0.52 0.52 1.365 0.52 1.885 0s0.52-1.365 0-1.885z"})])}Dt.compatConfig={MODE:3};function St(){return e.openBlock(),e.createElementBlock("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 32 32",fill:"currentColor","aria-hidden":"true",class:"dp__icon",role:"img"},[e.createElementVNode("path",{d:"M12.943 24.943l8-8c0.521-0.521 0.521-1.365 0-1.885l-8-8c-0.52-0.52-1.365-0.52-1.885 0s-0.52 1.365 0 1.885l7.057 7.057c0 0-7.057 7.057-7.057 7.057-0.52 0.52-0.52 1.365 0 1.885s1.365 0.52 1.885 0z"})])}St.compatConfig={MODE:3};function Ct(){return e.openBlock(),e.createElementBlock("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 32 32",fill:"currentColor","aria-hidden":"true",class:"dp__icon",role:"img"},[e.createElementVNode("path",{d:"M16 1.333c-8.095 0-14.667 6.572-14.667 14.667s6.572 14.667 14.667 14.667c8.095 0 14.667-6.572 14.667-14.667s-6.572-14.667-14.667-14.667zM16 4c6.623 0 12 5.377 12 12s-5.377 12-12 12c-6.623 0-12-5.377-12-12s5.377-12 12-12z"}),e.createElementVNode("path",{d:"M14.667 8v8c0 0.505 0.285 0.967 0.737 1.193l5.333 2.667c0.658 0.329 1.46 0.062 1.789-0.596s0.062-1.46-0.596-1.789l-4.596-2.298c0 0 0-7.176 0-7.176 0-0.736-0.597-1.333-1.333-1.333s-1.333 0.597-1.333 1.333z"})])}Ct.compatConfig={MODE:3};function Bt(){return e.openBlock(),e.createElementBlock("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 32 32",fill:"currentColor","aria-hidden":"true",class:"dp__icon",role:"img"},[e.createElementVNode("path",{d:"M24.943 19.057l-8-8c-0.521-0.521-1.365-0.521-1.885 0l-8 8c-0.52 0.52-0.52 1.365 0 1.885s1.365 0.52 1.885 0l7.057-7.057c0 0 7.057 7.057 7.057 7.057 0.52 0.52 1.365 0.52 1.885 0s0.52-1.365 0-1.885z"})])}Bt.compatConfig={MODE:3};function Tt(){return e.openBlock(),e.createElementBlock("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 32 32",fill:"currentColor","aria-hidden":"true",class:"dp__icon",role:"img"},[e.createElementVNode("path",{d:"M7.057 12.943l8 8c0.521 0.521 1.365 0.521 1.885 0l8-8c0.52-0.52 0.52-1.365 0-1.885s-1.365-0.52-1.885 0l-7.057 7.057c0 0-7.057-7.057-7.057-7.057-0.52-0.52-1.365-0.52-1.885 0s-0.52 1.365 0 1.885z"})])}Tt.compatConfig={MODE:3};const Ee=(t,n)=>n?new Date(t.toLocaleString("en-US",{timeZone:n})):new Date(t),$t=(t,n,o)=>{const a=At(t,n,o);return a||j()},zn=(t,n,o)=>{const a=n.dateInTz?Ee(new Date(t),n.dateInTz):j(t);return o?Be(a,!0):a},At=(t,n,o)=>{if(!t)return null;const a=o?Be(j(t),!0):j(t);return n?n.exactMatch?zn(t,n,o):Ee(a,n.timezone):a},Ln=t=>{const o=new Date(t.getFullYear(),0,1).getTimezoneOffset();return t.getTimezoneOffset()<o},Hn=(t,n)=>{if(!t)return 0;const o=new Date,a=new Date(o.toLocaleString("en-US",{timeZone:"UTC"})),r=new Date(o.toLocaleString("en-US",{timeZone:t})),d=(Ln(n??r)?r:n??r).getTimezoneOffset()/60;return(+a-+r)/(1e3*60*60)-d};var Ve=(t=>(t.month="month",t.year="year",t))(Ve||{}),_e=(t=>(t.top="top",t.bottom="bottom",t))(_e||{}),Xe=(t=>(t.header="header",t.calendar="calendar",t.timePicker="timePicker",t))(Xe||{}),$e=(t=>(t.month="month",t.year="year",t.calendar="calendar",t.time="time",t.minutes="minutes",t.hours="hours",t.seconds="seconds",t))($e||{});const Un=["timestamp","date","iso"];var Pe=(t=>(t.up="up",t.down="down",t.left="left",t.right="right",t))(Pe||{}),he=(t=>(t.arrowUp="ArrowUp",t.arrowDown="ArrowDown",t.arrowLeft="ArrowLeft",t.arrowRight="ArrowRight",t.enter="Enter",t.space=" ",t.esc="Escape",t.tab="Tab",t.home="Home",t.end="End",t.pageUp="PageUp",t.pageDown="PageDown",t))(he||{}),et=(t=>(t.MONTH_AND_YEAR="MM-yyyy",t.YEAR="yyyy",t.DATE="dd-MM-yyyy",t))(et||{});function sn(t){return n=>new Intl.DateTimeFormat(t,{weekday:"short",timeZone:"UTC"}).format(new Date(`2017-01-0${n}T00:00:00+00:00`)).slice(0,2)}function Wn(t){return n=>i.format(Ee(new Date(`2017-01-0${n}T00:00:00+00:00`),"UTC"),"EEEEEE",{locale:t})}const jn=(t,n,o)=>{const a=[1,2,3,4,5,6,7];let r;if(t!==null)try{r=a.map(Wn(t))}catch{r=a.map(sn(n))}else r=a.map(sn(n));const u=r.slice(0,o),d=r.slice(o+1,r.length);return[r[o]].concat(...d).concat(...u)},Pt=(t,n,o)=>{const a=[];for(let r=+t[0];r<=+t[1];r++)a.push({value:+r,text:pn(r,n)});return o?a.reverse():a},un=(t,n,o)=>{const a=[1,2,3,4,5,6,7,8,9,10,11,12].map(u=>{const d=u<10?`0${u}`:u;return new Date(`2017-${d}-01T00:00:00+00:00`)});if(t!==null)try{const u=o==="long"?"LLLL":"LLL";return a.map((d,h)=>{const c=i.format(Ee(d,"UTC"),u,{locale:t});return{text:c.charAt(0).toUpperCase()+c.substring(1),value:h}})}catch{}const r=new Intl.DateTimeFormat(n,{month:o,timeZone:"UTC"});return a.map((u,d)=>{const h=r.format(u);return{text:h.charAt(0).toUpperCase()+h.substring(1),value:d}})},Kn=t=>[12,1,2,3,4,5,6,7,8,9,10,11,12,1,2,3,4,5,6,7,8,9,10,11][t],Se=t=>{const n=e.unref(t);return n!=null&&n.$el?n==null?void 0:n.$el:n},xn=t=>({type:"dot",...t??{}}),cn=t=>Array.isArray(t)?!!t[0]&&!!t[1]:!1,Rt={prop:t=>`"${t}" prop must be enabled!`,dateArr:t=>`You need to use array as "model-value" binding in order to support "${t}"`},De=t=>t,dn=t=>t===0?t:!t||isNaN(+t)?null:+t,fn=t=>t===null,mn=t=>{if(t)return[...t.querySelectorAll("input, button, select, textarea, a[href]")][0]},qn=t=>{const n=[],o=a=>a.filter(r=>r);for(let a=0;a<t.length;a+=3){const r=[t[a],t[a+1],t[a+2]];n.push(o(r))}return n},ot=(t,n,o)=>{const a=o!=null,r=n!=null;if(!a&&!r)return!1;const u=+o,d=+n;return a&&r?+t>u||+t<d:a?+t>u:r?+t<d:!1},tt=(t,n)=>qn(t).map(o=>o.map(a=>{const{active:r,disabled:u,isBetween:d,highlighted:h}=n(a);return{...a,active:r,disabled:u,className:{dp__overlay_cell_active:r,dp__overlay_cell:!r,dp__overlay_cell_disabled:u,dp__overlay_cell_pad:!0,dp__overlay_cell_active_disabled:u&&r,dp__cell_in_between:d,"dp--highlighted":h}}})),je=(t,n,o=!1)=>{t&&n.allowStopPropagation&&(o&&t.stopImmediatePropagation(),t.stopPropagation())},Gn=()=>["a[href]","area[href]","input:not([disabled]):not([type='hidden'])","select:not([disabled])","textarea:not([disabled])","button:not([disabled])","[tabindex]:not([tabindex='-1'])","[data-datepicker-instance]"].join(", ");function Qn(t,n){let o=[...document.querySelectorAll(Gn())];o=o.filter(r=>!t.contains(r)||r.hasAttribute("data-datepicker-instance"));const a=o.indexOf(t);if(a>=0&&(n?a-1>=0:a+1<=o.length))return o[a+(n?-1:1)]}const Nt=(t,n)=>t==null?void 0:t.querySelector(`[data-dp-element="${n}"]`),pn=(t,n)=>new Intl.NumberFormat(n,{useGrouping:!1,style:"decimal"}).format(t),Et=(t,n)=>i.format(t,n??et.DATE),Ot=t=>Array.isArray(t),pt=(t,n,o)=>n.get(Et(t,o)),Xn=(t,n)=>t?n?n instanceof Map?!!pt(t,n):n(j(t)):!1:!0,Re=(t,n,o=!1,a)=>{if(t.key===he.enter||t.key===he.space)return o&&t.preventDefault(),n();if(a)return a(t)},Jn=()=>"ontouchstart"in window||navigator.maxTouchPoints>0,Zn=(t,n)=>t?et.MONTH_AND_YEAR:n?et.YEAR:et.DATE,gn=t=>t<10?`0${t}`:t,yn=(t,n,o,a,r,u)=>{const d=i.parse(t,n.slice(0,t.length),new Date,{locale:u});return i.isValid(d)&&i.isDate(d)?a||r?d:i.set(d,{hours:+o.hours,minutes:+(o==null?void 0:o.minutes),seconds:+(o==null?void 0:o.seconds),milliseconds:0}):null},Fn=(t,n,o,a,r,u)=>{const d=Array.isArray(o)?o[0]:o;if(typeof n=="string")return yn(t,n,d,a,r,u);if(Array.isArray(n)){let h=null;for(const c of n)if(h=yn(t,c,d,a,r,u),h)break;return h}return typeof n=="function"?n(t):null},j=t=>t?new Date(t):new Date,ea=(t,n,o)=>{if(n){const r=(t.getMonth()+1).toString().padStart(2,"0"),u=t.getDate().toString().padStart(2,"0"),d=t.getHours().toString().padStart(2,"0"),h=t.getMinutes().toString().padStart(2,"0"),c=o?t.getSeconds().toString().padStart(2,"0"):"00";return`${t.getFullYear()}-${r}-${u}T${d}:${h}:${c}.000Z`}const a=Date.UTC(t.getUTCFullYear(),t.getUTCMonth(),t.getUTCDate(),t.getUTCHours(),t.getUTCMinutes(),t.getUTCSeconds());return new Date(a).toISOString()},Be=(t,n)=>{const o=j(JSON.parse(JSON.stringify(t))),a=i.set(o,{hours:0,minutes:0,seconds:0,milliseconds:0});return n?i.startOfMonth(a):a},Ke=(t,n,o,a)=>{let r=t?j(t):j();return(n||n===0)&&(r=i.setHours(r,+n)),(o||o===0)&&(r=i.setMinutes(r,+o)),(a||a===0)&&(r=i.setSeconds(r,+a)),i.setMilliseconds(r,0)},ve=(t,n)=>!t||!n?!1:i.isBefore(Be(t),Be(n)),pe=(t,n)=>!t||!n?!1:i.isEqual(Be(t),Be(n)),we=(t,n)=>!t||!n?!1:i.isAfter(Be(t),Be(n)),st=(t,n,o)=>t!=null&&t[0]&&(t!=null&&t[1])?we(o,t[0])&&ve(o,t[1]):t!=null&&t[0]&&n?we(o,t[0])&&ve(o,n)||ve(o,t[0])&&we(o,n):!1,Ie=t=>{const n=i.set(new Date(t),{date:1});return Be(n)},Yt=(t,n,o)=>n&&(o||o===0)?Object.fromEntries(["hours","minutes","seconds"].map(a=>a===n?[a,o]:[a,isNaN(+t[a])?void 0:+t[a]])):{hours:isNaN(+t.hours)?void 0:+t.hours,minutes:isNaN(+t.minutes)?void 0:+t.minutes,seconds:isNaN(+t.seconds)?void 0:+t.seconds},Je=t=>({hours:i.getHours(t),minutes:i.getMinutes(t),seconds:i.getSeconds(t)}),hn=(t,n)=>{if(n){const o=i.getYear(j(n));if(o>t)return 12;if(o===t)return i.getMonth(j(n))}},kn=(t,n)=>{if(n){const o=i.getYear(j(n));return o<t?-1:o===t?i.getMonth(j(n)):void 0}},nt=t=>{if(t)return i.getYear(j(t))},bn=(t,n)=>{const o=we(t,n)?n:t,a=we(n,t)?n:t;return i.eachDayOfInterval({start:o,end:a})},ta=t=>{const n=i.addMonths(t,1);return{month:i.getMonth(n),year:i.getYear(n)}},He=(t,n)=>{const o=i.startOfWeek(t,{weekStartsOn:+n}),a=i.endOfWeek(t,{weekStartsOn:+n});return[o,a]},vn=(t,n)=>{const o={hours:i.getHours(j()),minutes:i.getMinutes(j()),seconds:n?i.getSeconds(j()):0};return Object.assign(o,t)},xe=(t,n,o)=>[i.set(j(t),{date:1}),i.set(j(),{month:n,year:o,date:1})],Ue=(t,n,o)=>{let a=t?j(t):j();return(n||n===0)&&(a=i.setMonth(a,n)),o&&(a=i.setYear(a,o)),a},wn=(t,n,o,a,r)=>{if(!a||r&&!n||!r&&!o)return!1;const u=r?i.addMonths(t,1):i.subMonths(t,1),d=[i.getMonth(u),i.getYear(u)];return r?!aa(...d,n):!na(...d,o)},na=(t,n,o)=>ve(...xe(o,t,n))||pe(...xe(o,t,n)),aa=(t,n,o)=>we(...xe(o,t,n))||pe(...xe(o,t,n)),Mn=(t,n,o,a,r,u,d)=>{if(typeof n=="function"&&!d)return n(t);const h=o?{locale:o}:void 0;return Array.isArray(t)?`${i.format(t[0],u,h)}${r&&!t[1]?"":a}${t[1]?i.format(t[1],u,h):""}`:i.format(t,u,h)},at=t=>{if(t)return null;throw new Error(Rt.prop("partial-range"))},gt=(t,n)=>{if(n)return t();throw new Error(Rt.prop("range"))},Vt=t=>Array.isArray(t)?i.isValid(t[0])&&(t[1]?i.isValid(t[1]):!0):t?i.isValid(t):!1,ra=(t,n)=>i.set(n??j(),{hours:+t.hours||0,minutes:+t.minutes||0,seconds:+t.seconds||0}),_t=(t,n,o,a)=>{if(!t)return!0;if(a){const r=o==="max"?i.isBefore(t,n):i.isAfter(t,n),u={seconds:0,milliseconds:0};return r||i.isEqual(i.set(t,u),i.set(n,u))}return o==="max"?t.getTime()<=n.getTime():t.getTime()>=n.getTime()},It=(t,n,o)=>t?ra(t,n):j(o??n),Dn=(t,n,o,a,r)=>{if(Array.isArray(a)){const d=It(t,a[0],n),h=It(t,a[1],n);return _t(a[0],d,o,!!n)&&_t(a[1],h,o,!!n)&&r}const u=It(t,a,n);return _t(a,u,o,!!n)&&r},zt=t=>i.set(j(),Je(t)),la=(t,n,o)=>{if(t instanceof Map){const a=`${gn(o+1)}-${n}`;return t.size?t.has(a):!1}return!1},oa=(t,n,o)=>{if(t instanceof Map){const a=`${gn(o+1)}-${n}`;return t.size?t.has(a):!0}return!0},Sn=(t,n,o)=>typeof t=="function"?t({month:n,year:o}):!!t.months.find(a=>a.month===n&&a.year===o),Lt=(t,n)=>typeof t=="function"?t(n):t.years.includes(n),Ht=t=>`dp-${i.format(t,"yyyy-MM-dd")}`,Cn=(t,n)=>{const o=i.subDays(Be(n),t),a=i.addDays(Be(n),t);return{before:o,after:a}},it=e.reactive({menuFocused:!1,shiftKeyInMenu:!1}),Bn=()=>{const t=a=>{it.menuFocused=a},n=a=>{it.shiftKeyInMenu!==a&&(it.shiftKeyInMenu=a)};return{control:e.computed(()=>({shiftKeyInMenu:it.shiftKeyInMenu,menuFocused:it.menuFocused})),setMenuFocused:t,setShiftKey:n}},ke=e.reactive({monthYear:[],calendar:[],time:[],actionRow:[],selectionGrid:[],timePicker:{0:[],1:[]},monthPicker:[]}),Ut=e.ref(null),yt=e.ref(!1),Wt=e.ref(!1),jt=e.ref(!1),Kt=e.ref(!1),Ae=e.ref(0),Me=e.ref(0),qe=()=>{const t=e.computed(()=>yt.value?[...ke.selectionGrid,ke.actionRow].filter(m=>m.length):Wt.value?[...ke.timePicker[0],...ke.timePicker[1],Kt.value?[]:[Ut.value],ke.actionRow].filter(m=>m.length):jt.value?[...ke.monthPicker,ke.actionRow]:[ke.monthYear,...ke.calendar,ke.time,ke.actionRow].filter(m=>m.length)),n=m=>{Ae.value=m?Ae.value+1:Ae.value-1;let Y=null;t.value[Me.value]&&(Y=t.value[Me.value][Ae.value]),!Y&&t.value[Me.value+(m?1:-1)]?(Me.value=Me.value+(m?1:-1),Ae.value=m?0:t.value[Me.value].length-1):Y||(Ae.value=m?Ae.value-1:Ae.value+1)},o=m=>{if(Me.value===0&&!m||Me.value===t.value.length&&m)return;Me.value=m?Me.value+1:Me.value-1,t.value[Me.value]?t.value[Me.value]&&!t.value[Me.value][Ae.value]&&Ae.value!==0&&(Ae.value=t.value[Me.value].length-1):Me.value=m?Me.value-1:Me.value+1},a=m=>{let Y=null;t.value[Me.value]&&(Y=t.value[Me.value][Ae.value]),Y?Y.focus({preventScroll:!yt.value}):Ae.value=m?Ae.value-1:Ae.value+1},r=()=>{n(!0),a(!0)},u=()=>{n(!1),a(!1)},d=()=>{o(!1),a(!0)},h=()=>{o(!0),a(!0)},c=(m,Y)=>{ke[Y]=m},E=(m,Y)=>{ke[Y]=m},f=()=>{Ae.value=0,Me.value=0};return{buildMatrix:c,buildMultiLevelMatrix:E,setTimePickerBackRef:m=>{Ut.value=m},setSelectionGrid:m=>{yt.value=m,f(),m||(ke.selectionGrid=[])},setTimePicker:(m,Y=!1)=>{Wt.value=m,Kt.value=Y,f(),m||(ke.timePicker[0]=[],ke.timePicker[1]=[])},setTimePickerElements:(m,Y=0)=>{ke.timePicker[Y]=m},arrowRight:r,arrowLeft:u,arrowUp:d,arrowDown:h,clearArrowNav:()=>{ke.monthYear=[],ke.calendar=[],ke.time=[],ke.actionRow=[],ke.selectionGrid=[],ke.timePicker[0]=[],ke.timePicker[1]=[],yt.value=!1,Wt.value=!1,Kt.value=!1,jt.value=!1,f(),Ut.value=null},setMonthPicker:m=>{jt.value=m,f()},refSets:ke}},Tn=t=>({menuAppearTop:"dp-menu-appear-top",menuAppearBottom:"dp-menu-appear-bottom",open:"dp-slide-down",close:"dp-slide-up",next:"calendar-next",previous:"calendar-prev",vNext:"dp-slide-up",vPrevious:"dp-slide-down",...t??{}}),sa=t=>({toggleOverlay:"Toggle overlay",menu:"Datepicker menu",input:"Datepicker input",openTimePicker:"Open time picker",closeTimePicker:"Close time Picker",incrementValue:n=>`Increment ${n}`,decrementValue:n=>`Decrement ${n}`,openTpOverlay:n=>`Open ${n} overlay`,amPmButton:"Switch AM/PM mode",openYearsOverlay:"Open years overlay",openMonthsOverlay:"Open months overlay",nextMonth:"Next month",prevMonth:"Previous month",nextYear:"Next year",prevYear:"Previous year",day:void 0,weekDay:void 0,clearInput:"Clear value",calendarIcon:"Calendar icon",timePicker:"Time picker",monthPicker:n=>`Month picker${n?" overlay":""}`,yearPicker:n=>`Year picker${n?" overlay":""}`,timeOverlay:n=>`${n} overlay`,...t??{}}),$n=t=>t?typeof t=="boolean"?t?2:0:+t>=2?+t:2:0,ia=t=>{const n=typeof t=="object"&&t,o={static:!0,solo:!1};if(!t)return{...o,count:$n(!1)};const a=n?t:{},r=n?a.count??!0:t,u=$n(r);return Object.assign(o,a,{count:u})},ua=(t,n,o)=>t||(typeof o=="string"?o:n),ca=t=>typeof t=="boolean"?t?Tn({}):!1:Tn(t),da=t=>{const n={enterSubmit:!0,tabSubmit:!0,openMenu:"open",selectOnFocus:!1,rangeSeparator:" - ",escClose:!0};return typeof t=="object"?{...n,...t??{},enabled:!0}:{...n,enabled:t}},fa=t=>({months:[],years:[],times:{hours:[],minutes:[],seconds:[]},...t??{}}),ma=t=>({showSelect:!0,showCancel:!0,showNow:!1,showPreview:!0,...t??{}}),pa=t=>{const n={input:!1};return typeof t=="object"?{...n,...t??{},enabled:!0}:{enabled:t,...n}},ga=t=>({...{allowStopPropagation:!0,closeOnScroll:!1,modeHeight:255,allowPreventDefault:!1,closeOnClearValue:!0,closeOnAutoApply:!0,noSwipe:!1,keepActionRow:!1,onClickOutside:void 0,tabOutClosesMenu:!0,arrowLeft:void 0,keepViewOnOffsetClick:!1,timeArrowHoldThreshold:0,shadowDom:!1,mobileBreakpoint:600,setDateOnMenuClose:!1},...t??{}}),ya=t=>{const n={dates:Array.isArray(t)?t.map(o=>j(o)):[],years:[],months:[],quarters:[],weeks:[],weekdays:[],options:{highlightDisabled:!1}};return typeof t=="function"?t:{...n,...t??{}}},ha=t=>typeof t=="object"?{type:(t==null?void 0:t.type)??"local",hideOnOffsetDates:(t==null?void 0:t.hideOnOffsetDates)??!1}:{type:t,hideOnOffsetDates:!1},ka=t=>{const n={noDisabledRange:!1,showLastInRange:!0,minMaxRawRange:!1,partialRange:!0,disableTimeRangeValidation:!1,maxRange:void 0,minRange:void 0,autoRange:void 0,fixedStart:!1,fixedEnd:!1};return typeof t=="object"?{enabled:!0,...n,...t}:{enabled:t,...n}},ba=t=>t?typeof t=="string"?{timezone:t,exactMatch:!1,dateInTz:void 0,emitTimezone:void 0,convertModel:!0}:{timezone:t.timezone,exactMatch:t.exactMatch??!1,dateInTz:t.dateInTz??void 0,emitTimezone:t.emitTimezone??void 0,convertModel:t.convertModel??!0}:{timezone:void 0,exactMatch:!1,emitTimezone:void 0},xt=(t,n,o,a)=>new Map(t.map(r=>{const u=$t(r,n,a);return[Et(u,o),u]})),va=(t,n)=>t.length?new Map(t.map(o=>{const a=$t(o.date,n);return[Et(a,et.DATE),o]})):null,wa=t=>{var o;const n=Zn(t.isMonthPicker,t.isYearPicker);return{minDate:At(t.minDate,t.timezone,t.isSpecific),maxDate:At(t.maxDate,t.timezone,t.isSpecific),disabledDates:Ot(t.disabledDates)?xt(t.disabledDates,t.timezone,n,t.isSpecific):t.disabledDates,allowedDates:Ot(t.allowedDates)?xt(t.allowedDates,t.timezone,n,t.isSpecific):null,highlight:typeof t.highlight=="object"&&Ot((o=t.highlight)==null?void 0:o.dates)?xt(t.highlight.dates,t.timezone,n):t.highlight,markers:va(t.markers,t.timezone)}},Ma=t=>typeof t=="boolean"?{enabled:t,dragSelect:!0,limit:null}:{enabled:!!t,limit:t.limit?+t.limit:null,dragSelect:t.dragSelect??!0},Da=t=>({...Object.fromEntries(Object.keys(t).map(o=>{const a=o,r=t[a],u=typeof t[a]=="string"?{[r]:!0}:Object.fromEntries(r.map(d=>[d,!0]));return[o,u]}))}),be=t=>{const n=()=>{const J=t.enableSeconds?":ss":"",X=t.enableMinutes?":mm":"";return t.is24?`HH${X}${J}`:`hh${X}${J} aa`},o=()=>{var J;return t.format?t.format:t.monthPicker?"MM/yyyy":t.timePicker?n():t.weekPicker?`${((J=H.value)==null?void 0:J.type)==="iso"?"II":"ww"}-RR`:t.yearPicker?"yyyy":t.quarterPicker?"QQQ/yyyy":t.enableTimePicker?`MM/dd/yyyy, ${n()}`:"MM/dd/yyyy"},a=J=>vn(J,t.enableSeconds),r=()=>L.value.enabled?t.startTime&&Array.isArray(t.startTime)?[a(t.startTime[0]),a(t.startTime[1])]:null:t.startTime&&!Array.isArray(t.startTime)?a(t.startTime):null,u=e.computed(()=>ia(t.multiCalendars)),d=e.computed(()=>r()),h=e.computed(()=>sa(t.ariaLabels)),c=e.computed(()=>fa(t.filters)),E=e.computed(()=>ca(t.transitions)),f=e.computed(()=>ma(t.actionRow)),P=e.computed(()=>ua(t.previewFormat,t.format,o())),g=e.computed(()=>da(t.textInput)),$=e.computed(()=>pa(t.inline)),U=e.computed(()=>ga(t.config)),_=e.computed(()=>ya(t.highlight)),H=e.computed(()=>ha(t.weekNumbers)),m=e.computed(()=>ba(t.timezone)),Y=e.computed(()=>Ma(t.multiDates)),w=e.computed(()=>wa({minDate:t.minDate,maxDate:t.maxDate,disabledDates:t.disabledDates,allowedDates:t.allowedDates,highlight:_.value,markers:t.markers,timezone:m.value,isSpecific:t.monthPicker||t.yearPicker||t.quarterPicker,isMonthPicker:t.monthPicker,isYearPicker:t.yearPicker})),L=e.computed(()=>ka(t.range)),q=e.computed(()=>Da(t.ui));return{defaultedTransitions:E,defaultedMultiCalendars:u,defaultedStartTime:d,defaultedAriaLabels:h,defaultedFilters:c,defaultedActionRow:f,defaultedPreviewFormat:P,defaultedTextInput:g,defaultedInline:$,defaultedConfig:U,defaultedHighlight:_,defaultedWeekNumbers:H,defaultedRange:L,propDates:w,defaultedTz:m,defaultedMultiDates:Y,defaultedUI:q,getDefaultPattern:o,getDefaultStartTime:r}},Sa=(t,n,o)=>{const a=e.ref(),{defaultedTextInput:r,defaultedRange:u,defaultedTz:d,defaultedMultiDates:h,getDefaultPattern:c}=be(n),E=e.ref(""),f=e.toRef(n,"format"),P=e.toRef(n,"formatLocale");e.watch(a,()=>{typeof n.onInternalModelChange=="function"&&t("internal-model-change",a.value,A(!0))},{deep:!0}),e.watch(u,(l,D)=>{l.enabled!==D.enabled&&(a.value=null)}),e.watch(f,()=>{W()});const g=l=>d.value.timezone&&d.value.convertModel?Ee(l,d.value.timezone):l,$=l=>{if(d.value.timezone&&d.value.convertModel){const D=Hn(d.value.timezone,l);return i.addHours(l,D)}return l},U=(l,D,re=!1)=>Mn(l,n.format,n.formatLocale,r.value.rangeSeparator,n.modelAuto,D??c(),re),_=l=>l?n.modelType?ne(l):{hours:i.getHours(l),minutes:i.getMinutes(l),seconds:n.enableSeconds?i.getSeconds(l):0}:null,H=l=>n.modelType?ne(l):{month:i.getMonth(l),year:i.getYear(l)},m=l=>Array.isArray(l)?h.value.enabled?l.map(D=>Y(D,i.setYear(j(),D))):gt(()=>[i.setYear(j(),l[0]),l[1]?i.setYear(j(),l[1]):at(u.value.partialRange)],u.value.enabled):i.setYear(j(),+l),Y=(l,D)=>(typeof l=="string"||typeof l=="number")&&n.modelType?B(l):D,w=l=>Array.isArray(l)?[Y(l[0],Ke(null,+l[0].hours,+l[0].minutes,l[0].seconds)),Y(l[1],Ke(null,+l[1].hours,+l[1].minutes,l[1].seconds))]:Y(l,Ke(null,l.hours,l.minutes,l.seconds)),L=l=>{const D=i.set(j(),{date:1});return Array.isArray(l)?h.value.enabled?l.map(re=>Y(re,Ue(D,+re.month,+re.year))):gt(()=>[Y(l[0],Ue(D,+l[0].month,+l[0].year)),Y(l[1],l[1]?Ue(D,+l[1].month,+l[1].year):at(u.value.partialRange))],u.value.enabled):Y(l,Ue(D,+l.month,+l.year))},q=l=>{if(Array.isArray(l))return l.map(D=>B(D));throw new Error(Rt.dateArr("multi-dates"))},J=l=>{if(Array.isArray(l)&&u.value.enabled){const D=l[0],re=l[1];return[j(Array.isArray(D)?D[0]:null),Array.isArray(re)&&re.length?j(re[0]):null]}return j(l[0])},X=l=>n.modelAuto?Array.isArray(l)?[B(l[0]),B(l[1])]:n.autoApply?[B(l)]:[B(l),null]:Array.isArray(l)?gt(()=>l[1]?[B(l[0]),l[1]?B(l[1]):at(u.value.partialRange)]:[B(l[0])],u.value.enabled):B(l),T=()=>{Array.isArray(a.value)&&u.value.enabled&&a.value.length===1&&a.value.push(at(u.value.partialRange))},G=()=>{const l=a.value;return[ne(l[0]),l[1]?ne(l[1]):at(u.value.partialRange)]},R=()=>a.value[1]?G():ne(De(a.value[0])),K=()=>(a.value||[]).map(l=>ne(l)),se=(l=!1)=>(l||T(),n.modelAuto?R():h.value.enabled?K():Array.isArray(a.value)?gt(()=>G(),u.value.enabled):ne(De(a.value))),ue=l=>!l||Array.isArray(l)&&!l.length?null:n.timePicker?w(De(l)):n.monthPicker?L(De(l)):n.yearPicker?m(De(l)):h.value.enabled?q(De(l)):n.weekPicker?J(De(l)):X(De(l)),p=l=>{const D=ue(l);Vt(De(D))?(a.value=De(D),W()):(a.value=null,E.value="")},z=()=>{const l=D=>i.format(D,r.value.format);return`${l(a.value[0])} ${r.value.rangeSeparator} ${a.value[1]?l(a.value[1]):""}`},F=()=>o.value&&a.value?Array.isArray(a.value)?z():i.format(a.value,r.value.format):U(a.value),y=()=>a.value?h.value.enabled?a.value.map(l=>U(l)).join("; "):r.value.enabled&&typeof r.value.format=="string"?F():U(a.value):"",W=()=>{!n.format||typeof n.format=="string"||r.value.enabled&&typeof r.value.format=="string"?E.value=y():E.value=n.format(a.value)},B=l=>{if(n.utc){const D=new Date(l);return n.utc==="preserve"?new Date(D.getTime()+D.getTimezoneOffset()*6e4):D}return n.modelType?Un.includes(n.modelType)?g(new Date(l)):n.modelType==="format"&&(typeof n.format=="string"||!n.format)?g(i.parse(l,c(),new Date,{locale:P.value})):g(i.parse(l,n.modelType,new Date,{locale:P.value})):g(new Date(l))},ne=l=>l?n.utc?ea(l,n.utc==="preserve",n.enableSeconds):n.modelType?n.modelType==="timestamp"?+$(l):n.modelType==="iso"?$(l).toISOString():n.modelType==="format"&&(typeof n.format=="string"||!n.format)?U($(l)):U($(l),n.modelType,!0):$(l):"",C=(l,D=!1,re=!1)=>{if(re)return l;if(t("update:model-value",l),d.value.emitTimezone&&D){const S=Array.isArray(l)?l.map(de=>Ee(De(de),d.value.emitTimezone)):Ee(De(l),d.value.emitTimezone);t("update:model-timezone-value",S)}},O=l=>Array.isArray(a.value)?h.value.enabled?a.value.map(D=>l(D)):[l(a.value[0]),a.value[1]?l(a.value[1]):at(u.value.partialRange)]:l(De(a.value)),k=()=>{if(Array.isArray(a.value)){const l=He(a.value[0],n.weekStart),D=a.value[1]?He(a.value[1],n.weekStart):[];return[l.map(re=>j(re)),D.map(re=>j(re))]}return He(a.value,n.weekStart).map(l=>j(l))},Q=(l,D)=>C(De(O(l)),!1,D),ae=l=>{const D=k();return l?D:t("update:model-value",k())},A=(l=!1)=>(l||W(),n.monthPicker?Q(H,l):n.timePicker?Q(_,l):n.yearPicker?Q(i.getYear,l):n.weekPicker?ae(l):C(se(l),!0,l));return{inputValue:E,internalModelValue:a,checkBeforeEmit:()=>a.value?u.value.enabled?u.value.partialRange?a.value.length>=1:a.value.length===2:!!a.value:!1,parseExternalModelValue:p,formatInputValue:W,emitModelValue:A}},Ca=(t,n)=>{const{defaultedFilters:o,propDates:a}=be(t),{validateMonthYearInRange:r}=Qe(t),u=(f,P)=>{let g=f;return o.value.months.includes(i.getMonth(g))?(g=P?i.addMonths(f,1):i.subMonths(f,1),u(g,P)):g},d=(f,P)=>{let g=f;return o.value.years.includes(i.getYear(g))?(g=P?i.addYears(f,1):i.subYears(f,1),d(g,P)):g},h=(f,P=!1)=>{const g=i.set(j(),{month:t.month,year:t.year});let $=f?i.addMonths(g,1):i.subMonths(g,1);t.disableYearSelect&&($=i.setYear($,t.year));let U=i.getMonth($),_=i.getYear($);o.value.months.includes(U)&&($=u($,f),U=i.getMonth($),_=i.getYear($)),o.value.years.includes(_)&&($=d($,f),_=i.getYear($)),r(U,_,f,t.preventMinMaxNavigation)&&c(U,_,P)},c=(f,P,g)=>{n("update-month-year",{month:f,year:P,fromNav:g})},E=e.computed(()=>f=>wn(i.set(j(),{month:t.month,year:t.year}),a.value.maxDate,a.value.minDate,t.preventMinMaxNavigation,f));return{handleMonthYearChange:h,isDisabled:E,updateMonthYear:c}},ht={multiCalendars:{type:[Boolean,Number,String,Object],default:void 0},modelValue:{type:[String,Date,Array,Object,Number],default:null},modelType:{type:String,default:null},position:{type:String,default:"center"},dark:{type:Boolean,default:!1},format:{type:[String,Function],default:()=>null},autoPosition:{type:[Boolean,String],default:!0},altPosition:{type:Function,default:null},transitions:{type:[Boolean,Object],default:!0},formatLocale:{type:Object,default:null},utc:{type:[Boolean,String],default:!1},ariaLabels:{type:Object,default:()=>({})},offset:{type:[Number,String],default:10},hideNavigation:{type:Array,default:()=>[]},timezone:{type:[String,Object],default:null},vertical:{type:Boolean,default:!1},disableMonthYearSelect:{type:Boolean,default:!1},disableYearSelect:{type:Boolean,default:!1},dayClass:{type:Function,default:null},yearRange:{type:Array,default:()=>[1900,2100]},enableTimePicker:{type:Boolean,default:!0},autoApply:{type:Boolean,default:!1},disabledDates:{type:[Array,Function],default:()=>[]},monthNameFormat:{type:String,default:"short"},startDate:{type:[Date,String],default:null},startTime:{type:[Object,Array],default:null},hideOffsetDates:{type:Boolean,default:!1},noToday:{type:Boolean,default:!1},disabledWeekDays:{type:Array,default:()=>[]},allowedDates:{type:Array,default:null},nowButtonLabel:{type:String,default:"Now"},markers:{type:Array,default:()=>[]},escClose:{type:Boolean,default:!0},spaceConfirm:{type:Boolean,default:!0},monthChangeOnArrows:{type:Boolean,default:!0},presetDates:{type:Array,default:()=>[]},flow:{type:Array,default:()=>[]},partialFlow:{type:Boolean,default:!1},preventMinMaxNavigation:{type:Boolean,default:!1},reverseYears:{type:Boolean,default:!1},weekPicker:{type:Boolean,default:!1},filters:{type:Object,default:()=>({})},arrowNavigation:{type:Boolean,default:!1},highlight:{type:[Function,Object],default:null},teleport:{type:[Boolean,String,Object],default:null},teleportCenter:{type:Boolean,default:!1},locale:{type:String,default:"en-Us"},weekNumName:{type:String,default:"W"},weekStart:{type:[Number,String],default:1},weekNumbers:{type:[String,Function,Object],default:null},monthChangeOnScroll:{type:[Boolean,String],default:!0},dayNames:{type:[Function,Array],default:null},monthPicker:{type:Boolean,default:!1},customProps:{type:Object,default:null},yearPicker:{type:Boolean,default:!1},modelAuto:{type:Boolean,default:!1},selectText:{type:String,default:"Select"},cancelText:{type:String,default:"Cancel"},previewFormat:{type:[String,Function],default:()=>""},multiDates:{type:[Object,Boolean],default:!1},ignoreTimeValidation:{type:Boolean,default:!1},minDate:{type:[Date,String],default:null},maxDate:{type:[Date,String],default:null},minTime:{type:Object,default:null},maxTime:{type:Object,default:null},name:{type:String,default:null},placeholder:{type:String,default:""},hideInputIcon:{type:Boolean,default:!1},clearable:{type:Boolean,default:!0},alwaysClearable:{type:Boolean,default:!1},state:{type:Boolean,default:null},required:{type:Boolean,default:!1},autocomplete:{type:String,default:"off"},timePicker:{type:Boolean,default:!1},enableSeconds:{type:Boolean,default:!1},is24:{type:Boolean,default:!0},noHoursOverlay:{type:Boolean,default:!1},noMinutesOverlay:{type:Boolean,default:!1},noSecondsOverlay:{type:Boolean,default:!1},hoursGridIncrement:{type:[String,Number],default:1},minutesGridIncrement:{type:[String,Number],default:5},secondsGridIncrement:{type:[String,Number],default:5},hoursIncrement:{type:[Number,String],default:1},minutesIncrement:{type:[Number,String],default:1},secondsIncrement:{type:[Number,String],default:1},range:{type:[Boolean,Object],default:!1},uid:{type:String,default:null},disabled:{type:Boolean,default:!1},readonly:{type:Boolean,default:!1},inline:{type:[Boolean,Object],default:!1},textInput:{type:[Boolean,Object],default:!1},sixWeeks:{type:[Boolean,String],default:!1},actionRow:{type:Object,default:()=>({})},focusStartDate:{type:Boolean,default:!1},disabledTimes:{type:[Function,Array],default:void 0},timePickerInline:{type:Boolean,default:!1},calendar:{type:Function,default:null},config:{type:Object,default:void 0},quarterPicker:{type:Boolean,default:!1},yearFirst:{type:Boolean,default:!1},loading:{type:Boolean,default:!1},onInternalModelChange:{type:[Function,Object],default:null},enableMinutes:{type:Boolean,default:!0},ui:{type:Object,default:()=>({})}},ze={...ht,shadow:{type:Boolean,default:!1},flowStep:{type:Number,default:0},internalModelValue:{type:[Date,Array],default:null},noOverlayFocus:{type:Boolean,default:!1},collapse:{type:Boolean,default:!1},menuWrapRef:{type:Object,default:null},getInputRect:{type:Function,default:()=>({})},isTextInputDate:{type:Boolean,default:!1},isMobile:{type:Boolean,default:void 0}},Ba=["title"],Ta=["disabled"],$a=e.defineComponent({compatConfig:{MODE:3},__name:"ActionRow",props:{menuMount:{type:Boolean,default:!1},calendarWidth:{type:Number,default:0},...ze},emits:["close-picker","select-date","select-now","invalid-select"],setup(t,{emit:n}){const o=n,a=t,{defaultedActionRow:r,defaultedPreviewFormat:u,defaultedMultiCalendars:d,defaultedTextInput:h,defaultedInline:c,defaultedRange:E,defaultedMultiDates:f}=be(a),{isTimeValid:P,isMonthValid:g}=Qe(a),{buildMatrix:$}=qe(),U=e.ref(null),_=e.ref(null),H=e.ref(!1),m=e.ref({}),Y=e.ref(null),w=e.ref(null);e.onMounted(()=>{a.arrowNavigation&&$([Se(U),Se(_)],"actionRow"),L(),window.addEventListener("resize",L)}),e.onUnmounted(()=>{window.removeEventListener("resize",L)});const L=()=>{H.value=!1,setTimeout(()=>{var F,y;const p=(F=Y.value)==null?void 0:F.getBoundingClientRect(),z=(y=w.value)==null?void 0:y.getBoundingClientRect();p&&z&&(m.value.maxWidth=`${z.width-p.width-20}px`),H.value=!0},0)},q=e.computed(()=>E.value.enabled&&!E.value.partialRange&&a.internalModelValue?a.internalModelValue.length===2:!0),J=e.computed(()=>!P.value(a.internalModelValue)||!g.value(a.internalModelValue)||!q.value),X=()=>{const p=u.value;return a.timePicker||a.monthPicker,p(De(a.internalModelValue))},T=()=>{const p=a.internalModelValue;return d.value.count>0?`${G(p[0])} - ${G(p[1])}`:[G(p[0]),G(p[1])]},G=p=>Mn(p,u.value,a.formatLocale,h.value.rangeSeparator,a.modelAuto,u.value),R=e.computed(()=>!a.internalModelValue||!a.menuMount?"":typeof u.value=="string"?Array.isArray(a.internalModelValue)?a.internalModelValue.length===2&&a.internalModelValue[1]?T():f.value.enabled?a.internalModelValue.map(p=>`${G(p)}`):a.modelAuto?`${G(a.internalModelValue[0])}`:`${G(a.internalModelValue[0])} -`:G(a.internalModelValue):X()),K=()=>f.value.enabled?"; ":" - ",se=e.computed(()=>Array.isArray(R.value)?R.value.join(K()):R.value),ue=()=>{P.value(a.internalModelValue)&&g.value(a.internalModelValue)&&q.value?o("select-date"):o("invalid-select")};return(p,z)=>(e.openBlock(),e.createElementBlock("div",{ref_key:"actionRowRef",ref:w,class:"dp__action_row"},[p.$slots["action-row"]?e.renderSlot(p.$slots,"action-row",e.normalizeProps(e.mergeProps({key:0},{internalModelValue:p.internalModelValue,disabled:J.value,selectDate:()=>p.$emit("select-date"),closePicker:()=>p.$emit("close-picker")}))):(e.openBlock(),e.createElementBlock(e.Fragment,{key:1},[e.unref(r).showPreview?(e.openBlock(),e.createElementBlock("div",{key:0,class:"dp__selection_preview",title:se.value,style:e.normalizeStyle(m.value)},[p.$slots["action-preview"]&&H.value?e.renderSlot(p.$slots,"action-preview",{key:0,value:p.internalModelValue}):e.createCommentVNode("",!0),!p.$slots["action-preview"]&&H.value?(e.openBlock(),e.createElementBlock(e.Fragment,{key:1},[e.createTextVNode(e.toDisplayString(se.value),1)],64)):e.createCommentVNode("",!0)],12,Ba)):e.createCommentVNode("",!0),e.createElementVNode("div",{ref_key:"actionBtnContainer",ref:Y,class:"dp__action_buttons","data-dp-element":"action-row"},[p.$slots["action-buttons"]?e.renderSlot(p.$slots,"action-buttons",{key:0,value:p.internalModelValue}):e.createCommentVNode("",!0),p.$slots["action-buttons"]?e.createCommentVNode("",!0):(e.openBlock(),e.createElementBlock(e.Fragment,{key:1},[!e.unref(c).enabled&&e.unref(r).showCancel?(e.openBlock(),e.createElementBlock("button",{key:0,ref_key:"cancelButtonRef",ref:U,type:"button",class:"dp__action_button dp__action_cancel",onClick:z[0]||(z[0]=F=>p.$emit("close-picker")),onKeydown:z[1]||(z[1]=F=>e.unref(Re)(F,()=>p.$emit("close-picker")))},e.toDisplayString(p.cancelText),545)):e.createCommentVNode("",!0),e.unref(r).showNow?(e.openBlock(),e.createElementBlock("button",{key:1,type:"button",class:"dp__action_button dp__action_cancel",onClick:z[2]||(z[2]=F=>p.$emit("select-now")),onKeydown:z[3]||(z[3]=F=>e.unref(Re)(F,()=>p.$emit("select-now")))},e.toDisplayString(p.nowButtonLabel),33)):e.createCommentVNode("",!0),e.unref(r).showSelect?(e.openBlock(),e.createElementBlock("button",{key:2,ref_key:"selectButtonRef",ref:_,type:"button",class:"dp__action_button dp__action_select",disabled:J.value,"data-test-id":"select-button",onKeydown:z[4]||(z[4]=F=>e.unref(Re)(F,()=>ue())),onClick:ue},e.toDisplayString(p.selectText),41,Ta)):e.createCommentVNode("",!0)],64))],512)],64))],512))}}),Aa=["role","aria-label","tabindex"],Pa={class:"dp__selection_grid_header"},Ra=["aria-selected","aria-disabled","data-test-id","onClick","onKeydown","onMouseover"],Na=["aria-label"],ut=e.defineComponent({__name:"SelectionOverlay",props:{items:{},type:{},isLast:{type:Boolean},arrowNavigation:{type:Boolean},skipButtonRef:{type:Boolean},headerRefs:{},hideNavigation:{},escClose:{type:Boolean},useRelative:{type:Boolean},height:{},textInput:{type:[Boolean,Object]},config:{},noOverlayFocus:{type:Boolean},focusValue:{},menuWrapRef:{},ariaLabels:{},overlayLabel:{}},emits:["selected","toggle","reset-flow","hover-value"],setup(t,{expose:n,emit:o}){const{setSelectionGrid:a,buildMultiLevelMatrix:r,setMonthPicker:u}=qe(),d=o,h=t,{defaultedAriaLabels:c,defaultedTextInput:E,defaultedConfig:f}=be(h),{hideNavigationButtons:P}=vt(),g=e.ref(!1),$=e.ref(null),U=e.ref(null),_=e.ref([]),H=e.ref(),m=e.ref(null),Y=e.ref(0),w=e.ref(null);e.onBeforeUpdate(()=>{$.value=null}),e.onMounted(()=>{e.nextTick().then(()=>K()),h.noOverlayFocus||q(),L(!0)}),e.onUnmounted(()=>L(!1));const L=O=>{var k;h.arrowNavigation&&((k=h.headerRefs)!=null&&k.length?u(O):a(O))},q=()=>{var k;const O=Se(U);O&&(E.value.enabled||($.value?(k=$.value)==null||k.focus({preventScroll:!0}):O.focus({preventScroll:!0})),g.value=O.clientHeight<O.scrollHeight)},J=e.computed(()=>({dp__overlay:!0,"dp--overlay-absolute":!h.useRelative,"dp--overlay-relative":h.useRelative})),X=e.computed(()=>h.useRelative?{height:`${h.height}px`,width:"var(--dp-menu-min-width)"}:void 0),T=e.computed(()=>({dp__overlay_col:!0})),G=e.computed(()=>({dp__btn:!0,dp__button:!0,dp__overlay_action:!0,dp__over_action_scroll:g.value,dp__button_bottom:h.isLast})),R=e.computed(()=>{var O,k;return{dp__overlay_container:!0,dp__container_flex:((O=h.items)==null?void 0:O.length)<=6,dp__container_block:((k=h.items)==null?void 0:k.length)>6}});e.watch(()=>h.items,()=>K(!1),{deep:!0});const K=(O=!0)=>{e.nextTick().then(()=>{const k=Se($),Q=Se(U),ae=Se(m),A=Se(w),Z=ae?ae.getBoundingClientRect().height:0;Q&&(Q.getBoundingClientRect().height?Y.value=Q.getBoundingClientRect().height-Z:Y.value=f.value.modeHeight-Z),k&&A&&O&&(A.scrollTop=k.offsetTop-A.offsetTop-(Y.value/2-k.getBoundingClientRect().height)-Z)})},se=O=>{O.disabled||d("selected",O.value)},ue=()=>{d("toggle"),d("reset-flow")},p=()=>{h.escClose&&ue()},z=(O,k,Q,ae)=>{O&&((k.active||k.value===h.focusValue)&&($.value=O),h.arrowNavigation&&(Array.isArray(_.value[Q])?_.value[Q][ae]=O:_.value[Q]=[O],F()))},F=()=>{var k,Q;const O=(k=h.headerRefs)!=null&&k.length?[h.headerRefs].concat(_.value):_.value.concat([h.skipButtonRef?[]:[m.value]]);r(De(O),(Q=h.headerRefs)!=null&&Q.length?"monthPicker":"selectionGrid")},y=O=>{h.arrowNavigation||je(O,f.value,!0)},W=O=>{H.value=O,d("hover-value",O)},B=()=>{if(ue(),!h.isLast){const O=Nt(h.menuWrapRef??null,"action-row");if(O){const k=mn(O);k==null||k.focus()}}},ne=O=>{switch(O.key){case he.esc:return p();case he.arrowLeft:return y(O);case he.arrowRight:return y(O);case he.arrowUp:return y(O);case he.arrowDown:return y(O);default:return}},C=O=>{if(O.key===he.enter)return ue();if(O.key===he.tab)return B()};return n({focusGrid:q}),(O,k)=>{var Q;return e.openBlock(),e.createElementBlock("div",{ref_key:"gridWrapRef",ref:U,class:e.normalizeClass(J.value),style:e.normalizeStyle(X.value),role:O.useRelative?void 0:"dialog","aria-label":O.overlayLabel,tabindex:O.useRelative?void 0:"0",onKeydown:ne,onClick:k[0]||(k[0]=e.withModifiers(()=>{},["prevent"]))},[e.createElementVNode("div",{ref_key:"containerRef",ref:w,class:e.normalizeClass(R.value),style:e.normalizeStyle({"--dp-overlay-height":`${Y.value}px`}),role:"grid"},[e.createElementVNode("div",Pa,[e.renderSlot(O.$slots,"header")]),O.$slots.overlay?e.renderSlot(O.$slots,"overlay",{key:0}):(e.openBlock(!0),e.createElementBlock(e.Fragment,{key:1},e.renderList(O.items,(ae,A)=>(e.openBlock(),e.createElementBlock("div",{key:A,class:e.normalizeClass(["dp__overlay_row",{dp__flex_row:O.items.length>=3}]),role:"row"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(ae,(Z,l)=>(e.openBlock(),e.createElementBlock("div",{key:Z.value,ref_for:!0,ref:D=>z(D,Z,A,l),role:"gridcell",class:e.normalizeClass(T.value),"aria-selected":Z.active||void 0,"aria-disabled":Z.disabled||void 0,tabindex:"0","data-test-id":Z.text,onClick:e.withModifiers(D=>se(Z),["prevent"]),onKeydown:D=>e.unref(Re)(D,()=>se(Z),!0),onMouseover:D=>W(Z.value)},[e.createElementVNode("div",{class:e.normalizeClass(Z.className)},[O.$slots.item?e.renderSlot(O.$slots,"item",{key:0,item:Z}):e.createCommentVNode("",!0),O.$slots.item?e.createCommentVNode("",!0):(e.openBlock(),e.createElementBlock(e.Fragment,{key:1},[e.createTextVNode(e.toDisplayString(Z.text),1)],64))],2)],42,Ra))),128))],2))),128))],6),O.$slots["button-icon"]?e.withDirectives((e.openBlock(),e.createElementBlock("button",{key:0,ref_key:"toggleButton",ref:m,type:"button","aria-label":(Q=e.unref(c))==null?void 0:Q.toggleOverlay,class:e.normalizeClass(G.value),tabindex:"0",onClick:ue,onKeydown:C},[e.renderSlot(O.$slots,"button-icon")],42,Na)),[[e.vShow,!e.unref(P)(O.hideNavigation,O.type)]]):e.createCommentVNode("",!0)],46,Aa)}}}),Ea=["data-dp-mobile"],kt=e.defineComponent({__name:"InstanceWrap",props:{multiCalendars:{},stretch:{type:Boolean},collapse:{type:Boolean},isMobile:{type:Boolean}},setup(t){const n=t,o=e.computed(()=>n.multiCalendars>0?[...Array(n.multiCalendars).keys()]:[0]),a=e.computed(()=>({dp__instance_calendar:n.multiCalendars>0}));return(r,u)=>(e.openBlock(),e.createElementBlock("div",{class:e.normalizeClass({dp__menu_inner:!r.stretch,"dp--menu--inner-stretched":r.stretch,dp__flex_display:r.multiCalendars>0,"dp--flex-display-collapsed":r.collapse}),"data-dp-mobile":r.isMobile},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(o.value,(d,h)=>(e.openBlock(),e.createElementBlock("div",{key:d,class:e.normalizeClass(a.value)},[e.renderSlot(r.$slots,"default",{instance:d,index:h})],2))),128))],10,Ea))}}),Oa=["data-dp-element","aria-label","aria-disabled"],ct=e.defineComponent({compatConfig:{MODE:3},__name:"ArrowBtn",props:{ariaLabel:{},elName:{},disabled:{type:Boolean}},emits:["activate","set-ref"],setup(t,{emit:n}){const o=n,a=e.ref(null);return e.onMounted(()=>o("set-ref",a)),(r,u)=>(e.openBlock(),e.createElementBlock("button",{ref_key:"elRef",ref:a,type:"button","data-dp-element":r.elName,class:"dp__btn dp--arrow-btn-nav",tabindex:"0","aria-label":r.ariaLabel,"aria-disabled":r.disabled||void 0,onClick:u[0]||(u[0]=d=>r.$emit("activate")),onKeydown:u[1]||(u[1]=d=>e.unref(Re)(d,()=>r.$emit("activate"),!0))},[e.createElementVNode("span",{class:e.normalizeClass(["dp__inner_nav",{dp__inner_nav_disabled:r.disabled}])},[e.renderSlot(r.$slots,"default")],2)],40,Oa))}}),Ya=["aria-label","data-test-id"],An=e.defineComponent({__name:"YearModePicker",props:{...ze,showYearPicker:{type:Boolean,default:!1},items:{type:Array,default:()=>[]},instance:{type:Number,default:0},year:{type:Number,default:0},isDisabled:{type:Function,default:()=>!1}},emits:["toggle-year-picker","year-select","handle-year"],setup(t,{emit:n}){const o=n,a=t,{showRightIcon:r,showLeftIcon:u}=vt(),{defaultedConfig:d,defaultedMultiCalendars:h,defaultedAriaLabels:c,defaultedTransitions:E,defaultedUI:f}=be(a),{showTransition:P,transitionName:g}=dt(E),$=e.ref(!1),U=(m=!1,Y)=>{$.value=!$.value,o("toggle-year-picker",{flow:m,show:Y})},_=m=>{$.value=!1,o("year-select",m)},H=(m=!1)=>{o("handle-year",m)};return(m,Y)=>{var w,L,q,J,X;return e.openBlock(),e.createElementBlock(e.Fragment,null,[e.createElementVNode("div",{class:e.normalizeClass(["dp--year-mode-picker",{"dp--hidden-el":$.value}])},[e.unref(u)(e.unref(h),t.instance)?(e.openBlock(),e.createBlock(ct,{key:0,ref:"mpPrevIconRef","aria-label":(w=e.unref(c))==null?void 0:w.prevYear,disabled:t.isDisabled(!1),class:e.normalizeClass((L=e.unref(f))==null?void 0:L.navBtnPrev),onActivate:Y[0]||(Y[0]=T=>H(!1))},{default:e.withCtx(()=>[m.$slots["arrow-left"]?e.renderSlot(m.$slots,"arrow-left",{key:0}):e.createCommentVNode("",!0),m.$slots["arrow-left"]?e.createCommentVNode("",!0):(e.openBlock(),e.createBlock(e.unref(Dt),{key:1}))]),_:3},8,["aria-label","disabled","class"])):e.createCommentVNode("",!0),e.createElementVNode("button",{ref:"mpYearButtonRef",class:"dp__btn dp--year-select",type:"button","aria-label":`${t.year}-${(q=e.unref(c))==null?void 0:q.openYearsOverlay}`,"data-test-id":`year-mode-btn-${t.instance}`,onClick:Y[1]||(Y[1]=()=>U(!1)),onKeydown:Y[2]||(Y[2]=e.withKeys(()=>U(!1),["enter"]))},[m.$slots.year?e.renderSlot(m.$slots,"year",{key:0,year:t.year}):e.createCommentVNode("",!0),m.$slots.year?e.createCommentVNode("",!0):(e.openBlock(),e.createElementBlock(e.Fragment,{key:1},[e.createTextVNode(e.toDisplayString(t.year),1)],64))],40,Ya),e.unref(r)(e.unref(h),t.instance)?(e.openBlock(),e.createBlock(ct,{key:1,ref:"mpNextIconRef","aria-label":(J=e.unref(c))==null?void 0:J.nextYear,disabled:t.isDisabled(!0),class:e.normalizeClass((X=e.unref(f))==null?void 0:X.navBtnNext),onActivate:Y[3]||(Y[3]=T=>H(!0))},{default:e.withCtx(()=>[m.$slots["arrow-right"]?e.renderSlot(m.$slots,"arrow-right",{key:0}):e.createCommentVNode("",!0),m.$slots["arrow-right"]?e.createCommentVNode("",!0):(e.openBlock(),e.createBlock(e.unref(St),{key:1}))]),_:3},8,["aria-label","disabled","class"])):e.createCommentVNode("",!0)],2),e.createVNode(e.Transition,{name:e.unref(g)(t.showYearPicker),css:e.unref(P)},{default:e.withCtx(()=>{var T,G;return[t.showYearPicker?(e.openBlock(),e.createBlock(ut,{key:0,items:t.items,"text-input":m.textInput,"esc-close":m.escClose,config:m.config,"is-last":m.autoApply&&!e.unref(d).keepActionRow,"hide-navigation":m.hideNavigation,"aria-labels":m.ariaLabels,"overlay-label":(G=(T=e.unref(c))==null?void 0:T.yearPicker)==null?void 0:G.call(T,!0),type:"year",onToggle:U,onSelected:Y[4]||(Y[4]=R=>_(R))},e.createSlots({"button-icon":e.withCtx(()=>[m.$slots["calendar-icon"]?e.renderSlot(m.$slots,"calendar-icon",{key:0}):e.createCommentVNode("",!0),m.$slots["calendar-icon"]?e.createCommentVNode("",!0):(e.openBlock(),e.createBlock(e.unref(Fe),{key:1}))]),_:2},[m.$slots["year-overlay-value"]?{name:"item",fn:e.withCtx(({item:R})=>[e.renderSlot(m.$slots,"year-overlay-value",{text:R.text,value:R.value})]),key:"0"}:void 0]),1032,["items","text-input","esc-close","config","is-last","hide-navigation","aria-labels","overlay-label"])):e.createCommentVNode("",!0)]}),_:3},8,["name","css"])],64)}}}),qt=(t,n,o)=>{if(n.value&&Array.isArray(n.value))if(n.value.some(a=>pe(t,a))){const a=n.value.filter(r=>!pe(r,t));n.value=a.length?a:null}else(o&&+o>n.value.length||!o)&&n.value.push(t);else n.value=[t]},Gt=(t,n,o)=>{let a=t.value?t.value.slice():[];return a.length===2&&a[1]!==null&&(a=[]),a.length?(ve(n,a[0])?a.unshift(n):a[1]=n,o("range-end",n)):(a=[n],o("range-start",n)),a},bt=(t,n,o,a)=>{t&&(t[0]&&t[1]&&o&&n("auto-apply"),t[0]&&!t[1]&&a&&o&&n("auto-apply"))},Pn=t=>{Array.isArray(t.value)&&t.value.length<=2&&t.range?t.modelValue.value=t.value.map(n=>Ee(j(n),t.timezone)):Array.isArray(t.value)||(t.modelValue.value=Ee(j(t.value),t.timezone))},Rn=(t,n,o,a)=>Array.isArray(n.value)&&(n.value.length===2||n.value.length===1&&a.value.partialRange)?a.value.fixedStart&&(we(t,n.value[0])||pe(t,n.value[0]))?[n.value[0],t]:a.value.fixedEnd&&(ve(t,n.value[1])||pe(t,n.value[1]))?[t,n.value[1]]:(o("invalid-fixed-range",t),n.value):[],Nn=({multiCalendars:t,range:n,highlight:o,propDates:a,calendars:r,modelValue:u,props:d,filters:h,year:c,month:E,emit:f})=>{const P=e.computed(()=>Pt(d.yearRange,d.locale,d.reverseYears)),g=e.ref([!1]),$=e.computed(()=>(R,K)=>{const se=i.set(Ie(new Date),{month:E.value(R),year:c.value(R)}),ue=K?i.endOfYear(se):i.startOfYear(se);return wn(ue,a.value.maxDate,a.value.minDate,d.preventMinMaxNavigation,K)}),U=()=>Array.isArray(u.value)&&t.value.solo&&u.value[1],_=()=>{for(let R=0;R<t.value.count;R++)if(R===0)r.value[R]=r.value[0];else if(R===t.value.count-1&&U())r.value[R]={month:i.getMonth(u.value[1]),year:i.getYear(u.value[1])};else{const K=i.set(j(),r.value[R-1]);r.value[R]={month:i.getMonth(K),year:i.getYear(i.addYears(K,1))}}},H=R=>{if(!R)return _();const K=i.set(j(),r.value[R]);return r.value[0].year=i.getYear(i.subYears(K,t.value.count-1)),_()},m=(R,K)=>{const se=i.differenceInYears(K,R);return n.value.showLastInRange&&se>1?K:R},Y=R=>d.focusStartDate||t.value.solo?R[0]:R[1]?m(R[0],R[1]):R[0],w=()=>{if(u.value){const R=Array.isArray(u.value)?Y(u.value):u.value;r.value[0]={month:i.getMonth(R),year:i.getYear(R)}}},L=()=>{w(),t.value.count&&_()};e.watch(u,(R,K)=>{d.isTextInputDate&&JSON.stringify(R??{})!==JSON.stringify(K??{})&&L()}),e.onMounted(()=>{L()});const q=(R,K)=>{r.value[K].year=R,f("update-month-year",{instance:K,year:R,month:r.value[K].month}),t.value.count&&!t.value.solo&&H(K)},J=e.computed(()=>R=>tt(P.value,K=>{var z;const se=c.value(R)===K.value,ue=ot(K.value,nt(a.value.minDate),nt(a.value.maxDate))||((z=h.value.years)==null?void 0:z.includes(c.value(R))),p=Lt(o.value,K.value);return{active:se,disabled:ue,highlighted:p}})),X=(R,K)=>{q(R,K),G(K)},T=(R,K=!1)=>{if(!$.value(R,K)){const se=K?c.value(R)+1:c.value(R)-1;q(se,R)}},G=(R,K=!1,se)=>{K||f("reset-flow"),se!==void 0?g.value[R]=se:g.value[R]=!g.value[R],g.value[R]?f("overlay-toggle",{open:!0,overlay:$e.year}):(f("overlay-closed"),f("overlay-toggle",{open:!1,overlay:$e.year}))};return{isDisabled:$,groupedYears:J,showYearPicker:g,selectYear:q,toggleYearPicker:G,handleYearSelect:X,handleYear:T}},Va=(t,n)=>{const{defaultedMultiCalendars:o,defaultedAriaLabels:a,defaultedTransitions:r,defaultedConfig:u,defaultedRange:d,defaultedHighlight:h,propDates:c,defaultedTz:E,defaultedFilters:f,defaultedMultiDates:P}=be(t),g=()=>{t.isTextInputDate&&L(i.getYear(j(t.startDate)),0)},{modelValue:$,year:U,month:_,calendars:H}=ft(t,n,g),m=e.computed(()=>un(t.formatLocale,t.locale,t.monthNameFormat)),Y=e.ref(null),{checkMinMaxRange:w}=Qe(t),{selectYear:L,groupedYears:q,showYearPicker:J,toggleYearPicker:X,handleYearSelect:T,handleYear:G,isDisabled:R}=Nn({modelValue:$,multiCalendars:o,range:d,highlight:h,calendars:H,year:U,propDates:c,month:_,filters:f,props:t,emit:n});e.onMounted(()=>{t.startDate&&($.value&&t.focusStartDate||!$.value)&&L(i.getYear(j(t.startDate)),0)});const K=A=>A?{month:i.getMonth(A),year:i.getYear(A)}:{month:null,year:null},se=()=>$.value?Array.isArray($.value)?$.value.map(A=>K(A)):K($.value):K(),ue=(A,Z)=>{const l=H.value[A],D=se();return Array.isArray(D)?D.some(re=>re.year===(l==null?void 0:l.year)&&re.month===Z):(l==null?void 0:l.year)===D.year&&Z===D.month},p=(A,Z,l)=>{var re,S;const D=se();return Array.isArray(D)?U.value(Z)===((re=D[l])==null?void 0:re.year)&&A===((S=D[l])==null?void 0:S.month):!1},z=(A,Z)=>{if(d.value.enabled){const l=se();if(Array.isArray($.value)&&Array.isArray(l)){const D=p(A,Z,0)||p(A,Z,1),re=Ue(Ie(j()),A,U.value(Z));return st($.value,Y.value,re)&&!D}return!1}return!1},F=e.computed(()=>A=>tt(m.value,Z=>{var de;const l=ue(A,Z.value),D=ot(Z.value,hn(U.value(A),c.value.minDate),kn(U.value(A),c.value.maxDate))||la(c.value.disabledDates,U.value(A),Z.value)||((de=f.value.months)==null?void 0:de.includes(Z.value))||!oa(c.value.allowedDates,U.value(A),Z.value),re=z(Z.value,A),S=Sn(h.value,Z.value,U.value(A));return{active:l,disabled:D,isBetween:re,highlighted:S}})),y=(A,Z)=>Ue(Ie(j()),A,U.value(Z)),W=(A,Z)=>{const l=$.value?$.value:Ie(new Date);$.value=Ue(l,A,U.value(Z)),n("auto-apply"),n("update-flow-step")},B=(A,Z)=>{const l=y(A,Z);d.value.fixedEnd||d.value.fixedStart?$.value=Rn(l,$,n,d):$.value?w(l,$.value)&&($.value=Gt($,y(A,Z),n)):$.value=[y(A,Z)],e.nextTick().then(()=>{bt($.value,n,t.autoApply,t.modelAuto)})},ne=(A,Z)=>{qt(y(A,Z),$,P.value.limit),n("auto-apply",!0)},C=(A,Z)=>(H.value[Z].month=A,k(Z,H.value[Z].year,A),P.value.enabled?ne(A,Z):d.value.enabled?B(A,Z):W(A,Z)),O=(A,Z)=>{L(A,Z),k(Z,A,null)},k=(A,Z,l)=>{let D=l;if(!D&&D!==0){const re=se();D=Array.isArray(re)?re[A].month:re.month}n("update-month-year",{instance:A,year:Z,month:D})};return{groupedMonths:F,groupedYears:q,year:U,isDisabled:R,defaultedMultiCalendars:o,defaultedAriaLabels:a,defaultedTransitions:r,defaultedConfig:u,showYearPicker:J,modelValue:$,presetDate:(A,Z)=>{Pn({value:A,modelValue:$,range:d.value.enabled,timezone:Z?void 0:E.value.timezone}),n("auto-apply")},setHoverDate:(A,Z)=>{Y.value=y(A,Z)},selectMonth:C,selectYear:O,toggleYearPicker:X,handleYearSelect:T,handleYear:G,getModelMonthYear:se}},_a=e.defineComponent({compatConfig:{MODE:3},__name:"MonthPicker",props:{...ze},emits:["update:internal-model-value","overlay-closed","reset-flow","range-start","range-end","auto-apply","update-month-year","update-flow-step","mount","invalid-fixed-range","overlay-toggle"],setup(t,{expose:n,emit:o}){const a=o,r=e.useSlots(),u=Oe(r,"yearMode"),d=t;e.onMounted(()=>{d.shadow||a("mount",null)});const{groupedMonths:h,groupedYears:c,year:E,isDisabled:f,defaultedMultiCalendars:P,defaultedConfig:g,showYearPicker:$,modelValue:U,presetDate:_,setHoverDate:H,selectMonth:m,selectYear:Y,toggleYearPicker:w,handleYearSelect:L,handleYear:q,getModelMonthYear:J}=Va(d,a);return n({getSidebarProps:()=>({modelValue:U,year:E,getModelMonthYear:J,selectMonth:m,selectYear:Y,handleYear:q}),presetDate:_,toggleYearPicker:T=>w(0,T)}),(T,G)=>(e.openBlock(),e.createBlock(kt,{"multi-calendars":e.unref(P).count,collapse:T.collapse,stretch:"","is-mobile":T.isMobile},{default:e.withCtx(({instance:R})=>[T.$slots["top-extra"]?e.renderSlot(T.$slots,"top-extra",{key:0,value:T.internalModelValue}):e.createCommentVNode("",!0),T.$slots["month-year"]?e.renderSlot(T.$slots,"month-year",e.normalizeProps(e.mergeProps({key:1},{year:e.unref(E),months:e.unref(h)(R),years:e.unref(c)(R),selectMonth:e.unref(m),selectYear:e.unref(Y),instance:R}))):(e.openBlock(),e.createBlock(ut,{key:2,items:e.unref(h)(R),"arrow-navigation":T.arrowNavigation,"is-last":T.autoApply&&!e.unref(g).keepActionRow,"esc-close":T.escClose,height:e.unref(g).modeHeight,config:T.config,"no-overlay-focus":!!(T.noOverlayFocus||T.textInput),"use-relative":"",type:"month",onSelected:K=>e.unref(m)(K,R),onHoverValue:K=>e.unref(H)(K,R)},e.createSlots({header:e.withCtx(()=>[e.createVNode(An,e.mergeProps(T.$props,{items:e.unref(c)(R),instance:R,"show-year-picker":e.unref($)[R],year:e.unref(E)(R),"is-disabled":K=>e.unref(f)(R,K),onHandleYear:K=>e.unref(q)(R,K),onYearSelect:K=>e.unref(L)(K,R),onToggleYearPicker:K=>e.unref(w)(R,K==null?void 0:K.flow,K==null?void 0:K.show)}),e.createSlots({_:2},[e.renderList(e.unref(u),(K,se)=>({name:K,fn:e.withCtx(ue=>[e.renderSlot(T.$slots,K,e.normalizeProps(e.guardReactiveProps(ue)))])}))]),1040,["items","instance","show-year-picker","year","is-disabled","onHandleYear","onYearSelect","onToggleYearPicker"])]),_:2},[T.$slots["month-overlay-value"]?{name:"item",fn:e.withCtx(({item:K})=>[e.renderSlot(T.$slots,"month-overlay-value",{text:K.text,value:K.value})]),key:"0"}:void 0]),1032,["items","arrow-navigation","is-last","esc-close","height","config","no-overlay-focus","onSelected","onHoverValue"]))]),_:3},8,["multi-calendars","collapse","is-mobile"]))}}),Ia=(t,n)=>{const o=()=>{t.isTextInputDate&&(f.value=i.getYear(j(t.startDate)))},{modelValue:a}=ft(t,n,o),r=e.ref(null),{defaultedHighlight:u,defaultedMultiDates:d,defaultedFilters:h,defaultedRange:c,propDates:E}=be(t),f=e.ref();e.onMounted(()=>{t.startDate&&(a.value&&t.focusStartDate||!a.value)&&(f.value=i.getYear(j(t.startDate)))});const P=w=>Array.isArray(a.value)?a.value.some(L=>i.getYear(L)===w):a.value?i.getYear(a.value)===w:!1,g=w=>c.value.enabled&&Array.isArray(a.value)?st(a.value,r.value,H(w)):!1,$=w=>E.value.allowedDates instanceof Map?E.value.allowedDates.size?E.value.allowedDates.has(`${w}`):!1:!0,U=w=>E.value.disabledDates instanceof Map?E.value.disabledDates.size?E.value.disabledDates.has(`${w}`):!1:!0,_=e.computed(()=>tt(Pt(t.yearRange,t.locale,t.reverseYears),w=>{const L=P(w.value),q=ot(w.value,nt(E.value.minDate),nt(E.value.maxDate))||h.value.years.includes(w.value)||!$(w.value)||U(w.value),J=g(w.value)&&!L,X=Lt(u.value,w.value);return{active:L,disabled:q,isBetween:J,highlighted:X}})),H=w=>i.setYear(Ie(i.startOfYear(new Date)),w);return{groupedYears:_,modelValue:a,focusYear:f,setHoverValue:w=>{r.value=i.setYear(Ie(new Date),w)},selectYear:w=>{var L;if(n("update-month-year",{instance:0,year:w}),d.value.enabled)return a.value?Array.isArray(a.value)&&(((L=a.value)==null?void 0:L.map(J=>i.getYear(J))).includes(w)?a.value=a.value.filter(J=>i.getYear(J)!==w):a.value.push(i.setYear(Be(j()),w))):a.value=[i.setYear(Be(i.startOfYear(j())),w)],n("auto-apply",!0);c.value.enabled?(a.value=Gt(a,H(w),n),e.nextTick().then(()=>{bt(a.value,n,t.autoApply,t.modelAuto)})):(a.value=H(w),n("auto-apply"))}}},za=e.defineComponent({compatConfig:{MODE:3},__name:"YearPicker",props:{...ze},emits:["update:internal-model-value","reset-flow","range-start","range-end","auto-apply","update-month-year"],setup(t,{expose:n,emit:o}){const a=o,r=t,{groupedYears:u,modelValue:d,focusYear:h,selectYear:c,setHoverValue:E}=Ia(r,a),{defaultedConfig:f}=be(r);return n({getSidebarProps:()=>({modelValue:d,selectYear:c})}),(g,$)=>(e.openBlock(),e.createElementBlock("div",null,[g.$slots["top-extra"]?e.renderSlot(g.$slots,"top-extra",{key:0,value:g.internalModelValue}):e.createCommentVNode("",!0),g.$slots["month-year"]?e.renderSlot(g.$slots,"month-year",e.normalizeProps(e.mergeProps({key:1},{years:e.unref(u),selectYear:e.unref(c)}))):(e.openBlock(),e.createBlock(ut,{key:2,items:e.unref(u),"is-last":g.autoApply&&!e.unref(f).keepActionRow,height:e.unref(f).modeHeight,config:g.config,"no-overlay-focus":!!(g.noOverlayFocus||g.textInput),"focus-value":e.unref(h),type:"year","use-relative":"",onSelected:e.unref(c),onHoverValue:e.unref(E)},e.createSlots({_:2},[g.$slots["year-overlay-value"]?{name:"item",fn:e.withCtx(({item:U})=>[e.renderSlot(g.$slots,"year-overlay-value",{text:U.text,value:U.value})]),key:"0"}:void 0]),1032,["items","is-last","height","config","no-overlay-focus","focus-value","onSelected","onHoverValue"]))]))}}),La={key:0,class:"dp__time_input"},Ha=["data-compact","data-collapsed"],Ua=["data-test-id","aria-label","onKeydown","onClick","onMousedown"],Wa=["aria-label","disabled","data-test-id","onKeydown","onClick"],ja=["data-test-id","aria-label","onKeydown","onClick","onMousedown"],Ka={key:0},xa=["aria-label","data-compact"],qa=e.defineComponent({compatConfig:{MODE:3},__name:"TimeInput",props:{hours:{type:Number,default:0},minutes:{type:Number,default:0},seconds:{type:Number,default:0},closeTimePickerBtn:{type:Object,default:null},order:{type:Number,default:0},disabledTimesConfig:{type:Function,default:null},validateTime:{type:Function,default:()=>!1},...ze},emits:["set-hours","set-minutes","update:hours","update:minutes","update:seconds","reset-flow","mounted","overlay-closed","overlay-opened","am-pm-change"],setup(t,{expose:n,emit:o}){const a=o,r=t,{setTimePickerElements:u,setTimePickerBackRef:d}=qe(),{defaultedAriaLabels:h,defaultedTransitions:c,defaultedFilters:E,defaultedConfig:f,defaultedRange:P,defaultedMultiCalendars:g}=be(r),{transitionName:$,showTransition:U}=dt(c),_=e.reactive({hours:!1,minutes:!1,seconds:!1}),H=e.ref("AM"),m=e.ref(null),Y=e.ref([]),w=e.ref(),L=e.ref(!1);e.onMounted(()=>{a("mounted")});const q=s=>i.set(new Date,{hours:s.hours,minutes:s.minutes,seconds:r.enableSeconds?s.seconds:0,milliseconds:0}),J=e.computed(()=>s=>B(s,r[s])||T(s,r[s])),X=e.computed(()=>({hours:r.hours,minutes:r.minutes,seconds:r.seconds})),T=(s,V)=>P.value.enabled&&!P.value.disableTimeRangeValidation?!r.validateTime(s,V):!1,G=(s,V)=>{if(P.value.enabled&&!P.value.disableTimeRangeValidation){const oe=V?+r[`${s}Increment`]:-+r[`${s}Increment`],N=r[s]+oe;return!r.validateTime(s,N)}return!1},R=e.computed(()=>s=>!Q(+r[s]+ +r[`${s}Increment`],s)||G(s,!0)),K=e.computed(()=>s=>!Q(+r[s]-+r[`${s}Increment`],s)||G(s,!1)),se=(s,V)=>i.add(i.set(j(),s),V),ue=(s,V)=>i.sub(i.set(j(),s),V),p=e.computed(()=>({dp__time_col:!0,dp__time_col_block:!r.timePickerInline,dp__time_col_reg_block:!r.enableSeconds&&r.is24&&!r.timePickerInline,dp__time_col_reg_inline:!r.enableSeconds&&r.is24&&r.timePickerInline,dp__time_col_reg_with_button:!r.enableSeconds&&!r.is24,dp__time_col_sec:r.enableSeconds&&r.is24,dp__time_col_sec_with_button:r.enableSeconds&&!r.is24})),z=e.computed(()=>r.timePickerInline&&P.value.enabled&&!g.value.count),F=e.computed(()=>{const s=[{type:"hours"}];return r.enableMinutes&&s.push({type:"",separator:!0},{type:"minutes"}),r.enableSeconds&&s.push({type:"",separator:!0},{type:"seconds"}),s}),y=e.computed(()=>F.value.filter(s=>!s.separator)),W=e.computed(()=>s=>{if(s==="hours"){const V=re(+r.hours);return{text:V<10?`0${V}`:`${V}`,value:V}}return{text:r[s]<10?`0${r[s]}`:`${r[s]}`,value:r[s]}}),B=(s,V)=>{var N;if(!r.disabledTimesConfig)return!1;const oe=r.disabledTimesConfig(r.order,s==="hours"?V:void 0);return oe[s]?!!((N=oe[s])!=null&&N.includes(V)):!0},ne=(s,V)=>V!=="hours"||H.value==="AM"?s:s+12,C=s=>{const V=r.is24?24:12,oe=s==="hours"?V:60,N=+r[`${s}GridIncrement`],me=s==="hours"&&!r.is24?N:0,fe=[];for(let ge=me;ge<oe;ge+=N)fe.push({value:r.is24?ge:ne(ge,s),text:ge<10?`0${ge}`:`${ge}`});return s==="hours"&&!r.is24&&fe.unshift({value:H.value==="PM"?12:0,text:"12"}),tt(fe,ge=>({active:!1,disabled:E.value.times[s].includes(ge.value)||!Q(ge.value,s)||B(s,ge.value)||T(s,ge.value)}))},O=s=>s>=0?s:59,k=s=>s>=0?s:23,Q=(s,V)=>{const oe=r.minTime?q(Yt(r.minTime)):null,N=r.maxTime?q(Yt(r.maxTime)):null,me=q(Yt(X.value,V,V==="minutes"||V==="seconds"?O(s):k(s)));return oe&&N?(i.isBefore(me,N)||i.isEqual(me,N))&&(i.isAfter(me,oe)||i.isEqual(me,oe)):oe?i.isAfter(me,oe)||i.isEqual(me,oe):N?i.isBefore(me,N)||i.isEqual(me,N):!0},ae=s=>r[`no${s[0].toUpperCase()+s.slice(1)}Overlay`],A=s=>{ae(s)||(_[s]=!_[s],_[s]?(L.value=!0,a("overlay-opened",s)):(L.value=!1,a("overlay-closed",s)))},Z=s=>s==="hours"?i.getHours:s==="minutes"?i.getMinutes:i.getSeconds,l=()=>{w.value&&clearTimeout(w.value)},D=(s,V=!0,oe)=>{const N=V?se:ue,me=V?+r[`${s}Increment`]:-+r[`${s}Increment`];Q(+r[s]+me,s)&&a(`update:${s}`,Z(s)(N({[s]:+r[s]},{[s]:+r[`${s}Increment`]}))),!(oe!=null&&oe.keyboard)&&f.value.timeArrowHoldThreshold&&(w.value=setTimeout(()=>{D(s,V)},f.value.timeArrowHoldThreshold))},re=s=>r.is24?s:(s>=12?H.value="PM":H.value="AM",Kn(s)),S=()=>{H.value==="PM"?(H.value="AM",a("update:hours",r.hours-12)):(H.value="PM",a("update:hours",r.hours+12)),a("am-pm-change",H.value)},de=s=>{_[s]=!0},ce=(s,V,oe)=>{if(s&&r.arrowNavigation){Array.isArray(Y.value[V])?Y.value[V][oe]=s:Y.value[V]=[s];const N=Y.value.reduce((me,fe)=>fe.map((ge,v)=>[...me[v]||[],fe[v]]),[]);d(r.closeTimePickerBtn),m.value&&(N[1]=N[1].concat(m.value)),u(N,r.order)}},te=(s,V)=>(A(s),a(`update:${s}`,V));return n({openChildCmp:de}),(s,V)=>{var oe;return s.disabled?e.createCommentVNode("",!0):(e.openBlock(),e.createElementBlock("div",La,[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(F.value,(N,me)=>{var fe,ge,v;return e.openBlock(),e.createElementBlock("div",{key:me,class:e.normalizeClass(p.value),"data-compact":z.value&&!s.enableSeconds,"data-collapsed":z.value&&s.enableSeconds},[N.separator?(e.openBlock(),e.createElementBlock(e.Fragment,{key:0},[L.value?e.createCommentVNode("",!0):(e.openBlock(),e.createElementBlock(e.Fragment,{key:0},[e.createTextVNode(":")],64))],64)):(e.openBlock(),e.createElementBlock(e.Fragment,{key:1},[e.createElementVNode("button",{ref_for:!0,ref:I=>ce(I,me,0),type:"button",class:e.normalizeClass({dp__btn:!0,dp__inc_dec_button:!s.timePickerInline,dp__inc_dec_button_inline:s.timePickerInline,dp__tp_inline_btn_top:s.timePickerInline,dp__inc_dec_button_disabled:R.value(N.type),"dp--hidden-el":L.value}),"data-test-id":`${N.type}-time-inc-btn-${r.order}`,"aria-label":(fe=e.unref(h))==null?void 0:fe.incrementValue(N.type),tabindex:"0",onKeydown:I=>e.unref(Re)(I,()=>D(N.type,!0,{keyboard:!0}),!0),onClick:I=>e.unref(f).timeArrowHoldThreshold?void 0:D(N.type,!0),onMousedown:I=>e.unref(f).timeArrowHoldThreshold?D(N.type,!0):void 0,onMouseup:l},[r.timePickerInline?(e.openBlock(),e.createElementBlock(e.Fragment,{key:1},[s.$slots["tp-inline-arrow-up"]?e.renderSlot(s.$slots,"tp-inline-arrow-up",{key:0}):(e.openBlock(),e.createElementBlock(e.Fragment,{key:1},[V[2]||(V[2]=e.createElementVNode("span",{class:"dp__tp_inline_btn_bar dp__tp_btn_in_l"},null,-1)),V[3]||(V[3]=e.createElementVNode("span",{class:"dp__tp_inline_btn_bar dp__tp_btn_in_r"},null,-1))],64))],64)):(e.openBlock(),e.createElementBlock(e.Fragment,{key:0},[s.$slots["arrow-up"]?e.renderSlot(s.$slots,"arrow-up",{key:0}):e.createCommentVNode("",!0),s.$slots["arrow-up"]?e.createCommentVNode("",!0):(e.openBlock(),e.createBlock(e.unref(Bt),{key:1}))],64))],42,Ua),e.createElementVNode("button",{ref_for:!0,ref:I=>ce(I,me,1),type:"button","aria-label":`${W.value(N.type).text}-${(ge=e.unref(h))==null?void 0:ge.openTpOverlay(N.type)}`,class:e.normalizeClass({dp__time_display:!0,dp__time_display_block:!s.timePickerInline,dp__time_display_inline:s.timePickerInline,"dp--time-invalid":J.value(N.type),"dp--time-overlay-btn":!J.value(N.type),"dp--hidden-el":L.value}),disabled:ae(N.type),tabindex:"0","data-test-id":`${N.type}-toggle-overlay-btn-${r.order}`,onKeydown:I=>e.unref(Re)(I,()=>A(N.type),!0),onClick:I=>A(N.type)},[s.$slots[N.type]?e.renderSlot(s.$slots,N.type,{key:0,text:W.value(N.type).text,value:W.value(N.type).value}):e.createCommentVNode("",!0),s.$slots[N.type]?e.createCommentVNode("",!0):(e.openBlock(),e.createElementBlock(e.Fragment,{key:1},[e.createTextVNode(e.toDisplayString(W.value(N.type).text),1)],64))],42,Wa),e.createElementVNode("button",{ref_for:!0,ref:I=>ce(I,me,2),type:"button",class:e.normalizeClass({dp__btn:!0,dp__inc_dec_button:!s.timePickerInline,dp__inc_dec_button_inline:s.timePickerInline,dp__tp_inline_btn_bottom:s.timePickerInline,dp__inc_dec_button_disabled:K.value(N.type),"dp--hidden-el":L.value}),"data-test-id":`${N.type}-time-dec-btn-${r.order}`,"aria-label":(v=e.unref(h))==null?void 0:v.decrementValue(N.type),tabindex:"0",onKeydown:I=>e.unref(Re)(I,()=>D(N.type,!1,{keyboard:!0}),!0),onClick:I=>e.unref(f).timeArrowHoldThreshold?void 0:D(N.type,!1),onMousedown:I=>e.unref(f).timeArrowHoldThreshold?D(N.type,!1):void 0,onMouseup:l},[r.timePickerInline?(e.openBlock(),e.createElementBlock(e.Fragment,{key:1},[s.$slots["tp-inline-arrow-down"]?e.renderSlot(s.$slots,"tp-inline-arrow-down",{key:0}):(e.openBlock(),e.createElementBlock(e.Fragment,{key:1},[V[4]||(V[4]=e.createElementVNode("span",{class:"dp__tp_inline_btn_bar dp__tp_btn_in_l"},null,-1)),V[5]||(V[5]=e.createElementVNode("span",{class:"dp__tp_inline_btn_bar dp__tp_btn_in_r"},null,-1))],64))],64)):(e.openBlock(),e.createElementBlock(e.Fragment,{key:0},[s.$slots["arrow-down"]?e.renderSlot(s.$slots,"arrow-down",{key:0}):e.createCommentVNode("",!0),s.$slots["arrow-down"]?e.createCommentVNode("",!0):(e.openBlock(),e.createBlock(e.unref(Tt),{key:1}))],64))],42,ja)],64))],10,Ha)}),128)),s.is24?e.createCommentVNode("",!0):(e.openBlock(),e.createElementBlock("div",Ka,[s.$slots["am-pm-button"]?e.renderSlot(s.$slots,"am-pm-button",{key:0,toggle:S,value:H.value}):e.createCommentVNode("",!0),s.$slots["am-pm-button"]?e.createCommentVNode("",!0):(e.openBlock(),e.createElementBlock("button",{key:1,ref_key:"amPmButton",ref:m,type:"button",class:"dp__pm_am_button",role:"button","aria-label":(oe=e.unref(h))==null?void 0:oe.amPmButton,tabindex:"0","data-compact":z.value,onClick:S,onKeydown:V[0]||(V[0]=N=>e.unref(Re)(N,()=>S(),!0))},e.toDisplayString(H.value),41,xa))])),(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(y.value,(N,me)=>(e.openBlock(),e.createBlock(e.Transition,{key:me,name:e.unref($)(_[N.type]),css:e.unref(U)},{default:e.withCtx(()=>{var fe,ge;return[_[N.type]?(e.openBlock(),e.createBlock(ut,{key:0,items:C(N.type),"is-last":s.autoApply&&!e.unref(f).keepActionRow,"esc-close":s.escClose,type:N.type,"text-input":s.textInput,config:s.config,"arrow-navigation":s.arrowNavigation,"aria-labels":s.ariaLabels,"overlay-label":(ge=(fe=e.unref(h)).timeOverlay)==null?void 0:ge.call(fe,N.type),onSelected:v=>te(N.type,v),onToggle:v=>A(N.type),onResetFlow:V[1]||(V[1]=v=>s.$emit("reset-flow"))},e.createSlots({"button-icon":e.withCtx(()=>[s.$slots["clock-icon"]?e.renderSlot(s.$slots,"clock-icon",{key:0}):e.createCommentVNode("",!0),s.$slots["clock-icon"]?e.createCommentVNode("",!0):(e.openBlock(),e.createBlock(e.resolveDynamicComponent(s.timePickerInline?e.unref(Fe):e.unref(Ct)),{key:1}))]),_:2},[s.$slots[`${N.type}-overlay-value`]?{name:"item",fn:e.withCtx(({item:v})=>[e.renderSlot(s.$slots,`${N.type}-overlay-value`,{text:v.text,value:v.value})]),key:"0"}:void 0,s.$slots[`${N.type}-overlay-header`]?{name:"header",fn:e.withCtx(()=>[e.renderSlot(s.$slots,`${N.type}-overlay-header`,{toggle:()=>A(N.type)})]),key:"1"}:void 0]),1032,["items","is-last","esc-close","type","text-input","config","arrow-navigation","aria-labels","overlay-label","onSelected","onToggle"])):e.createCommentVNode("",!0)]}),_:2},1032,["name","css"]))),128))]))}}}),Ga=["data-dp-mobile"],Qa=["aria-label","tabindex"],Xa=["role","aria-label","tabindex"],Ja=["aria-label"],En=e.defineComponent({compatConfig:{MODE:3},__name:"TimePicker",props:{hours:{type:[Number,Array],default:0},minutes:{type:[Number,Array],default:0},seconds:{type:[Number,Array],default:0},disabledTimesConfig:{type:Function,default:null},validateTime:{type:Function,default:()=>!1},...ze},emits:["update:hours","update:minutes","update:seconds","mount","reset-flow","overlay-opened","overlay-closed","am-pm-change"],setup(t,{expose:n,emit:o}){const a=o,r=t,{buildMatrix:u,setTimePicker:d}=qe(),h=e.useSlots(),{defaultedTransitions:c,defaultedAriaLabels:E,defaultedTextInput:f,defaultedConfig:P,defaultedRange:g}=be(r),{transitionName:$,showTransition:U}=dt(c),{hideNavigationButtons:_}=vt(),H=e.ref(null),m=e.ref(null),Y=e.ref([]),w=e.ref(null),L=e.ref(!1);e.onMounted(()=>{a("mount"),!r.timePicker&&r.arrowNavigation?u([Se(H.value)],"time"):d(!0,r.timePicker)});const q=e.computed(()=>g.value.enabled&&r.modelAuto?cn(r.internalModelValue):!0),J=e.ref(!1),X=B=>({hours:Array.isArray(r.hours)?r.hours[B]:r.hours,minutes:Array.isArray(r.minutes)?r.minutes[B]:r.minutes,seconds:Array.isArray(r.seconds)?r.seconds[B]:r.seconds}),T=e.computed(()=>{const B=[];if(g.value.enabled)for(let ne=0;ne<2;ne++)B.push(X(ne));else B.push(X(0));return B}),G=(B,ne=!1,C="")=>{ne||a("reset-flow"),J.value=B,a(B?"overlay-opened":"overlay-closed",$e.time),r.arrowNavigation&&d(B),e.nextTick(()=>{C!==""&&Y.value[0]&&Y.value[0].openChildCmp(C)})},R=e.computed(()=>({dp__btn:!0,dp__button:!0,dp__button_bottom:r.autoApply&&!P.value.keepActionRow})),K=Oe(h,"timePicker"),se=(B,ne,C)=>g.value.enabled?ne===0?[B,T.value[1][C]]:[T.value[0][C],B]:B,ue=B=>{a("update:hours",B)},p=B=>{a("update:minutes",B)},z=B=>{a("update:seconds",B)},F=()=>{if(w.value&&!f.value.enabled&&!r.noOverlayFocus){const B=mn(w.value);B&&B.focus({preventScroll:!0})}},y=B=>{L.value=!1,a("overlay-closed",B)},W=B=>{L.value=!0,a("overlay-opened",B)};return n({toggleTimePicker:G}),(B,ne)=>{var C;return e.openBlock(),e.createElementBlock("div",{class:"dp--tp-wrap","data-dp-mobile":B.isMobile},[!B.timePicker&&!B.timePickerInline?e.withDirectives((e.openBlock(),e.createElementBlock("button",{key:0,ref_key:"openTimePickerBtn",ref:H,type:"button",class:e.normalizeClass({...R.value,"dp--hidden-el":J.value}),"aria-label":(C=e.unref(E))==null?void 0:C.openTimePicker,tabindex:B.noOverlayFocus?void 0:0,"data-test-id":"open-time-picker-btn",onKeydown:ne[0]||(ne[0]=O=>e.unref(Re)(O,()=>G(!0))),onClick:ne[1]||(ne[1]=O=>G(!0))},[B.$slots["clock-icon"]?e.renderSlot(B.$slots,"clock-icon",{key:0}):e.createCommentVNode("",!0),B.$slots["clock-icon"]?e.createCommentVNode("",!0):(e.openBlock(),e.createBlock(e.unref(Ct),{key:1}))],42,Qa)),[[e.vShow,!e.unref(_)(B.hideNavigation,"time")]]):e.createCommentVNode("",!0),e.createVNode(e.Transition,{name:e.unref($)(J.value),css:e.unref(U)&&!B.timePickerInline},{default:e.withCtx(()=>{var O,k;return[J.value||B.timePicker||B.timePickerInline?(e.openBlock(),e.createElementBlock("div",{key:0,ref_key:"overlayRef",ref:w,role:B.timePickerInline?void 0:"dialog",class:e.normalizeClass({dp__overlay:!B.timePickerInline,"dp--overlay-absolute":!r.timePicker&&!B.timePickerInline,"dp--overlay-relative":r.timePicker}),style:e.normalizeStyle(B.timePicker?{height:`${e.unref(P).modeHeight}px`}:void 0),"aria-label":(O=e.unref(E))==null?void 0:O.timePicker,tabindex:B.timePickerInline?void 0:0},[e.createElementVNode("div",{class:e.normalizeClass(B.timePickerInline?"dp__time_picker_inline_container":"dp__overlay_container dp__container_flex dp__time_picker_overlay_container"),style:{display:"flex"}},[B.$slots["time-picker-overlay"]?e.renderSlot(B.$slots,"time-picker-overlay",{key:0,hours:t.hours,minutes:t.minutes,seconds:t.seconds,setHours:ue,setMinutes:p,setSeconds:z}):e.createCommentVNode("",!0),B.$slots["time-picker-overlay"]?e.createCommentVNode("",!0):(e.openBlock(),e.createElementBlock("div",{key:1,class:e.normalizeClass(B.timePickerInline?"dp__flex":"dp__overlay_row dp__flex_row")},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(T.value,(Q,ae)=>e.withDirectives((e.openBlock(),e.createBlock(qa,e.mergeProps({key:ae,ref_for:!0},{...B.$props,order:ae,hours:Q.hours,minutes:Q.minutes,seconds:Q.seconds,closeTimePickerBtn:m.value,disabledTimesConfig:t.disabledTimesConfig,disabled:ae===0?e.unref(g).fixedStart:e.unref(g).fixedEnd},{ref_for:!0,ref_key:"timeInputRefs",ref:Y,"validate-time":(A,Z)=>t.validateTime(A,se(Z,ae,A)),"onUpdate:hours":A=>ue(se(A,ae,"hours")),"onUpdate:minutes":A=>p(se(A,ae,"minutes")),"onUpdate:seconds":A=>z(se(A,ae,"seconds")),onMounted:F,onOverlayClosed:y,onOverlayOpened:W,onAmPmChange:ne[2]||(ne[2]=A=>B.$emit("am-pm-change",A))}),e.createSlots({_:2},[e.renderList(e.unref(K),(A,Z)=>({name:A,fn:e.withCtx(l=>[e.renderSlot(B.$slots,A,e.mergeProps({ref_for:!0},l))])}))]),1040,["validate-time","onUpdate:hours","onUpdate:minutes","onUpdate:seconds"])),[[e.vShow,ae===0?!0:q.value]])),128))],2)),!B.timePicker&&!B.timePickerInline?e.withDirectives((e.openBlock(),e.createElementBlock("button",{key:2,ref_key:"closeTimePickerBtn",ref:m,type:"button",class:e.normalizeClass({...R.value,"dp--hidden-el":L.value}),"aria-label":(k=e.unref(E))==null?void 0:k.closeTimePicker,tabindex:"0",onKeydown:ne[3]||(ne[3]=Q=>e.unref(Re)(Q,()=>G(!1))),onClick:ne[4]||(ne[4]=Q=>G(!1))},[B.$slots["calendar-icon"]?e.renderSlot(B.$slots,"calendar-icon",{key:0}):e.createCommentVNode("",!0),B.$slots["calendar-icon"]?e.createCommentVNode("",!0):(e.openBlock(),e.createBlock(e.unref(Fe),{key:1}))],42,Ja)),[[e.vShow,!e.unref(_)(B.hideNavigation,"time")]]):e.createCommentVNode("",!0)],2)],14,Xa)):e.createCommentVNode("",!0)]}),_:3},8,["name","css"])],8,Ga)}}}),On=(t,n,o,a)=>{const{defaultedRange:r}=be(t),u=(w,L)=>Array.isArray(n[w])?n[w][L]:n[w],d=w=>t.enableSeconds?Array.isArray(n.seconds)?n.seconds[w]:n.seconds:0,h=(w,L)=>w?L!==void 0?Ke(w,u("hours",L),u("minutes",L),d(L)):Ke(w,n.hours,n.minutes,d()):i.setSeconds(j(),d(L)),c=(w,L)=>{n[w]=L},E=e.computed(()=>t.modelAuto&&r.value.enabled?Array.isArray(o.value)?o.value.length>1:!1:r.value.enabled),f=(w,L)=>{const q=Object.fromEntries(Object.keys(n).map(J=>J===w?[J,L]:[J,n[J]].slice()));if(E.value&&!r.value.disableTimeRangeValidation){const J=T=>o.value?Ke(o.value[T],q.hours[T],q.minutes[T],q.seconds[T]):null,X=T=>i.setMilliseconds(o.value[T],0);return!(pe(J(0),J(1))&&(i.isAfter(J(0),X(1))||i.isBefore(J(1),X(0))))}return!0},P=(w,L)=>{f(w,L)&&(c(w,L),a&&a())},g=w=>{P("hours",w)},$=w=>{P("minutes",w)},U=w=>{P("seconds",w)},_=(w,L,q,J)=>{L&&g(w),!L&&!q&&$(w),q&&U(w),o.value&&J(o.value)},H=w=>{if(w){const L=Array.isArray(w),q=L?[+w[0].hours,+w[1].hours]:+w.hours,J=L?[+w[0].minutes,+w[1].minutes]:+w.minutes,X=L?[+w[0].seconds,+w[1].seconds]:+w.seconds;c("hours",q),c("minutes",J),t.enableSeconds&&c("seconds",X)}},m=(w,L)=>{const q={hours:Array.isArray(n.hours)?n.hours[w]:n.hours,disabledArr:[]};return(L||L===0)&&(q.hours=L),Array.isArray(t.disabledTimes)&&(q.disabledArr=r.value.enabled&&Array.isArray(t.disabledTimes[w])?t.disabledTimes[w]:t.disabledTimes),q},Y=e.computed(()=>(w,L)=>{var q;if(Array.isArray(t.disabledTimes)){const{disabledArr:J,hours:X}=m(w,L),T=J.filter(G=>+G.hours===X);return((q=T[0])==null?void 0:q.minutes)==="*"?{hours:[X],minutes:void 0,seconds:void 0}:{hours:[],minutes:(T==null?void 0:T.map(G=>+G.minutes))??[],seconds:(T==null?void 0:T.map(G=>G.seconds?+G.seconds:void 0))??[]}}return{hours:[],minutes:[],seconds:[]}});return{setTime:c,updateHours:g,updateMinutes:$,updateSeconds:U,getSetDateTime:h,updateTimeValues:_,getSecondsValue:d,assignStartTime:H,validateTime:f,disabledTimesConfig:Y}},Za=(t,n)=>{const o=()=>{t.isTextInputDate&&L()},{modelValue:a,time:r}=ft(t,n,o),{defaultedStartTime:u,defaultedRange:d,defaultedTz:h}=be(t),{updateTimeValues:c,getSetDateTime:E,setTime:f,assignStartTime:P,disabledTimesConfig:g,validateTime:$}=On(t,r,a,U);function U(){n("update-flow-step")}const _=X=>{const{hours:T,minutes:G,seconds:R}=X;return{hours:+T,minutes:+G,seconds:R?+R:0}},H=()=>{if(t.startTime){if(Array.isArray(t.startTime)){const T=_(t.startTime[0]),G=_(t.startTime[1]);return[i.set(j(),T),i.set(j(),G)]}const X=_(t.startTime);return i.set(j(),X)}return d.value.enabled?[null,null]:null},m=()=>{if(d.value.enabled){const[X,T]=H();a.value=[Ee(E(X,0),h.value.timezone),Ee(E(T,1),h.value.timezone)]}else a.value=Ee(E(H()),h.value.timezone)},Y=X=>Array.isArray(X)?[Je(j(X[0])),Je(j(X[1]))]:[Je(X??j())],w=(X,T,G)=>{f("hours",X),f("minutes",T),f("seconds",t.enableSeconds?G:0)},L=()=>{const[X,T]=Y(a.value);return d.value.enabled?w([X.hours,T.hours],[X.minutes,T.minutes],[X.seconds,T.seconds]):w(X.hours,X.minutes,X.seconds)};e.onMounted(()=>{if(!t.shadow)return P(u.value),a.value?L():m()});const q=()=>{Array.isArray(a.value)?a.value=a.value.map((X,T)=>X&&E(X,T)):a.value=E(a.value),n("time-update")};return{modelValue:a,time:r,disabledTimesConfig:g,updateTime:(X,T=!0,G=!1)=>{c(X,T,G,q)},validateTime:$}},Fa=e.defineComponent({compatConfig:{MODE:3},__name:"TimePickerSolo",props:{...ze},emits:["update:internal-model-value","time-update","am-pm-change","mount","reset-flow","update-flow-step","overlay-toggle"],setup(t,{expose:n,emit:o}){const a=o,r=t,u=e.useSlots(),d=Oe(u,"timePicker"),h=e.ref(null),{time:c,modelValue:E,disabledTimesConfig:f,updateTime:P,validateTime:g}=Za(r,a);return e.onMounted(()=>{r.shadow||a("mount",null)}),n({getSidebarProps:()=>({modelValue:E,time:c,updateTime:P}),toggleTimePicker:(_,H=!1,m="")=>{var Y;(Y=h.value)==null||Y.toggleTimePicker(_,H,m)}}),(_,H)=>(e.openBlock(),e.createBlock(kt,{"multi-calendars":0,stretch:"","is-mobile":_.isMobile},{default:e.withCtx(()=>[e.createVNode(En,e.mergeProps({ref_key:"tpRef",ref:h},_.$props,{hours:e.unref(c).hours,minutes:e.unref(c).minutes,seconds:e.unref(c).seconds,"internal-model-value":_.internalModelValue,"disabled-times-config":e.unref(f),"validate-time":e.unref(g),"onUpdate:hours":H[0]||(H[0]=m=>e.unref(P)(m)),"onUpdate:minutes":H[1]||(H[1]=m=>e.unref(P)(m,!1)),"onUpdate:seconds":H[2]||(H[2]=m=>e.unref(P)(m,!1,!0)),onAmPmChange:H[3]||(H[3]=m=>_.$emit("am-pm-change",m)),onResetFlow:H[4]||(H[4]=m=>_.$emit("reset-flow")),onOverlayClosed:H[5]||(H[5]=m=>_.$emit("overlay-toggle",{open:!1,overlay:m})),onOverlayOpened:H[6]||(H[6]=m=>_.$emit("overlay-toggle",{open:!0,overlay:m}))}),e.createSlots({_:2},[e.renderList(e.unref(d),(m,Y)=>({name:m,fn:e.withCtx(w=>[e.renderSlot(_.$slots,m,e.normalizeProps(e.guardReactiveProps(w)))])}))]),1040,["hours","minutes","seconds","internal-model-value","disabled-times-config","validate-time"])]),_:3},8,["is-mobile"]))}}),er={class:"dp--header-wrap"},tr={key:0,class:"dp__month_year_wrap"},nr={key:0},ar={class:"dp__month_year_wrap"},rr=["data-dp-element","aria-label","data-test-id","onClick","onKeydown"],lr=e.defineComponent({compatConfig:{MODE:3},__name:"DpHeader",props:{month:{type:Number,default:0},year:{type:Number,default:0},instance:{type:Number,default:0},years:{type:Array,default:()=>[]},months:{type:Array,default:()=>[]},...ze},emits:["update-month-year","mount","reset-flow","overlay-closed","overlay-opened"],setup(t,{expose:n,emit:o}){const a=o,r=t,{defaultedTransitions:u,defaultedAriaLabels:d,defaultedMultiCalendars:h,defaultedFilters:c,defaultedConfig:E,defaultedHighlight:f,propDates:P,defaultedUI:g}=be(r),{transitionName:$,showTransition:U}=dt(u),{buildMatrix:_}=qe(),{handleMonthYearChange:H,isDisabled:m,updateMonthYear:Y}=Ca(r,a),{showLeftIcon:w,showRightIcon:L}=vt(),q=e.ref(!1),J=e.ref(!1),X=e.ref(!1),T=e.ref([null,null,null,null]);e.onMounted(()=>{a("mount")});const G=k=>({get:()=>r[k],set:Q=>{const ae=k===Ve.month?Ve.year:Ve.month;a("update-month-year",{[k]:Q,[ae]:r[ae]}),k===Ve.month?y(!0):W(!0)}}),R=e.computed(G(Ve.month)),K=e.computed(G(Ve.year)),se=e.computed(()=>k=>({month:r.month,year:r.year,items:k===Ve.month?r.months:r.years,instance:r.instance,updateMonthYear:Y,toggle:k===Ve.month?y:W})),ue=e.computed(()=>{const k=r.months.find(Q=>Q.value===r.month);return k||{text:"",value:0}}),p=e.computed(()=>tt(r.months,k=>{const Q=r.month===k.value,ae=ot(k.value,hn(r.year,P.value.minDate),kn(r.year,P.value.maxDate))||c.value.months.includes(k.value),A=Sn(f.value,k.value,r.year);return{active:Q,disabled:ae,highlighted:A}})),z=e.computed(()=>tt(r.years,k=>{const Q=r.year===k.value,ae=ot(k.value,nt(P.value.minDate),nt(P.value.maxDate))||c.value.years.includes(k.value),A=Lt(f.value,k.value);return{active:Q,disabled:ae,highlighted:A}})),F=(k,Q,ae)=>{ae!==void 0?k.value=ae:k.value=!k.value,k.value?(X.value=!0,a("overlay-opened",Q)):(X.value=!1,a("overlay-closed",Q))},y=(k=!1,Q)=>{B(k),F(q,$e.month,Q)},W=(k=!1,Q)=>{B(k),F(J,$e.year,Q)},B=k=>{k||a("reset-flow")},ne=(k,Q)=>{r.arrowNavigation&&(T.value[Q]=Se(k),_(T.value,"monthYear"))},C=e.computed(()=>{var k,Q,ae,A,Z,l;return[{type:Ve.month,index:1,toggle:y,modelValue:R.value,updateModelValue:D=>R.value=D,text:ue.value.text,showSelectionGrid:q.value,items:p.value,ariaLabel:(k=d.value)==null?void 0:k.openMonthsOverlay,overlayLabel:((ae=(Q=d.value).monthPicker)==null?void 0:ae.call(Q,!0))??void 0},{type:Ve.year,index:2,toggle:W,modelValue:K.value,updateModelValue:D=>K.value=D,text:pn(r.year,r.locale),showSelectionGrid:J.value,items:z.value,ariaLabel:(A=d.value)==null?void 0:A.openYearsOverlay,overlayLabel:((l=(Z=d.value).yearPicker)==null?void 0:l.call(Z,!0))??void 0}]}),O=e.computed(()=>r.disableYearSelect?[C.value[0]]:r.yearFirst?[...C.value].reverse():C.value);return n({toggleMonthPicker:y,toggleYearPicker:W,handleMonthYearChange:H}),(k,Q)=>{var ae,A,Z,l,D,re;return e.openBlock(),e.createElementBlock("div",er,[k.$slots["month-year"]?(e.openBlock(),e.createElementBlock("div",tr,[e.renderSlot(k.$slots,"month-year",e.normalizeProps(e.guardReactiveProps({month:t.month,year:t.year,months:t.months,years:t.years,updateMonthYear:e.unref(Y),handleMonthYearChange:e.unref(H),instance:t.instance,isDisabled:e.unref(m)})))])):(e.openBlock(),e.createElementBlock(e.Fragment,{key:1},[k.$slots["top-extra"]?(e.openBlock(),e.createElementBlock("div",nr,[e.renderSlot(k.$slots,"top-extra",{value:k.internalModelValue})])):e.createCommentVNode("",!0),e.createElementVNode("div",ar,[e.unref(w)(e.unref(h),t.instance)&&!k.vertical?(e.openBlock(),e.createBlock(ct,{key:0,"aria-label":(ae=e.unref(d))==null?void 0:ae.prevMonth,disabled:e.unref(m)(!1),class:e.normalizeClass((A=e.unref(g))==null?void 0:A.navBtnPrev),"el-name":"action-prev",onActivate:Q[0]||(Q[0]=S=>e.unref(H)(!1,!0)),onSetRef:Q[1]||(Q[1]=S=>ne(S,0))},{default:e.withCtx(()=>[k.$slots["arrow-left"]?e.renderSlot(k.$slots,"arrow-left",{key:0}):e.createCommentVNode("",!0),k.$slots["arrow-left"]?e.createCommentVNode("",!0):(e.openBlock(),e.createBlock(e.unref(Dt),{key:1}))]),_:3},8,["aria-label","disabled","class"])):e.createCommentVNode("",!0),e.createElementVNode("div",{class:e.normalizeClass(["dp__month_year_wrap",{dp__year_disable_select:k.disableYearSelect}])},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(O.value,(S,de)=>(e.openBlock(),e.createElementBlock(e.Fragment,{key:S.type},[e.createElementVNode("button",{ref_for:!0,ref:ce=>ne(ce,de+1),type:"button","data-dp-element":`overlay-${S.type}`,class:e.normalizeClass(["dp__btn dp__month_year_select",{"dp--hidden-el":X.value}]),"aria-label":`${S.text}-${S.ariaLabel}`,"data-test-id":`${S.type}-toggle-overlay-${t.instance}`,onClick:S.toggle,onKeydown:ce=>e.unref(Re)(ce,()=>S.toggle(),!0)},[k.$slots[S.type]?e.renderSlot(k.$slots,S.type,{key:0,text:S.text,value:r[S.type]}):e.createCommentVNode("",!0),k.$slots[S.type]?e.createCommentVNode("",!0):(e.openBlock(),e.createElementBlock(e.Fragment,{key:1},[e.createTextVNode(e.toDisplayString(S.text),1)],64))],42,rr),e.createVNode(e.Transition,{name:e.unref($)(S.showSelectionGrid),css:e.unref(U)},{default:e.withCtx(()=>[S.showSelectionGrid?(e.openBlock(),e.createBlock(ut,{key:0,items:S.items,"arrow-navigation":k.arrowNavigation,"hide-navigation":k.hideNavigation,"is-last":k.autoApply&&!e.unref(E).keepActionRow,"skip-button-ref":!1,config:k.config,type:S.type,"header-refs":[],"esc-close":k.escClose,"menu-wrap-ref":k.menuWrapRef,"text-input":k.textInput,"aria-labels":k.ariaLabels,"overlay-label":S.overlayLabel,onSelected:S.updateModelValue,onToggle:S.toggle},e.createSlots({"button-icon":e.withCtx(()=>[k.$slots["calendar-icon"]?e.renderSlot(k.$slots,"calendar-icon",{key:0}):e.createCommentVNode("",!0),k.$slots["calendar-icon"]?e.createCommentVNode("",!0):(e.openBlock(),e.createBlock(e.unref(Fe),{key:1}))]),_:2},[k.$slots[`${S.type}-overlay-value`]?{name:"item",fn:e.withCtx(({item:ce})=>[e.renderSlot(k.$slots,`${S.type}-overlay-value`,{text:ce.text,value:ce.value})]),key:"0"}:void 0,k.$slots[`${S.type}-overlay`]?{name:"overlay",fn:e.withCtx(()=>[e.renderSlot(k.$slots,`${S.type}-overlay`,e.mergeProps({ref_for:!0},se.value(S.type)))]),key:"1"}:void 0,k.$slots[`${S.type}-overlay-header`]?{name:"header",fn:e.withCtx(()=>[e.renderSlot(k.$slots,`${S.type}-overlay-header`,{toggle:S.toggle})]),key:"2"}:void 0]),1032,["items","arrow-navigation","hide-navigation","is-last","config","type","esc-close","menu-wrap-ref","text-input","aria-labels","overlay-label","onSelected","onToggle"])):e.createCommentVNode("",!0)]),_:2},1032,["name","css"])],64))),128))],2),e.unref(w)(e.unref(h),t.instance)&&k.vertical?(e.openBlock(),e.createBlock(ct,{key:1,"aria-label":(Z=e.unref(d))==null?void 0:Z.prevMonth,"el-name":"action-prev",disabled:e.unref(m)(!1),class:e.normalizeClass((l=e.unref(g))==null?void 0:l.navBtnPrev),onActivate:Q[2]||(Q[2]=S=>e.unref(H)(!1,!0))},{default:e.withCtx(()=>[k.$slots["arrow-up"]?e.renderSlot(k.$slots,"arrow-up",{key:0}):e.createCommentVNode("",!0),k.$slots["arrow-up"]?e.createCommentVNode("",!0):(e.openBlock(),e.createBlock(e.unref(Bt),{key:1}))]),_:3},8,["aria-label","disabled","class"])):e.createCommentVNode("",!0),e.unref(L)(e.unref(h),t.instance)?(e.openBlock(),e.createBlock(ct,{key:2,ref:"rightIcon","el-name":"action-next",disabled:e.unref(m)(!0),"aria-label":(D=e.unref(d))==null?void 0:D.nextMonth,class:e.normalizeClass((re=e.unref(g))==null?void 0:re.navBtnNext),onActivate:Q[3]||(Q[3]=S=>e.unref(H)(!0,!0)),onSetRef:Q[4]||(Q[4]=S=>ne(S,k.disableYearSelect?2:3))},{default:e.withCtx(()=>[k.$slots[k.vertical?"arrow-down":"arrow-right"]?e.renderSlot(k.$slots,k.vertical?"arrow-down":"arrow-right",{key:0}):e.createCommentVNode("",!0),k.$slots[k.vertical?"arrow-down":"arrow-right"]?e.createCommentVNode("",!0):(e.openBlock(),e.createBlock(e.resolveDynamicComponent(k.vertical?e.unref(Tt):e.unref(St)),{key:1}))]),_:3},8,["disabled","aria-label","class"])):e.createCommentVNode("",!0)])],64))])}}}),or={class:"dp__calendar_header",role:"row"},sr={key:0,class:"dp__calendar_header_item",role:"gridcell"},ir=["aria-label"],ur={key:0,class:"dp__calendar_item dp__week_num",role:"gridcell"},cr={class:"dp__cell_inner"},dr=["id","aria-pressed","aria-disabled","aria-label","tabindex","data-test-id","onClick","onTouchend","onKeydown","onMouseenter","onMouseleave","onMousedown"],fr=e.defineComponent({compatConfig:{MODE:3},__name:"DpCalendar",props:{mappedDates:{type:Array,default:()=>[]},instance:{type:Number,default:0},month:{type:Number,default:0},year:{type:Number,default:0},...ze},emits:["select-date","set-hover-date","handle-scroll","mount","handle-swipe","handle-space","tooltip-open","tooltip-close"],setup(t,{expose:n,emit:o}){const a=o,r=t,{buildMultiLevelMatrix:u}=qe(),{defaultedTransitions:d,defaultedConfig:h,defaultedAriaLabels:c,defaultedMultiCalendars:E,defaultedWeekNumbers:f,defaultedMultiDates:P,defaultedUI:g}=be(r),$=e.ref(null),U=e.ref({bottom:"",left:"",transform:""}),_=e.ref([]),H=e.ref(null),m=e.ref(!0),Y=e.ref(""),w=e.ref({startX:0,endX:0,startY:0,endY:0}),L=e.ref([]),q=e.ref({left:"50%"}),J=e.ref(!1),X=e.computed(()=>r.calendar?r.calendar(r.mappedDates):r.mappedDates),T=e.computed(()=>r.dayNames?Array.isArray(r.dayNames)?r.dayNames:r.dayNames(r.locale,+r.weekStart):jn(r.formatLocale,r.locale,+r.weekStart));e.onMounted(()=>{a("mount",{cmp:"calendar",refs:_}),h.value.noSwipe||H.value&&(H.value.addEventListener("touchstart",ne,{passive:!1}),H.value.addEventListener("touchend",C,{passive:!1}),H.value.addEventListener("touchmove",O,{passive:!1})),r.monthChangeOnScroll&&H.value&&H.value.addEventListener("wheel",ae,{passive:!1})});const G=S=>S?r.vertical?"vNext":"next":r.vertical?"vPrevious":"previous",R=(S,de)=>{if(r.transitions){const ce=Be(Ue(j(),r.month,r.year));Y.value=we(Be(Ue(j(),S,de)),ce)?d.value[G(!0)]:d.value[G(!1)],m.value=!1,e.nextTick(()=>{m.value=!0})}},K=e.computed(()=>({...g.value.calendar??{}})),se=e.computed(()=>S=>{const de=xn(S);return{dp__marker_dot:de.type==="dot",dp__marker_line:de.type==="line"}}),ue=e.computed(()=>S=>pe(S,$.value)),p=e.computed(()=>({dp__calendar:!0,dp__calendar_next:E.value.count>0&&r.instance!==0})),z=e.computed(()=>S=>r.hideOffsetDates?S.current:!0),F=async(S,de)=>{const{width:ce,height:te}=S.getBoundingClientRect();$.value=de.value;let s={left:`${ce/2}px`},V=-50;if(await e.nextTick(),L.value[0]){const{left:oe,width:N}=L.value[0].getBoundingClientRect();oe<0&&(s={left:"0"},V=0,q.value.left=`${ce/2}px`),window.innerWidth<oe+N&&(s={right:"0"},V=0,q.value.left=`${N-ce/2}px`)}U.value={bottom:`${te}px`,...s,transform:`translateX(${V}%)`}},y=async(S,de,ce)=>{var s,V,oe;const te=Se(_.value[de][ce]);te&&((s=S.marker)!=null&&s.customPosition&&((oe=(V=S.marker)==null?void 0:V.tooltip)!=null&&oe.length)?U.value=S.marker.customPosition(te):await F(te,S),a("tooltip-open",S.marker))},W=async(S,de,ce)=>{var te,s;if(J.value&&P.value.enabled&&P.value.dragSelect)return a("select-date",S);if(a("set-hover-date",S),(s=(te=S.marker)==null?void 0:te.tooltip)!=null&&s.length){if(r.hideOffsetDates&&!S.current)return;await y(S,de,ce)}},B=S=>{$.value&&($.value=null,U.value=JSON.parse(JSON.stringify({bottom:"",left:"",transform:""})),a("tooltip-close",S.marker))},ne=S=>{w.value.startX=S.changedTouches[0].screenX,w.value.startY=S.changedTouches[0].screenY},C=S=>{w.value.endX=S.changedTouches[0].screenX,w.value.endY=S.changedTouches[0].screenY,k()},O=S=>{r.vertical&&!r.inline&&S.preventDefault()},k=()=>{const S=r.vertical?"Y":"X";Math.abs(w.value[`start${S}`]-w.value[`end${S}`])>10&&a("handle-swipe",w.value[`start${S}`]>w.value[`end${S}`]?"right":"left")},Q=(S,de,ce)=>{S&&(Array.isArray(_.value[de])?_.value[de][ce]=S:_.value[de]=[S]),r.arrowNavigation&&u(_.value,"calendar")},ae=S=>{r.monthChangeOnScroll&&(S.preventDefault(),a("handle-scroll",S))},A=S=>f.value.type==="local"?i.getWeek(S.value,{weekStartsOn:+r.weekStart}):f.value.type==="iso"?i.getISOWeek(S.value):typeof f.value.type=="function"?f.value.type(S.value):"",Z=S=>{const de=S[0];return f.value.hideOnOffsetDates?S.some(ce=>ce.current)?A(de):"":A(de)},l=(S,de,ce=!0)=>{!ce&&Jn()||(!P.value.enabled||h.value.allowPreventDefault)&&(je(S,h.value),a("select-date",de))},D=S=>{je(S,h.value)},re=S=>{P.value.enabled&&P.value.dragSelect?(J.value=!0,a("select-date",S)):P.value.enabled&&a("select-date",S)};return n({triggerTransition:R}),(S,de)=>(e.openBlock(),e.createElementBlock("div",{class:e.normalizeClass(p.value)},[e.createElementVNode("div",{ref_key:"calendarWrapRef",ref:H,class:e.normalizeClass(K.value),role:"grid"},[e.createElementVNode("div",or,[S.weekNumbers?(e.openBlock(),e.createElementBlock("div",sr,e.toDisplayString(S.weekNumName),1)):e.createCommentVNode("",!0),(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(T.value,(ce,te)=>{var s,V;return e.openBlock(),e.createElementBlock("div",{key:te,class:"dp__calendar_header_item",role:"gridcell","data-test-id":"calendar-header","aria-label":(V=(s=e.unref(c))==null?void 0:s.weekDay)==null?void 0:V.call(s,te)},[S.$slots["calendar-header"]?e.renderSlot(S.$slots,"calendar-header",{key:0,day:ce,index:te}):e.createCommentVNode("",!0),S.$slots["calendar-header"]?e.createCommentVNode("",!0):(e.openBlock(),e.createElementBlock(e.Fragment,{key:1},[e.createTextVNode(e.toDisplayString(ce),1)],64))],8,ir)}),128))]),de[2]||(de[2]=e.createElementVNode("div",{class:"dp__calendar_header_separator"},null,-1)),e.createVNode(e.Transition,{name:Y.value,css:!!S.transitions},{default:e.withCtx(()=>[m.value?(e.openBlock(),e.createElementBlock("div",{key:0,class:"dp__calendar",role:"rowgroup",onMouseleave:de[1]||(de[1]=ce=>J.value=!1)},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(X.value,(ce,te)=>(e.openBlock(),e.createElementBlock("div",{key:te,class:"dp__calendar_row",role:"row"},[S.weekNumbers?(e.openBlock(),e.createElementBlock("div",ur,[e.createElementVNode("div",cr,e.toDisplayString(Z(ce.days)),1)])):e.createCommentVNode("",!0),(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(ce.days,(s,V)=>{var oe,N,me;return e.openBlock(),e.createElementBlock("div",{id:e.unref(Ht)(s.value),ref_for:!0,ref:fe=>Q(fe,te,V),key:V+te,role:"gridcell",class:"dp__calendar_item","aria-pressed":(s.classData.dp__active_date||s.classData.dp__range_start||s.classData.dp__range_start)??void 0,"aria-disabled":s.classData.dp__cell_disabled||void 0,"aria-label":(N=(oe=e.unref(c))==null?void 0:oe.day)==null?void 0:N.call(oe,s),tabindex:!s.current&&S.hideOffsetDates?void 0:0,"data-test-id":e.unref(Ht)(s.value),onClick:e.withModifiers(fe=>l(fe,s),["prevent"]),onTouchend:fe=>l(fe,s,!1),onKeydown:fe=>e.unref(Re)(fe,()=>S.$emit("select-date",s)),onMouseenter:fe=>W(s,te,V),onMouseleave:fe=>B(s),onMousedown:fe=>re(s),onMouseup:de[0]||(de[0]=fe=>J.value=!1)},[e.createElementVNode("div",{class:e.normalizeClass(["dp__cell_inner",s.classData])},[S.$slots.day&&z.value(s)?e.renderSlot(S.$slots,"day",{key:0,day:+s.text,date:s.value}):e.createCommentVNode("",!0),S.$slots.day?e.createCommentVNode("",!0):(e.openBlock(),e.createElementBlock(e.Fragment,{key:1},[e.createTextVNode(e.toDisplayString(s.text),1)],64)),s.marker&&z.value(s)?(e.openBlock(),e.createElementBlock(e.Fragment,{key:2},[S.$slots.marker?e.renderSlot(S.$slots,"marker",{key:0,marker:s.marker,day:+s.text,date:s.value}):(e.openBlock(),e.createElementBlock("div",{key:1,class:e.normalizeClass(se.value(s.marker)),style:e.normalizeStyle(s.marker.color?{backgroundColor:s.marker.color}:{})},null,6))],64)):e.createCommentVNode("",!0),ue.value(s.value)?(e.openBlock(),e.createElementBlock("div",{key:3,ref_for:!0,ref_key:"activeTooltip",ref:L,class:"dp__marker_tooltip",style:e.normalizeStyle(U.value)},[(me=s.marker)!=null&&me.tooltip?(e.openBlock(),e.createElementBlock("div",{key:0,class:"dp__tooltip_content",onClick:D},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(s.marker.tooltip,(fe,ge)=>(e.openBlock(),e.createElementBlock("div",{key:ge,class:"dp__tooltip_text"},[S.$slots["marker-tooltip"]?e.renderSlot(S.$slots,"marker-tooltip",{key:0,tooltip:fe,day:s.value}):e.createCommentVNode("",!0),S.$slots["marker-tooltip"]?e.createCommentVNode("",!0):(e.openBlock(),e.createElementBlock(e.Fragment,{key:1},[e.createElementVNode("div",{class:"dp__tooltip_mark",style:e.normalizeStyle(fe.color?{backgroundColor:fe.color}:{})},null,4),e.createElementVNode("div",null,e.toDisplayString(fe.text),1)],64))]))),128)),e.createElementVNode("div",{class:"dp__arrow_bottom_tp",style:e.normalizeStyle(q.value)},null,4)])):e.createCommentVNode("",!0)],4)):e.createCommentVNode("",!0)],2)],40,dr)}),128))]))),128))],32)):e.createCommentVNode("",!0)]),_:3},8,["name","css"])],2)],2))}}),Yn=t=>Array.isArray(t),mr=(t,n,o,a)=>{const r=e.ref([]),u=e.ref(new Date),d=e.ref(),h=()=>C(t.isTextInputDate),{modelValue:c,calendars:E,time:f,today:P}=ft(t,n,h),{defaultedMultiCalendars:g,defaultedStartTime:$,defaultedRange:U,defaultedConfig:_,defaultedTz:H,propDates:m,defaultedMultiDates:Y}=be(t),{validateMonthYearInRange:w,isDisabled:L,isDateRangeAllowed:q,checkMinMaxRange:J}=Qe(t),{updateTimeValues:X,getSetDateTime:T,setTime:G,assignStartTime:R,validateTime:K,disabledTimesConfig:se}=On(t,f,c,a),ue=e.computed(()=>b=>E.value[b]?E.value[b].month:0),p=e.computed(()=>b=>E.value[b]?E.value[b].year:0),z=b=>!_.value.keepViewOnOffsetClick||b?!0:!d.value,F=(b,ee,M,x=!1)=>{var le,Ne;z(x)&&(E.value[b]||(E.value[b]={month:0,year:0}),E.value[b].month=fn(ee)?(le=E.value[b])==null?void 0:le.month:ee,E.value[b].year=fn(M)?(Ne=E.value[b])==null?void 0:Ne.year:M)},y=()=>{t.autoApply&&n("select-date")},W=()=>{$.value&&R($.value)};e.onMounted(()=>{t.shadow||(c.value||(de(),W()),C(!0),t.focusStartDate&&t.startDate&&de())});const B=e.computed(()=>{var b;return(b=t.flow)!=null&&b.length&&!t.partialFlow?t.flowStep===t.flow.length:!0}),ne=()=>{t.autoApply&&B.value&&n("auto-apply",t.partialFlow?t.flowStep!==t.flow.length:!1)},C=(b=!1)=>{if(c.value)return Array.isArray(c.value)?(r.value=c.value,l(b)):Q(c.value,b);if(g.value.count&&b&&!t.startDate)return k(j(),b)},O=()=>Array.isArray(c.value)&&U.value.enabled?i.getMonth(c.value[0])===i.getMonth(c.value[1]??c.value[0]):!1,k=(b=new Date,ee=!1)=>{if((!g.value.count||!g.value.static||ee)&&F(0,i.getMonth(b),i.getYear(b)),g.value.count&&(!c.value||O()||!g.value.solo)&&(!g.value.solo||ee))for(let M=1;M<g.value.count;M++){const x=i.set(j(),{month:ue.value(M-1),year:p.value(M-1)}),le=i.add(x,{months:1});E.value[M]={month:i.getMonth(le),year:i.getYear(le)}}},Q=(b,ee)=>{k(b),G("hours",i.getHours(b)),G("minutes",i.getMinutes(b)),G("seconds",i.getSeconds(b)),g.value.count&&ee&&S()},ae=b=>{if(g.value.count){if(g.value.solo)return 0;const ee=i.getMonth(b[0]),M=i.getMonth(b[1]);return Math.abs(M-ee)<g.value.count?0:1}return 1},A=(b,ee)=>{b[1]&&U.value.showLastInRange?k(b[ae(b)],ee):k(b[0],ee);const M=(x,le)=>[x(b[0]),b[1]?x(b[1]):f[le][1]];G("hours",M(i.getHours,"hours")),G("minutes",M(i.getMinutes,"minutes")),G("seconds",M(i.getSeconds,"seconds"))},Z=(b,ee)=>{if((U.value.enabled||t.weekPicker)&&!Y.value.enabled)return A(b,ee);if(Y.value.enabled&&ee){const M=b[b.length-1];return Q(M,ee)}},l=b=>{const ee=c.value;Z(ee,b),g.value.count&&g.value.solo&&S()},D=(b,ee)=>{const M=i.set(j(),{month:ue.value(ee),year:p.value(ee)}),x=b<0?i.addMonths(M,1):i.subMonths(M,1);w(i.getMonth(x),i.getYear(x),b<0,t.preventMinMaxNavigation)&&(F(ee,i.getMonth(x),i.getYear(x)),n("update-month-year",{instance:ee,month:i.getMonth(x),year:i.getYear(x)}),g.value.count&&!g.value.solo&&re(ee),o())},re=b=>{for(let ee=b-1;ee>=0;ee--){const M=i.subMonths(i.set(j(),{month:ue.value(ee+1),year:p.value(ee+1)}),1);F(ee,i.getMonth(M),i.getYear(M))}for(let ee=b+1;ee<=g.value.count-1;ee++){const M=i.addMonths(i.set(j(),{month:ue.value(ee-1),year:p.value(ee-1)}),1);F(ee,i.getMonth(M),i.getYear(M))}},S=()=>{if(Array.isArray(c.value)&&c.value.length===2){const b=j(j(c.value[1]?c.value[1]:i.addMonths(c.value[0],1))),[ee,M]=[i.getMonth(c.value[0]),i.getYear(c.value[0])],[x,le]=[i.getMonth(c.value[1]),i.getYear(c.value[1])];(ee!==x||ee===x&&M!==le)&&g.value.solo&&F(1,i.getMonth(b),i.getYear(b))}else c.value&&!Array.isArray(c.value)&&(F(0,i.getMonth(c.value),i.getYear(c.value)),k(j()))},de=()=>{t.startDate&&(F(0,i.getMonth(j(t.startDate)),i.getYear(j(t.startDate))),g.value.count&&re(0))},ce=(b,ee)=>{if(t.monthChangeOnScroll){const M=new Date().getTime()-u.value.getTime(),x=Math.abs(b.deltaY);let le=500;x>1&&(le=100),x>100&&(le=0),M>le&&(u.value=new Date,D(t.monthChangeOnScroll!=="inverse"?-b.deltaY:b.deltaY,ee))}},te=(b,ee,M=!1)=>{t.monthChangeOnArrows&&t.vertical===M&&s(b,ee)},s=(b,ee)=>{D(b==="right"?-1:1,ee)},V=b=>{if(m.value.markers)return pt(b.value,m.value.markers)},oe=(b,ee)=>{switch(t.sixWeeks===!0?"append":t.sixWeeks){case"prepend":return[!0,!1];case"center":return[b==0,!0];case"fair":return[b==0||ee>b,!0];case"append":return[!1,!1];default:return[!1,!1]}},N=(b,ee,M,x)=>{if(t.sixWeeks&&b.length<6){const le=6-b.length,Ne=(ee.getDay()+7-x)%7,lt=6-(M.getDay()+7-x)%7,[mt,rn]=oe(Ne,lt);for(let Ze=1;Ze<=le;Ze++)if(rn?!!(Ze%2)==mt:mt){const Mt=b[0].days[0],ln=me(i.addDays(Mt.value,-7),i.getMonth(ee));b.unshift({days:ln})}else{const Mt=b[b.length-1],ln=Mt.days[Mt.days.length-1],Kr=me(i.addDays(ln.value,1),i.getMonth(ee));b.push({days:Kr})}}return b},me=(b,ee)=>{const M=j(b),x=[];for(let le=0;le<7;le++){const Ne=i.addDays(M,le),Le=i.getMonth(Ne)!==ee;x.push({text:t.hideOffsetDates&&Le?"":Ne.getDate(),value:Ne,current:!Le,classData:{}})}return x},fe=(b,ee)=>{const M=[],x=new Date(ee,b),le=new Date(ee,b+1,0),Ne=t.weekStart,Le=i.startOfWeek(x,{weekStartsOn:Ne}),lt=mt=>{const rn=me(mt,b);if(M.push({days:rn}),!M[M.length-1].days.some(Ze=>pe(Be(Ze.value),Be(le)))){const Ze=i.addDays(mt,7);lt(Ze)}};return lt(Le),N(M,x,le,Ne)},ge=b=>{const ee=Ke(j(b.value),f.hours,f.minutes,Te());n("date-update",ee),Y.value.enabled?qt(ee,c,Y.value.limit):c.value=ee,a(),e.nextTick().then(()=>{ne()})},v=b=>U.value.noDisabledRange?bn(r.value[0],b).some(M=>L(M)):!1,I=()=>{r.value=c.value?c.value.slice():[],r.value.length===2&&!(U.value.fixedStart||U.value.fixedEnd)&&(r.value=[])},ye=(b,ee)=>{const M=[j(b.value),i.addDays(j(b.value),+U.value.autoRange)];q(M)?(ee&&Ce(b.value),r.value=M):n("invalid-date",b.value)},Ce=b=>{const ee=i.getMonth(j(b)),M=i.getYear(j(b));if(F(0,ee,M),g.value.count>0)for(let x=1;x<g.value.count;x++){const le=ta(i.set(j(b),{year:p.value(x-1),month:ue.value(x-1)}));F(x,le.month,le.year)}},We=b=>{if(v(b.value)||!J(b.value,c.value,U.value.fixedStart?0:1))return n("invalid-date",b.value);r.value=Rn(j(b.value),c,n,U)},ie=(b,ee)=>{if(I(),U.value.autoRange)return ye(b,ee);if(U.value.fixedStart||U.value.fixedEnd)return We(b);r.value[0]?J(j(b.value),c.value)&&!v(b.value)?ve(j(b.value),j(r.value[0]))?(r.value.unshift(j(b.value)),n("range-end",r.value[0])):(r.value[1]=j(b.value),n("range-end",r.value[1])):(t.autoApply&&n("auto-apply-invalid",b.value),n("invalid-date",b.value)):(r.value[0]=j(b.value),n("range-start",r.value[0]))},Te=(b=!0)=>t.enableSeconds?Array.isArray(f.seconds)?b?f.seconds[0]:f.seconds[1]:f.seconds:0,Ye=b=>{r.value[b]=Ke(r.value[b],f.hours[b],f.minutes[b],Te(b!==1))},Jt=()=>{var b,ee;r.value[0]&&r.value[1]&&+((b=r.value)==null?void 0:b[0])>+((ee=r.value)==null?void 0:ee[1])&&(r.value.reverse(),n("range-start",r.value[0]),n("range-end",r.value[1]))},wt=()=>{r.value.length&&(r.value[0]&&!r.value[1]?Ye(0):(Ye(0),Ye(1),a()),Jt(),c.value=r.value.slice(),bt(r.value,n,t.autoApply,t.modelAuto))},Zt=(b,ee=!1)=>{if(L(b.value)||!b.current&&t.hideOffsetDates)return n("invalid-date",b.value);if(d.value=JSON.parse(JSON.stringify(b)),!U.value.enabled)return ge(b);Yn(f.hours)&&Yn(f.minutes)&&!Y.value.enabled&&(ie(b,ee),wt())},Ft=(b,ee)=>{var x;F(b,ee.month,ee.year,!0),g.value.count&&!g.value.solo&&re(b),n("update-month-year",{instance:b,month:ee.month,year:ee.year}),o(g.value.solo?b:void 0);const M=(x=t.flow)!=null&&x.length?t.flow[t.flowStep]:void 0;!ee.fromNav&&(M===$e.month||M===$e.year)&&a()},en=(b,ee)=>{Pn({value:b,modelValue:c,range:U.value.enabled,timezone:ee?void 0:H.value.timezone}),y(),t.multiCalendars&&e.nextTick().then(()=>C(!0))},tn=()=>{const b=$t(j(),H.value);!U.value.enabled&&!Y.value.enabled?c.value=b:c.value&&Array.isArray(c.value)&&c.value[0]?Y.value.enabled?c.value=[...c.value,b]:c.value=ve(b,c.value[0])?[b,c.value[0]]:[c.value[0],b]:c.value=[b],y()},nn=()=>{if(Array.isArray(c.value))if(Y.value.enabled){const b=an();c.value[c.value.length-1]=T(b)}else c.value=c.value.map((b,ee)=>b&&T(b,ee));else c.value=T(c.value);n("time-update")},an=()=>Array.isArray(c.value)&&c.value.length?c.value[c.value.length-1]:null;return{calendars:E,modelValue:c,month:ue,year:p,time:f,disabledTimesConfig:se,today:P,validateTime:K,getCalendarDays:fe,getMarker:V,handleScroll:ce,handleSwipe:s,handleArrow:te,selectDate:Zt,updateMonthYear:Ft,presetDate:en,selectCurrentDate:tn,updateTime:(b,ee=!0,M=!1)=>{X(b,ee,M,nn)},assignMonthAndYear:k,setStartTime:W}},pr={key:0},gr=e.defineComponent({__name:"DatePicker",props:{...ze},emits:["tooltip-open","tooltip-close","mount","update:internal-model-value","update-flow-step","reset-flow","auto-apply","focus-menu","select-date","range-start","range-end","invalid-fixed-range","time-update","am-pm-change","time-picker-open","time-picker-close","recalculate-position","update-month-year","auto-apply-invalid","date-update","invalid-date","overlay-toggle"],setup(t,{expose:n,emit:o}){const a=o,r=t,{calendars:u,month:d,year:h,modelValue:c,time:E,disabledTimesConfig:f,today:P,validateTime:g,getCalendarDays:$,getMarker:U,handleArrow:_,handleScroll:H,handleSwipe:m,selectDate:Y,updateMonthYear:w,presetDate:L,selectCurrentDate:q,updateTime:J,assignMonthAndYear:X,setStartTime:T}=mr(r,a,O,k),G=e.useSlots(),{setHoverDate:R,getDayClassData:K,clearHoverDate:se}=Nr(c,r),{defaultedMultiCalendars:ue}=be(r),p=e.ref([]),z=e.ref([]),F=e.ref(null),y=Oe(G,"calendar"),W=Oe(G,"monthYear"),B=Oe(G,"timePicker"),ne=te=>{r.shadow||a("mount",te)};e.watch(u,()=>{r.shadow||setTimeout(()=>{a("recalculate-position")},0)},{deep:!0}),e.watch(ue,(te,s)=>{te.count-s.count>0&&X()},{deep:!0});const C=e.computed(()=>te=>$(d.value(te),h.value(te)).map(s=>({...s,days:s.days.map(V=>(V.marker=U(V),V.classData=K(V),V))})));function O(te){var s;te||te===0?(s=z.value[te])==null||s.triggerTransition(d.value(te),h.value(te)):z.value.forEach((V,oe)=>V.triggerTransition(d.value(oe),h.value(oe)))}function k(){a("update-flow-step")}const Q=(te,s=!1)=>{Y(te,s),r.spaceConfirm&&a("select-date")},ae=(te,s,V=0)=>{var oe;(oe=p.value[V])==null||oe.toggleMonthPicker(te,s)},A=(te,s,V=0)=>{var oe;(oe=p.value[V])==null||oe.toggleYearPicker(te,s)},Z=(te,s,V)=>{var oe;(oe=F.value)==null||oe.toggleTimePicker(te,s,V)},l=(te,s)=>{var V;if(!r.range){const oe=c.value?c.value:P,N=s?new Date(s):oe,me=te?i.startOfWeek(N,{weekStartsOn:1}):i.endOfWeek(N,{weekStartsOn:1});Y({value:me,current:i.getMonth(N)===d.value(0),text:"",classData:{}}),(V=document.getElementById(Ht(me)))==null||V.focus()}},D=te=>{var s;(s=p.value[0])==null||s.handleMonthYearChange(te,!0)},re=te=>{w(0,{month:d.value(0),year:h.value(0)+(te?1:-1),fromNav:!0})},S=(te,s)=>{te===$e.time&&a(`time-picker-${s?"open":"close"}`),a("overlay-toggle",{open:s,overlay:te})},de=te=>{a("overlay-toggle",{open:!1,overlay:te}),a("focus-menu")};return n({clearHoverDate:se,presetDate:L,selectCurrentDate:q,toggleMonthPicker:ae,toggleYearPicker:A,toggleTimePicker:Z,handleArrow:_,updateMonthYear:w,getSidebarProps:()=>({modelValue:c,month:d,year:h,time:E,updateTime:J,updateMonthYear:w,selectDate:Y,presetDate:L}),changeMonth:D,changeYear:re,selectWeekDate:l,setStartTime:T}),(te,s)=>(e.openBlock(),e.createElementBlock(e.Fragment,null,[e.createVNode(kt,{"multi-calendars":e.unref(ue).count,collapse:te.collapse,"is-mobile":te.isMobile},{default:e.withCtx(({instance:V,index:oe})=>[te.disableMonthYearSelect?e.createCommentVNode("",!0):(e.openBlock(),e.createBlock(lr,e.mergeProps({key:0,ref:N=>{N&&(p.value[oe]=N)},months:e.unref(un)(te.formatLocale,te.locale,te.monthNameFormat),years:e.unref(Pt)(te.yearRange,te.locale,te.reverseYears),month:e.unref(d)(V),year:e.unref(h)(V),instance:V},te.$props,{onMount:s[0]||(s[0]=N=>ne(e.unref(Xe).header)),onResetFlow:s[1]||(s[1]=N=>te.$emit("reset-flow")),onUpdateMonthYear:N=>e.unref(w)(V,N),onOverlayClosed:de,onOverlayOpened:s[2]||(s[2]=N=>te.$emit("overlay-toggle",{open:!0,overlay:N}))}),e.createSlots({_:2},[e.renderList(e.unref(W),(N,me)=>({name:N,fn:e.withCtx(fe=>[e.renderSlot(te.$slots,N,e.normalizeProps(e.guardReactiveProps(fe)))])}))]),1040,["months","years","month","year","instance","onUpdateMonthYear"])),e.createVNode(fr,e.mergeProps({ref:N=>{N&&(z.value[oe]=N)},"mapped-dates":C.value(V),month:e.unref(d)(V),year:e.unref(h)(V),instance:V},te.$props,{onSelectDate:N=>e.unref(Y)(N,V!==1),onHandleSpace:N=>Q(N,V!==1),onSetHoverDate:s[3]||(s[3]=N=>e.unref(R)(N)),onHandleScroll:N=>e.unref(H)(N,V),onHandleSwipe:N=>e.unref(m)(N,V),onMount:s[4]||(s[4]=N=>ne(e.unref(Xe).calendar)),onResetFlow:s[5]||(s[5]=N=>te.$emit("reset-flow")),onTooltipOpen:s[6]||(s[6]=N=>te.$emit("tooltip-open",N)),onTooltipClose:s[7]||(s[7]=N=>te.$emit("tooltip-close",N))}),e.createSlots({_:2},[e.renderList(e.unref(y),(N,me)=>({name:N,fn:e.withCtx(fe=>[e.renderSlot(te.$slots,N,e.normalizeProps(e.guardReactiveProps({...fe})))])}))]),1040,["mapped-dates","month","year","instance","onSelectDate","onHandleSpace","onHandleScroll","onHandleSwipe"])]),_:3},8,["multi-calendars","collapse","is-mobile"]),te.enableTimePicker?(e.openBlock(),e.createElementBlock("div",pr,[te.$slots["time-picker"]?e.renderSlot(te.$slots,"time-picker",e.normalizeProps(e.mergeProps({key:0},{time:e.unref(E),updateTime:e.unref(J)}))):(e.openBlock(),e.createBlock(En,e.mergeProps({key:1,ref_key:"timePickerRef",ref:F},te.$props,{hours:e.unref(E).hours,minutes:e.unref(E).minutes,seconds:e.unref(E).seconds,"internal-model-value":te.internalModelValue,"disabled-times-config":e.unref(f),"validate-time":e.unref(g),onMount:s[8]||(s[8]=V=>ne(e.unref(Xe).timePicker)),"onUpdate:hours":s[9]||(s[9]=V=>e.unref(J)(V)),"onUpdate:minutes":s[10]||(s[10]=V=>e.unref(J)(V,!1)),"onUpdate:seconds":s[11]||(s[11]=V=>e.unref(J)(V,!1,!0)),onResetFlow:s[12]||(s[12]=V=>te.$emit("reset-flow")),onOverlayClosed:s[13]||(s[13]=V=>S(V,!1)),onOverlayOpened:s[14]||(s[14]=V=>S(V,!0)),onAmPmChange:s[15]||(s[15]=V=>te.$emit("am-pm-change",V))}),e.createSlots({_:2},[e.renderList(e.unref(B),(V,oe)=>({name:V,fn:e.withCtx(N=>[e.renderSlot(te.$slots,V,e.normalizeProps(e.guardReactiveProps(N)))])}))]),1040,["hours","minutes","seconds","internal-model-value","disabled-times-config","validate-time"]))])):e.createCommentVNode("",!0)],64))}}),yr=(t,n)=>{const o=e.ref(),{defaultedMultiCalendars:a,defaultedConfig:r,defaultedHighlight:u,defaultedRange:d,propDates:h,defaultedFilters:c,defaultedMultiDates:E}=be(t),{modelValue:f,year:P,month:g,calendars:$}=ft(t,n),{isDisabled:U}=Qe(t),{selectYear:_,groupedYears:H,showYearPicker:m,isDisabled:Y,toggleYearPicker:w,handleYearSelect:L,handleYear:q}=Nn({modelValue:f,multiCalendars:a,range:d,highlight:u,calendars:$,propDates:h,month:g,year:P,filters:c,props:t,emit:n}),J=(y,W)=>[y,W].map(B=>i.format(B,"MMMM",{locale:t.formatLocale})).join("-"),X=e.computed(()=>y=>f.value?Array.isArray(f.value)?f.value.some(W=>i.isSameQuarter(y,W)):i.isSameQuarter(f.value,y):!1),T=y=>{if(d.value.enabled){if(Array.isArray(f.value)){const W=pe(y,f.value[0])||pe(y,f.value[1]);return st(f.value,o.value,y)&&!W}return!1}return!1},G=(y,W)=>y.quarter===i.getQuarter(W)&&y.year===i.getYear(W),R=y=>typeof u.value=="function"?u.value({quarter:i.getQuarter(y),year:i.getYear(y)}):!!u.value.quarters.find(W=>G(W,y)),K=e.computed(()=>y=>{const W=i.set(new Date,{year:P.value(y)});return i.eachQuarterOfInterval({start:i.startOfYear(W),end:i.endOfYear(W)}).map(B=>{const ne=i.startOfQuarter(B),C=i.endOfQuarter(B),O=U(B),k=T(ne),Q=R(ne);return{text:J(ne,C),value:ne,active:X.value(ne),highlighted:Q,disabled:O,isBetween:k}})}),se=y=>{qt(y,f,E.value.limit),n("auto-apply",!0)},ue=y=>{f.value=Gt(f,y,n),bt(f.value,n,t.autoApply,t.modelAuto)},p=y=>{f.value=y,n("auto-apply")};return{defaultedConfig:r,defaultedMultiCalendars:a,groupedYears:H,year:P,isDisabled:Y,quarters:K,showYearPicker:m,modelValue:f,setHoverDate:y=>{o.value=y},selectYear:_,selectQuarter:(y,W,B)=>{if(!B)return $.value[W].month=i.getMonth(i.endOfQuarter(y)),E.value.enabled?se(y):d.value.enabled?ue(y):p(y)},toggleYearPicker:w,handleYearSelect:L,handleYear:q}},hr={class:"dp--quarter-items"},kr=["data-test-id","disabled","onClick","onMouseover"],br=e.defineComponent({compatConfig:{MODE:3},__name:"QuarterPicker",props:{...ze},emits:["update:internal-model-value","reset-flow","overlay-closed","auto-apply","range-start","range-end","overlay-toggle","update-month-year"],setup(t,{expose:n,emit:o}){const a=o,r=t,u=e.useSlots(),d=Oe(u,"yearMode"),{defaultedMultiCalendars:h,defaultedConfig:c,groupedYears:E,year:f,isDisabled:P,quarters:g,modelValue:$,showYearPicker:U,setHoverDate:_,selectQuarter:H,toggleYearPicker:m,handleYearSelect:Y,handleYear:w}=yr(r,a);return n({getSidebarProps:()=>({modelValue:$,year:f,selectQuarter:H,handleYearSelect:Y,handleYear:w})}),(q,J)=>(e.openBlock(),e.createBlock(kt,{"multi-calendars":e.unref(h).count,collapse:q.collapse,stretch:"","is-mobile":q.isMobile},{default:e.withCtx(({instance:X})=>[e.createElementVNode("div",{class:"dp-quarter-picker-wrap",style:e.normalizeStyle({minHeight:`${e.unref(c).modeHeight}px`})},[q.$slots["top-extra"]?e.renderSlot(q.$slots,"top-extra",{key:0,value:q.internalModelValue}):e.createCommentVNode("",!0),e.createElementVNode("div",null,[e.createVNode(An,e.mergeProps(q.$props,{items:e.unref(E)(X),instance:X,"show-year-picker":e.unref(U)[X],year:e.unref(f)(X),"is-disabled":T=>e.unref(P)(X,T),onHandleYear:T=>e.unref(w)(X,T),onYearSelect:T=>e.unref(Y)(T,X),onToggleYearPicker:T=>e.unref(m)(X,T==null?void 0:T.flow,T==null?void 0:T.show)}),e.createSlots({_:2},[e.renderList(e.unref(d),(T,G)=>({name:T,fn:e.withCtx(R=>[e.renderSlot(q.$slots,T,e.normalizeProps(e.guardReactiveProps(R)))])}))]),1040,["items","instance","show-year-picker","year","is-disabled","onHandleYear","onYearSelect","onToggleYearPicker"])]),e.createElementVNode("div",hr,[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(e.unref(g)(X),(T,G)=>(e.openBlock(),e.createElementBlock("div",{key:G},[e.createElementVNode("button",{type:"button",class:e.normalizeClass(["dp--qr-btn",{"dp--qr-btn-active":T.active,"dp--qr-btn-between":T.isBetween,"dp--qr-btn-disabled":T.disabled,"dp--highlighted":T.highlighted}]),"data-test-id":T.value,disabled:T.disabled,onClick:R=>e.unref(H)(T.value,X,T.disabled),onMouseover:R=>e.unref(_)(T.value)},[q.$slots.quarter?e.renderSlot(q.$slots,"quarter",{key:0,value:T.value,text:T.text}):(e.openBlock(),e.createElementBlock(e.Fragment,{key:1},[e.createTextVNode(e.toDisplayString(T.text),1)],64))],42,kr)]))),128))])],4)]),_:3},8,["multi-calendars","collapse","is-mobile"]))}}),Vn=(t,n)=>{const o=e.ref(0);e.onMounted(()=>{a(),window.addEventListener("resize",a,{passive:!0})}),e.onUnmounted(()=>{window.removeEventListener("resize",a)});const a=()=>{o.value=window.document.documentElement.clientWidth};return{isMobile:e.computed(()=>o.value<=t.value.mobileBreakpoint&&!n?!0:void 0)}},vr=["id","tabindex","role","aria-label"],wr={key:0,class:"dp--menu-load-container"},Mr={key:1,class:"dp--menu-header"},Dr=["data-dp-mobile"],Sr={key:0,class:"dp__sidebar_left"},Cr=["data-dp-mobile"],Br=["data-test-id","data-dp-mobile","onClick","onKeydown"],Tr={key:2,class:"dp__sidebar_right"},$r={key:3,class:"dp__action_extra"},_n=e.defineComponent({compatConfig:{MODE:3},__name:"DatepickerMenu",props:{...ht,shadow:{type:Boolean,default:!1},openOnTop:{type:Boolean,default:!1},internalModelValue:{type:[Date,Array],default:null},noOverlayFocus:{type:Boolean,default:!1},collapse:{type:Boolean,default:!1},getInputRect:{type:Function,default:()=>({})},isTextInputDate:{type:Boolean,default:!1}},emits:["close-picker","select-date","auto-apply","time-update","flow-step","update-month-year","invalid-select","update:internal-model-value","recalculate-position","invalid-fixed-range","tooltip-open","tooltip-close","time-picker-open","time-picker-close","am-pm-change","range-start","range-end","auto-apply-invalid","date-update","invalid-date","overlay-toggle","menu-blur"],setup(t,{expose:n,emit:o}){const a=o,r=t,u=e.ref(null),d=e.computed(()=>{const{openOnTop:v,...I}=r;return{...I,isMobile:_.value,flowStep:K.value,menuWrapRef:u.value}}),{setMenuFocused:h,setShiftKey:c,control:E}=Bn(),f=e.useSlots(),{defaultedTextInput:P,defaultedInline:g,defaultedConfig:$,defaultedUI:U}=be(r),{isMobile:_}=Vn($,r.shadow),H=e.ref(null),m=e.ref(0),Y=e.ref(null),w=e.ref(!1),L=e.ref(null),q=e.ref(!1);e.onMounted(()=>{if(!r.shadow){w.value=!0,J(),window.addEventListener("resize",J);const v=Se(u);if(v&&!P.value.enabled&&!g.value.enabled&&(h(!0),W()),v){const I=ye=>{q.value=!0,$.value.allowPreventDefault&&ye.preventDefault(),je(ye,$.value,!0)};v.addEventListener("pointerdown",I),v.addEventListener("mousedown",I)}}document.addEventListener("mousedown",fe)}),e.onUnmounted(()=>{window.removeEventListener("resize",J),document.addEventListener("mousedown",fe)});const J=()=>{const v=Se(Y);v&&(m.value=v.getBoundingClientRect().width)},{arrowRight:X,arrowLeft:T,arrowDown:G,arrowUp:R}=qe(),{flowStep:K,updateFlowStep:se,childMount:ue,resetFlow:p,handleFlow:z}=Er(r,a,L),F=e.computed(()=>r.monthPicker?_a:r.yearPicker?za:r.timePicker?Fa:r.quarterPicker?br:gr),y=e.computed(()=>{var ye;if($.value.arrowLeft)return $.value.arrowLeft;const v=(ye=u.value)==null?void 0:ye.getBoundingClientRect(),I=r.getInputRect();return(I==null?void 0:I.width)<(m==null?void 0:m.value)&&(I==null?void 0:I.left)<=((v==null?void 0:v.left)??0)?`${(I==null?void 0:I.width)/2}px`:(I==null?void 0:I.right)>=((v==null?void 0:v.right)??0)&&(I==null?void 0:I.width)<(m==null?void 0:m.value)?`${(m==null?void 0:m.value)-(I==null?void 0:I.width)/2}px`:"50%"}),W=()=>{const v=Se(u);v&&v.focus({preventScroll:!0})},B=e.computed(()=>{var v;return((v=L.value)==null?void 0:v.getSidebarProps())||{}}),ne=()=>{r.openOnTop&&a("recalculate-position")},C=Oe(f,"action"),O=e.computed(()=>r.monthPicker||r.yearPicker?Oe(f,"monthYear"):r.timePicker?Oe(f,"timePicker"):Oe(f,"shared")),k=e.computed(()=>r.openOnTop?"dp__arrow_bottom":"dp__arrow_top"),Q=e.computed(()=>({dp__menu_disabled:r.disabled,dp__menu_readonly:r.readonly,"dp-menu-loading":r.loading})),ae=e.computed(()=>({dp__menu:!0,dp__menu_index:!g.value.enabled,dp__relative:g.value.enabled,...U.value.menu??{}})),A=v=>{je(v,$.value,!0)},Z=()=>{r.escClose&&a("close-picker")},l=v=>{if(r.arrowNavigation){if(v===Pe.up)return R();if(v===Pe.down)return G();if(v===Pe.left)return T();if(v===Pe.right)return X()}else v===Pe.left||v===Pe.up?ce("handleArrow",Pe.left,0,v===Pe.up):ce("handleArrow",Pe.right,0,v===Pe.down)},D=v=>{c(v.shiftKey),!r.disableMonthYearSelect&&v.code===he.tab&&v.target.classList.contains("dp__menu")&&E.value.shiftKeyInMenu&&(v.preventDefault(),je(v,$.value,!0),a("close-picker"))},re=()=>{W(),a("time-picker-close")},S=v=>{var I,ye,Ce;(I=L.value)==null||I.toggleTimePicker(!1,!1),(ye=L.value)==null||ye.toggleMonthPicker(!1,!1,v),(Ce=L.value)==null||Ce.toggleYearPicker(!1,!1,v)},de=(v,I=0)=>{var ye,Ce,We;return v==="month"?(ye=L.value)==null?void 0:ye.toggleMonthPicker(!1,!0,I):v==="year"?(Ce=L.value)==null?void 0:Ce.toggleYearPicker(!1,!0,I):v==="time"?(We=L.value)==null?void 0:We.toggleTimePicker(!0,!1):S(I)},ce=(v,...I)=>{var ye,Ce;(ye=L.value)!=null&&ye[v]&&((Ce=L.value)==null||Ce[v](...I))},te=()=>{ce("selectCurrentDate")},s=(v,I)=>{ce("presetDate",e.toValue(v),I)},V=()=>{ce("clearHoverDate")},oe=(v,I)=>{ce("updateMonthYear",v,I)},N=(v,I)=>{v.preventDefault(),l(I)},me=v=>{var I,ye,Ce;if(D(v),v.key===he.home||v.key===he.end)return ce("selectWeekDate",v.key===he.home,v.target.getAttribute("id"));switch((v.key===he.pageUp||v.key===he.pageDown)&&(v.shiftKey?(ce("changeYear",v.key===he.pageUp),(I=Nt(u.value,"overlay-year"))==null||I.focus()):(ce("changeMonth",v.key===he.pageUp),(ye=Nt(u.value,v.key===he.pageUp?"action-prev":"action-next"))==null||ye.focus()),v.target.getAttribute("id")&&((Ce=u.value)==null||Ce.focus({preventScroll:!0}))),v.key){case he.esc:return Z();case he.arrowLeft:return N(v,Pe.left);case he.arrowRight:return N(v,Pe.right);case he.arrowUp:return N(v,Pe.up);case he.arrowDown:return N(v,Pe.down);default:return}},fe=v=>{var I;g.value.enabled&&!g.value.input&&!((I=u.value)!=null&&I.contains(v.target))&&q.value&&(q.value=!1,a("menu-blur"))};return n({updateMonthYear:oe,switchView:de,handleFlow:z,onValueCleared:()=>{var v,I;(I=(v=L.value)==null?void 0:v.setStartTime)==null||I.call(v)}}),(v,I)=>{var ye,Ce,We;return e.openBlock(),e.createElementBlock("div",{id:v.uid?`dp-menu-${v.uid}`:void 0,ref_key:"dpMenuRef",ref:u,tabindex:e.unref(g).enabled?void 0:"0",role:e.unref(g).enabled?void 0:"dialog","aria-label":(ye=v.ariaLabels)==null?void 0:ye.menu,class:e.normalizeClass(ae.value),style:e.normalizeStyle({"--dp-arrow-left":y.value}),onMouseleave:V,onClick:A,onKeydown:me},[(v.disabled||v.readonly)&&e.unref(g).enabled||v.loading?(e.openBlock(),e.createElementBlock("div",{key:0,class:e.normalizeClass(Q.value)},[v.loading?(e.openBlock(),e.createElementBlock("div",wr,I[19]||(I[19]=[e.createElementVNode("span",{class:"dp--menu-loader"},null,-1)]))):e.createCommentVNode("",!0)],2)):e.createCommentVNode("",!0),v.$slots["menu-header"]?(e.openBlock(),e.createElementBlock("div",Mr,[e.renderSlot(v.$slots,"menu-header")])):e.createCommentVNode("",!0),!e.unref(g).enabled&&!v.teleportCenter?(e.openBlock(),e.createElementBlock("div",{key:2,class:e.normalizeClass(k.value)},null,2)):e.createCommentVNode("",!0),e.createElementVNode("div",{ref_key:"innerMenuRef",ref:Y,class:e.normalizeClass({dp__menu_content_wrapper:((Ce=v.presetDates)==null?void 0:Ce.length)||!!v.$slots["left-sidebar"]||!!v.$slots["right-sidebar"],"dp--menu-content-wrapper-collapsed":t.collapse&&(((We=v.presetDates)==null?void 0:We.length)||!!v.$slots["left-sidebar"]||!!v.$slots["right-sidebar"])}),"data-dp-mobile":e.unref(_),style:e.normalizeStyle({"--dp-menu-width":`${m.value}px`})},[v.$slots["left-sidebar"]?(e.openBlock(),e.createElementBlock("div",Sr,[e.renderSlot(v.$slots,"left-sidebar",e.normalizeProps(e.guardReactiveProps(B.value)))])):e.createCommentVNode("",!0),v.presetDates.length?(e.openBlock(),e.createElementBlock("div",{key:1,class:e.normalizeClass({"dp--preset-dates-collapsed":t.collapse,"dp--preset-dates":!0}),"data-dp-mobile":e.unref(_)},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(v.presetDates,(ie,Te)=>(e.openBlock(),e.createElementBlock(e.Fragment,{key:Te},[ie.slot?e.renderSlot(v.$slots,ie.slot,{key:0,presetDate:s,label:ie.label,value:ie.value}):(e.openBlock(),e.createElementBlock("button",{key:1,type:"button",style:e.normalizeStyle(ie.style||{}),class:e.normalizeClass(["dp__btn dp--preset-range",{"dp--preset-range-collapsed":t.collapse}]),"data-test-id":ie.testId??void 0,"data-dp-mobile":e.unref(_),onClick:e.withModifiers(Ye=>s(ie.value,ie.noTz),["prevent"]),onKeydown:Ye=>e.unref(Re)(Ye,()=>s(ie.value,ie.noTz),!0)},e.toDisplayString(ie.label),47,Br))],64))),128))],10,Cr)):e.createCommentVNode("",!0),e.createElementVNode("div",{ref_key:"calendarWrapperRef",ref:H,class:"dp__instance_calendar",role:"document"},[(e.openBlock(),e.createBlock(e.resolveDynamicComponent(F.value),e.mergeProps({ref_key:"dynCmpRef",ref:L},d.value,{"flow-step":e.unref(K),onMount:e.unref(ue),onUpdateFlowStep:e.unref(se),onResetFlow:e.unref(p),onFocusMenu:W,onSelectDate:I[0]||(I[0]=ie=>v.$emit("select-date")),onDateUpdate:I[1]||(I[1]=ie=>v.$emit("date-update",ie)),onTooltipOpen:I[2]||(I[2]=ie=>v.$emit("tooltip-open",ie)),onTooltipClose:I[3]||(I[3]=ie=>v.$emit("tooltip-close",ie)),onAutoApply:I[4]||(I[4]=ie=>v.$emit("auto-apply",ie)),onRangeStart:I[5]||(I[5]=ie=>v.$emit("range-start",ie)),onRangeEnd:I[6]||(I[6]=ie=>v.$emit("range-end",ie)),onInvalidFixedRange:I[7]||(I[7]=ie=>v.$emit("invalid-fixed-range",ie)),onTimeUpdate:I[8]||(I[8]=ie=>v.$emit("time-update")),onAmPmChange:I[9]||(I[9]=ie=>v.$emit("am-pm-change",ie)),onTimePickerOpen:I[10]||(I[10]=ie=>v.$emit("time-picker-open",ie)),onTimePickerClose:re,onRecalculatePosition:ne,onUpdateMonthYear:I[11]||(I[11]=ie=>v.$emit("update-month-year",ie)),onAutoApplyInvalid:I[12]||(I[12]=ie=>v.$emit("auto-apply-invalid",ie)),onInvalidDate:I[13]||(I[13]=ie=>v.$emit("invalid-date",ie)),onOverlayToggle:I[14]||(I[14]=ie=>v.$emit("overlay-toggle",ie)),"onUpdate:internalModelValue":I[15]||(I[15]=ie=>v.$emit("update:internal-model-value",ie))}),e.createSlots({_:2},[e.renderList(O.value,(ie,Te)=>({name:ie,fn:e.withCtx(Ye=>[e.renderSlot(v.$slots,ie,e.normalizeProps(e.guardReactiveProps({...Ye})))])}))]),1040,["flow-step","onMount","onUpdateFlowStep","onResetFlow"]))],512),v.$slots["right-sidebar"]?(e.openBlock(),e.createElementBlock("div",Tr,[e.renderSlot(v.$slots,"right-sidebar",e.normalizeProps(e.guardReactiveProps(B.value)))])):e.createCommentVNode("",!0),v.$slots["action-extra"]?(e.openBlock(),e.createElementBlock("div",$r,[v.$slots["action-extra"]?e.renderSlot(v.$slots,"action-extra",{key:0,selectCurrentDate:te}):e.createCommentVNode("",!0)])):e.createCommentVNode("",!0)],14,Dr),!v.autoApply||e.unref($).keepActionRow?(e.openBlock(),e.createBlock($a,e.mergeProps({key:3,"menu-mount":w.value},d.value,{"calendar-width":m.value,onClosePicker:I[16]||(I[16]=ie=>v.$emit("close-picker")),onSelectDate:I[17]||(I[17]=ie=>v.$emit("select-date")),onInvalidSelect:I[18]||(I[18]=ie=>v.$emit("invalid-select")),onSelectNow:te}),e.createSlots({_:2},[e.renderList(e.unref(C),(ie,Te)=>({name:ie,fn:e.withCtx(Ye=>[e.renderSlot(v.$slots,ie,e.normalizeProps(e.guardReactiveProps({...Ye})))])}))]),1040,["menu-mount","calendar-width"])):e.createCommentVNode("",!0)],46,vr)}}});var rt=(t=>(t.center="center",t.left="left",t.right="right",t))(rt||{});const Ar=({menuRef:t,menuRefInner:n,inputRef:o,pickerWrapperRef:a,inline:r,emit:u,props:d,slots:h})=>{const{defaultedConfig:c}=be(d),E=e.ref({}),f=e.ref(!1),P=e.ref({top:"0",left:"0"}),g=e.ref(!1),$=e.toRef(d,"teleportCenter");e.watch($,()=>{P.value=JSON.parse(JSON.stringify({})),q()});const U=y=>{if(d.teleport){const W=y.getBoundingClientRect();return{left:W.left+window.scrollX,top:W.top+window.scrollY}}return{top:0,left:0}},_=(y,W)=>{P.value.left=`${y+W-E.value.width}px`},H=y=>{P.value.left=`${y}px`},m=(y,W)=>{d.position===rt.left&&H(y),d.position===rt.right&&_(y,W),d.position===rt.center&&(P.value.left=`${y+W/2-E.value.width/2}px`)},Y=y=>{const{width:W,height:B}=y.getBoundingClientRect(),{top:ne,left:C}=U(y);return{top:+ne,left:+C,width:W,height:B}},w=()=>{P.value.left="50%",P.value.top="50%",P.value.transform="translate(-50%, -50%)",P.value.position="fixed",delete P.value.opacity},L=()=>{const y=Se(o);P.value=d.altPosition(y)},q=(y=!0)=>{var W;if(!r.value.enabled){if($.value)return w();if(d.altPosition!==null)return L();if(y){const B=d.teleport?(W=n.value)==null?void 0:W.$el:t.value;B&&(E.value=B.getBoundingClientRect()),u("recalculate-position")}return se()}},J=({inputEl:y,left:W,width:B})=>{window.screen.width>768&&!f.value&&m(W,B),G(y)},X=y=>{const{top:W,left:B,height:ne,width:C}=Y(y);P.value.top=`${ne+W+ +d.offset}px`,g.value=!1,f.value||(P.value.left=`${B+C/2-E.value.width/2}px`),J({inputEl:y,left:B,width:C})},T=y=>{const{top:W,left:B,width:ne}=Y(y);P.value.top=`${W-+d.offset-E.value.height}px`,g.value=!0,J({inputEl:y,left:B,width:ne})},G=y=>{if(d.autoPosition){const{left:W,width:B}=Y(y),{left:ne,right:C}=E.value;if(!f.value){if(Math.abs(ne)!==Math.abs(C)){if(ne<=0)return f.value=!0,H(W);if(C>=document.documentElement.clientWidth)return f.value=!0,_(W,B)}return m(W,B)}}},R=()=>{const y=Se(o);if(y){if(d.autoPosition===_e.top)return _e.top;if(d.autoPosition===_e.bottom)return _e.bottom;const{height:W}=E.value,{top:B,height:ne}=y.getBoundingClientRect(),O=window.innerHeight-B-ne,k=B;return W<=O?_e.bottom:W>O&&W<=k?_e.top:O>=k?_e.bottom:_e.top}return _e.bottom},K=y=>R()===_e.bottom?X(y):T(y),se=()=>{const y=Se(o);if(y)return d.autoPosition?K(y):X(y)},ue=function(y){if(y){const W=y.scrollHeight>y.clientHeight,ne=window.getComputedStyle(y).overflowY.indexOf("hidden")!==-1;return W&&!ne}return!0},p=function(y){return!y||y===document.body||y.nodeType===Node.DOCUMENT_FRAGMENT_NODE?window:ue(y)?y:p(y.assignedSlot&&c.value.shadowDom?y.assignedSlot.parentNode:y.parentNode)},z=y=>{if(y)switch(d.position){case rt.left:return{left:0,transform:"translateX(0)"};case rt.right:return{left:`${y.width}px`,transform:"translateX(-100%)"};default:return{left:`${y.width/2}px`,transform:"translateX(-50%)"}}return{}};return{openOnTop:g,menuStyle:P,xCorrect:f,setMenuPosition:q,getScrollableParent:p,shadowRender:(y,W)=>{var ae,A,Z;const B=document.createElement("div"),ne=(ae=Se(o))==null?void 0:ae.getBoundingClientRect();B.setAttribute("id","dp--temp-container");const C=(A=a.value)!=null&&A.clientWidth?a.value:document.body;C.append(B);const O=z(ne),k=c.value.shadowDom?Object.keys(h).filter(l=>["right-sidebar","left-sidebar","top-extra","action-extra"].includes(l)):Object.keys(h),Q=e.h(y,{...W,shadow:!0,style:{opacity:0,position:"absolute",...O}},Object.fromEntries(k.map(l=>[l,h[l]])));e.render(Q,B),E.value=(Z=Q.el)==null?void 0:Z.getBoundingClientRect(),e.render(null,B),C.removeChild(B)}}},Ge=[{name:"clock-icon",use:["time","calendar","shared"]},{name:"arrow-left",use:["month-year","calendar","shared","year-mode"]},{name:"arrow-right",use:["month-year","calendar","shared","year-mode"]},{name:"arrow-up",use:["time","calendar","month-year","shared"]},{name:"arrow-down",use:["time","calendar","month-year","shared"]},{name:"calendar-icon",use:["month-year","time","calendar","shared","year-mode"]},{name:"day",use:["calendar","shared"]},{name:"month-overlay-value",use:["calendar","month-year","shared"]},{name:"year-overlay-value",use:["calendar","month-year","shared","year-mode"]},{name:"year-overlay",use:["month-year","shared"]},{name:"month-overlay",use:["month-year","shared"]},{name:"month-overlay-header",use:["month-year","shared"]},{name:"year-overlay-header",use:["month-year","shared"]},{name:"hours-overlay-value",use:["calendar","time","shared"]},{name:"hours-overlay-header",use:["calendar","time","shared"]},{name:"minutes-overlay-value",use:["calendar","time","shared"]},{name:"minutes-overlay-header",use:["calendar","time","shared"]},{name:"seconds-overlay-value",use:["calendar","time","shared"]},{name:"seconds-overlay-header",use:["calendar","time","shared"]},{name:"hours",use:["calendar","time","shared"]},{name:"minutes",use:["calendar","time","shared"]},{name:"month",use:["calendar","month-year","shared"]},{name:"year",use:["calendar","month-year","shared","year-mode"]},{name:"action-buttons",use:["action"]},{name:"action-preview",use:["action"]},{name:"calendar-header",use:["calendar","shared"]},{name:"marker-tooltip",use:["calendar","shared"]},{name:"action-extra",use:["menu"]},{name:"time-picker-overlay",use:["calendar","time","shared"]},{name:"am-pm-button",use:["calendar","time","shared"]},{name:"left-sidebar",use:["menu"]},{name:"right-sidebar",use:["menu"]},{name:"month-year",use:["month-year","shared"]},{name:"time-picker",use:["menu","shared"]},{name:"action-row",use:["action"]},{name:"marker",use:["calendar","shared"]},{name:"quarter",use:["shared"]},{name:"top-extra",use:["shared","month-year"]},{name:"tp-inline-arrow-up",use:["shared","time"]},{name:"tp-inline-arrow-down",use:["shared","time"]},{name:"menu-header",use:["menu"]}],Pr=[{name:"trigger"},{name:"input-icon"},{name:"clear-icon"},{name:"dp-input"}],Rr={all:()=>Ge,monthYear:()=>Ge.filter(t=>t.use.includes("month-year")),input:()=>Pr,timePicker:()=>Ge.filter(t=>t.use.includes("time")),action:()=>Ge.filter(t=>t.use.includes("action")),calendar:()=>Ge.filter(t=>t.use.includes("calendar")),menu:()=>Ge.filter(t=>t.use.includes("menu")),shared:()=>Ge.filter(t=>t.use.includes("shared")),yearMode:()=>Ge.filter(t=>t.use.includes("year-mode"))},Oe=(t,n,o)=>{const a=[];return Rr[n]().forEach(r=>{t[r.name]&&a.push(r.name)}),o!=null&&o.length&&o.forEach(r=>{r.slot&&a.push(r.slot)}),a},dt=t=>{const n=e.computed(()=>a=>t.value?a?t.value.open:t.value.close:""),o=e.computed(()=>a=>t.value?a?t.value.menuAppearTop:t.value.menuAppearBottom:"");return{transitionName:n,showTransition:!!t.value,menuTransition:o}},ft=(t,n,o)=>{const{defaultedRange:a,defaultedTz:r}=be(t),u=j(Ee(j(),r.value.timezone)),d=e.ref([{month:i.getMonth(u),year:i.getYear(u)}]),h=g=>{const $={hours:i.getHours(u),minutes:i.getMinutes(u),seconds:0};return a.value.enabled?[$[g],$[g]]:$[g]},c=e.reactive({hours:h("hours"),minutes:h("minutes"),seconds:h("seconds")});e.watch(a,(g,$)=>{g.enabled!==$.enabled&&(c.hours=h("hours"),c.minutes=h("minutes"),c.seconds=h("seconds"))},{deep:!0});const E=e.computed({get:()=>t.internalModelValue,set:g=>{!t.readonly&&!t.disabled&&n("update:internal-model-value",g)}}),f=e.computed(()=>g=>d.value[g]?d.value[g].month:0),P=e.computed(()=>g=>d.value[g]?d.value[g].year:0);return e.watch(E,(g,$)=>{o&&JSON.stringify(g??{})!==JSON.stringify($??{})&&o()},{deep:!0}),{calendars:d,time:c,modelValue:E,month:f,year:P,today:u}},Nr=(t,n)=>{const{defaultedMultiCalendars:o,defaultedMultiDates:a,defaultedUI:r,defaultedHighlight:u,defaultedTz:d,propDates:h,defaultedRange:c}=be(n),{isDisabled:E}=Qe(n),f=e.ref(null),P=e.ref(Ee(new Date,d.value.timezone)),g=l=>{!l.current&&n.hideOffsetDates||(f.value=l.value)},$=()=>{f.value=null},U=l=>Array.isArray(t.value)&&c.value.enabled&&t.value[0]&&f.value?l?we(f.value,t.value[0]):ve(f.value,t.value[0]):!0,_=(l,D)=>{const re=()=>t.value?D?t.value[0]||null:t.value[1]:null,S=t.value&&Array.isArray(t.value)?re():null;return pe(j(l.value),S)},H=l=>{const D=Array.isArray(t.value)?t.value[0]:null;return l?!ve(f.value??null,D):!0},m=(l,D=!0)=>(c.value.enabled||n.weekPicker)&&Array.isArray(t.value)&&t.value.length===2?n.hideOffsetDates&&!l.current?!1:pe(j(l.value),t.value[D?0:1]):c.value.enabled?_(l,D)&&H(D)||pe(l.value,Array.isArray(t.value)?t.value[0]:null)&&U(D):!1,Y=(l,D)=>{if(Array.isArray(t.value)&&t.value[0]&&t.value.length===1){const re=pe(l.value,f.value);return D?we(t.value[0],l.value)&&re:ve(t.value[0],l.value)&&re}return!1},w=l=>!t.value||n.hideOffsetDates&&!l.current?!1:c.value.enabled?n.modelAuto&&Array.isArray(t.value)?pe(l.value,t.value[0]?t.value[0]:P.value):!1:a.value.enabled&&Array.isArray(t.value)?t.value.some(D=>pe(D,l.value)):pe(l.value,t.value?t.value:P.value),L=l=>{if(c.value.autoRange||n.weekPicker){if(f.value){if(n.hideOffsetDates&&!l.current)return!1;const D=i.addDays(f.value,+c.value.autoRange),re=He(j(f.value),n.weekStart);return n.weekPicker?pe(re[1],j(l.value)):pe(D,j(l.value))}return!1}return!1},q=l=>{if(c.value.autoRange||n.weekPicker){if(f.value){const D=i.addDays(f.value,+c.value.autoRange);if(n.hideOffsetDates&&!l.current)return!1;const re=He(j(f.value),n.weekStart);return n.weekPicker?we(l.value,re[0])&&ve(l.value,re[1]):we(l.value,f.value)&&ve(l.value,D)}return!1}return!1},J=l=>{if(c.value.autoRange||n.weekPicker){if(f.value){if(n.hideOffsetDates&&!l.current)return!1;const D=He(j(f.value),n.weekStart);return n.weekPicker?pe(D[0],l.value):pe(f.value,l.value)}return!1}return!1},X=l=>st(t.value,f.value,l.value),T=()=>n.modelAuto&&Array.isArray(n.internalModelValue)?!!n.internalModelValue[0]:!1,G=()=>n.modelAuto?cn(n.internalModelValue):!0,R=l=>{if(n.weekPicker)return!1;const D=c.value.enabled?!m(l)&&!m(l,!1):!0;return!E(l.value)&&!w(l)&&!(!l.current&&n.hideOffsetDates)&&D},K=l=>c.value.enabled?n.modelAuto?T()&&w(l):!1:w(l),se=l=>u.value?Xn(l.value,h.value.highlight):!1,ue=l=>{const D=E(l.value);return D&&(typeof u.value=="function"?!u.value(l.value,D):!u.value.options.highlightDisabled)},p=l=>{var D;return typeof u.value=="function"?u.value(l.value):(D=u.value.weekdays)==null?void 0:D.includes(l.value.getDay())},z=l=>(c.value.enabled||n.weekPicker)&&(!(o.value.count>0)||l.current)&&G()&&!(!l.current&&n.hideOffsetDates)&&!w(l)?X(l):!1,F=l=>{if(Array.isArray(t.value)&&t.value.length===1){const{before:D,after:re}=Cn(+c.value.maxRange,t.value[0]);return i.isBefore(l.value,D)||i.isAfter(l.value,re)}return!1},y=l=>{if(Array.isArray(t.value)&&t.value.length===1){const{before:D,after:re}=Cn(+c.value.minRange,t.value[0]);return st([D,re],t.value[0],l.value)}return!1},W=l=>c.value.enabled&&(c.value.maxRange||c.value.minRange)?c.value.maxRange&&c.value.minRange?F(l)||y(l):c.value.maxRange?F(l):y(l):!1,B=l=>{const{isRangeStart:D,isRangeEnd:re}=k(l),S=c.value.enabled?D||re:!1;return{dp__cell_offset:!l.current,dp__pointer:!n.disabled&&!(!l.current&&n.hideOffsetDates)&&!E(l.value)&&!W(l),dp__cell_disabled:E(l.value)||W(l),dp__cell_highlight:!ue(l)&&(se(l)||p(l))&&!K(l)&&!S&&!J(l)&&!(z(l)&&n.weekPicker)&&!re,dp__cell_highlight_active:!ue(l)&&(se(l)||p(l))&&K(l),dp__today:!n.noToday&&pe(l.value,P.value)&&l.current,"dp--past":ve(l.value,P.value),"dp--future":we(l.value,P.value)}},ne=l=>({dp__active_date:K(l),dp__date_hover:R(l)}),C=l=>{if(t.value&&!Array.isArray(t.value)){const D=He(t.value,n.weekStart);return{...ae(l),dp__range_start:pe(D[0],l.value),dp__range_end:pe(D[1],l.value),dp__range_between_week:we(l.value,D[0])&&ve(l.value,D[1])}}return{...ae(l)}},O=l=>{if(t.value&&Array.isArray(t.value)){const D=He(t.value[0],n.weekStart),re=t.value[1]?He(t.value[1],n.weekStart):[];return{...ae(l),dp__range_start:pe(D[0],l.value)||pe(re[0],l.value),dp__range_end:pe(D[1],l.value)||pe(re[1],l.value),dp__range_between_week:we(l.value,D[0])&&ve(l.value,D[1])||we(l.value,re[0])&&ve(l.value,re[1]),dp__range_between:we(l.value,D[1])&&ve(l.value,re[0])}}return{...ae(l)}},k=l=>{const D=o.value.count>0?l.current&&m(l)&&G():m(l)&&G(),re=o.value.count>0?l.current&&m(l,!1)&&G():m(l,!1)&&G();return{isRangeStart:D,isRangeEnd:re}},Q=l=>{const{isRangeStart:D,isRangeEnd:re}=k(l);return{dp__range_start:D,dp__range_end:re,dp__range_between:z(l),dp__date_hover:pe(l.value,f.value)&&!D&&!re&&!n.weekPicker,dp__date_hover_start:Y(l,!0),dp__date_hover_end:Y(l,!1)}},ae=l=>({...Q(l),dp__cell_auto_range:q(l),dp__cell_auto_range_start:J(l),dp__cell_auto_range_end:L(l)}),A=l=>c.value.enabled?c.value.autoRange?ae(l):n.modelAuto?{...ne(l),...Q(l)}:n.weekPicker?O(l):Q(l):n.weekPicker?C(l):ne(l);return{setHoverDate:g,clearHoverDate:$,getDayClassData:l=>n.hideOffsetDates&&!l.current?{}:{...B(l),...A(l),[n.dayClass?n.dayClass(l.value,n.internalModelValue):""]:!0,...r.value.calendarCell??{}}}},Qe=t=>{const{defaultedFilters:n,defaultedRange:o,propDates:a,defaultedMultiDates:r}=be(t),u=p=>a.value.disabledDates?typeof a.value.disabledDates=="function"?a.value.disabledDates(j(p)):!!pt(p,a.value.disabledDates):!1,d=p=>a.value.maxDate?t.yearPicker?i.getYear(p)>i.getYear(a.value.maxDate):we(p,a.value.maxDate):!1,h=p=>a.value.minDate?t.yearPicker?i.getYear(p)<i.getYear(a.value.minDate):ve(p,a.value.minDate):!1,c=p=>{const z=d(p),F=h(p),y=u(p),B=n.value.months.map(Q=>+Q).includes(i.getMonth(p)),ne=t.disabledWeekDays.length?t.disabledWeekDays.some(Q=>+Q===i.getDay(p)):!1,C=$(p),O=i.getYear(p),k=O<+t.yearRange[0]||O>+t.yearRange[1];return!(z||F||y||B||k||ne||C)},E=(p,z)=>ve(...xe(a.value.minDate,p,z))||pe(...xe(a.value.minDate,p,z)),f=(p,z)=>we(...xe(a.value.maxDate,p,z))||pe(...xe(a.value.maxDate,p,z)),P=(p,z,F)=>{let y=!1;return a.value.maxDate&&F&&f(p,z)&&(y=!0),a.value.minDate&&!F&&E(p,z)&&(y=!0),y},g=(p,z,F,y)=>{let W=!1;return y&&(a.value.minDate||a.value.maxDate)?a.value.minDate&&a.value.maxDate?W=P(p,z,F):(a.value.minDate&&E(p,z)||a.value.maxDate&&f(p,z))&&(W=!0):W=!0,W},$=p=>Array.isArray(a.value.allowedDates)&&!a.value.allowedDates.length?!0:a.value.allowedDates?!pt(p,a.value.allowedDates):!1,U=p=>!c(p),_=p=>o.value.noDisabledRange?!i.eachDayOfInterval({start:p[0],end:p[1]}).some(F=>U(F)):!0,H=p=>{if(p){const z=i.getYear(p);return z>=+t.yearRange[0]&&z<=t.yearRange[1]}return!0},m=(p,z)=>!!(Array.isArray(p)&&p[z]&&(o.value.maxRange||o.value.minRange)&&H(p[z])),Y=(p,z,F=0)=>{if(m(z,F)&&H(p)){const y=i.differenceInCalendarDays(p,z[F]),W=bn(z[F],p),B=W.length===1?0:W.filter(C=>U(C)).length,ne=Math.abs(y)-(o.value.minMaxRawRange?0:B);if(o.value.minRange&&o.value.maxRange)return ne>=+o.value.minRange&&ne<=+o.value.maxRange;if(o.value.minRange)return ne>=+o.value.minRange;if(o.value.maxRange)return ne<=+o.value.maxRange}return!0},w=()=>!t.enableTimePicker||t.monthPicker||t.yearPicker||t.ignoreTimeValidation,L=p=>Array.isArray(p)?[p[0]?zt(p[0]):null,p[1]?zt(p[1]):null]:zt(p),q=(p,z,F)=>p.find(y=>+y.hours===i.getHours(z)&&y.minutes==="*"?!0:+y.minutes===i.getMinutes(z)&&+y.hours===i.getHours(z))&&F,J=(p,z,F)=>{const[y,W]=p,[B,ne]=z;return!q(y,B,F)&&!q(W,ne,F)&&F},X=(p,z)=>{const F=Array.isArray(z)?z:[z];return Array.isArray(t.disabledTimes)?Array.isArray(t.disabledTimes[0])?J(t.disabledTimes,F,p):!F.some(y=>q(t.disabledTimes,y,p)):p},T=(p,z)=>{const F=Array.isArray(z)?[Je(z[0]),z[1]?Je(z[1]):void 0]:Je(z),y=!t.disabledTimes(F);return p&&y},G=(p,z)=>t.disabledTimes?Array.isArray(t.disabledTimes)?X(z,p):T(z,p):z,R=p=>{let z=!0;if(!p||w())return!0;const F=!a.value.minDate&&!a.value.maxDate?L(p):p;return(t.maxTime||a.value.maxDate)&&(z=Dn(t.maxTime,a.value.maxDate,"max",De(F),z)),(t.minTime||a.value.minDate)&&(z=Dn(t.minTime,a.value.minDate,"min",De(F),z)),G(p,z)},K=p=>{if(!t.monthPicker)return!0;let z=!0;const F=j(Ie(p));if(a.value.minDate&&a.value.maxDate){const y=j(Ie(a.value.minDate)),W=j(Ie(a.value.maxDate));return we(F,y)&&ve(F,W)||pe(F,y)||pe(F,W)}if(a.value.minDate){const y=j(Ie(a.value.minDate));z=we(F,y)||pe(F,y)}if(a.value.maxDate){const y=j(Ie(a.value.maxDate));z=ve(F,y)||pe(F,y)}return z},se=e.computed(()=>p=>!t.enableTimePicker||t.ignoreTimeValidation?!0:R(p)),ue=e.computed(()=>p=>t.monthPicker?Array.isArray(p)&&(o.value.enabled||r.value.enabled)?!p.filter(F=>!K(F)).length:K(p):!0);return{isDisabled:U,validateDate:c,validateMonthYearInRange:g,isDateRangeAllowed:_,checkMinMaxRange:Y,isValidTime:R,isTimeValid:se,isMonthValid:ue}},vt=()=>{const t=e.computed(()=>(a,r)=>a==null?void 0:a.includes(r)),n=e.computed(()=>(a,r)=>a.count?a.solo?!0:r===0:!0),o=e.computed(()=>(a,r)=>a.count?a.solo?!0:r===a.count-1:!0);return{hideNavigationButtons:t,showLeftIcon:n,showRightIcon:o}},Er=(t,n,o)=>{const a=e.ref(0),r=e.reactive({[Xe.timePicker]:!t.enableTimePicker||t.timePicker||t.monthPicker,[Xe.calendar]:!1,[Xe.header]:!1}),u=e.computed(()=>t.monthPicker||t.timePicker),d=P=>{var g;if((g=t.flow)!=null&&g.length){if(!P&&u.value)return f();r[P]=!0,Object.keys(r).filter($=>!r[$]).length||f()}},h=()=>{var P,g;(P=t.flow)!=null&&P.length&&a.value!==-1&&(a.value+=1,n("flow-step",a.value),f()),((g=t.flow)==null?void 0:g.length)===a.value&&e.nextTick().then(()=>c())},c=()=>{a.value=-1},E=(P,g,...$)=>{var U,_;t.flow[a.value]===P&&o.value&&((_=(U=o.value)[g])==null||_.call(U,...$))},f=(P=0)=>{P&&(a.value+=P),E($e.month,"toggleMonthPicker",!0),E($e.year,"toggleYearPicker",!0),E($e.calendar,"toggleTimePicker",!1,!0),E($e.time,"toggleTimePicker",!0,!0);const g=t.flow[a.value];(g===$e.hours||g===$e.minutes||g===$e.seconds)&&E(g,"toggleTimePicker",!0,!0,g)};return{childMount:d,updateFlowStep:h,resetFlow:c,handleFlow:f,flowStep:a}},Or={key:1,class:"dp__input_wrap"},Yr=["id","name","inputmode","placeholder","disabled","readonly","required","value","autocomplete","aria-label","aria-disabled","aria-invalid"],Vr={key:2,class:"dp--clear-btn"},_r=["aria-label"],Ir=e.defineComponent({compatConfig:{MODE:3},__name:"DatepickerInput",props:{isMenuOpen:{type:Boolean,default:!1},inputValue:{type:String,default:""},...ht},emits:["clear","open","update:input-value","set-input-date","close","select-date","set-empty-date","toggle","focus-prev","focus","blur","real-blur","text-input"],setup(t,{expose:n,emit:o}){const a=o,r=t,{defaultedTextInput:u,defaultedAriaLabels:d,defaultedInline:h,defaultedConfig:c,defaultedRange:E,defaultedMultiDates:f,defaultedUI:P,getDefaultPattern:g,getDefaultStartTime:$}=be(r),{checkMinMaxRange:U}=Qe(r),_=e.ref(),H=e.ref(null),m=e.ref(!1),Y=e.ref(!1),w=e.computed(()=>({dp__pointer:!r.disabled&&!r.readonly&&!u.value.enabled,dp__disabled:r.disabled,dp__input_readonly:!u.value.enabled,dp__input:!0,dp__input_icon_pad:!r.hideInputIcon,dp__input_valid:typeof r.state=="boolean"?r.state:!1,dp__input_invalid:typeof r.state=="boolean"?!r.state:!1,dp__input_focus:m.value||r.isMenuOpen,dp__input_reg:!u.value.enabled,...P.value.input??{}})),L=()=>{a("set-input-date",null),r.clearable&&r.autoApply&&(a("set-empty-date"),_.value=null)},q=C=>{const O=$();return Fn(C,u.value.format??g(),O??vn({},r.enableSeconds),r.inputValue,Y.value,r.formatLocale)},J=C=>{const{rangeSeparator:O}=u.value,[k,Q]=C.split(`${O}`);if(k){const ae=q(k.trim()),A=Q?q(Q.trim()):null;if(i.isAfter(ae,A))return;const Z=ae&&A?[ae,A]:[ae];U(A,Z,0)&&(_.value=ae?Z:null)}},X=()=>{Y.value=!0},T=C=>{if(E.value.enabled)J(C);else if(f.value.enabled){const O=C.split(";");_.value=O.map(k=>q(k.trim())).filter(k=>k)}else _.value=q(C)},G=C=>{var k;const O=typeof C=="string"?C:(k=C.target)==null?void 0:k.value;O!==""?(u.value.openMenu&&!r.isMenuOpen&&a("open"),T(O),a("set-input-date",_.value)):L(),Y.value=!1,a("update:input-value",O),a("text-input",C,_.value)},R=C=>{u.value.enabled?(T(C.target.value),u.value.enterSubmit&&Vt(_.value)&&r.inputValue!==""?(a("set-input-date",_.value,!0),_.value=null):u.value.enterSubmit&&r.inputValue===""&&(_.value=null,a("clear"))):ue(C)},K=(C,O)=>{u.value.enabled&&u.value.tabSubmit&&!O&&T(C.target.value),u.value.tabSubmit&&Vt(_.value)&&r.inputValue!==""?(a("set-input-date",_.value,!0,!0),_.value=null):u.value.tabSubmit&&r.inputValue===""&&(_.value=null,a("clear",!0))},se=()=>{m.value=!0,a("focus"),e.nextTick().then(()=>{var C;u.value.enabled&&u.value.selectOnFocus&&((C=H.value)==null||C.select())})},ue=C=>{if(je(C,c.value,!0),u.value.enabled&&u.value.openMenu&&!h.value.input){if(u.value.openMenu==="open"&&!r.isMenuOpen)return a("open");if(u.value.openMenu==="toggle")return a("toggle")}else u.value.enabled||a("toggle")},p=()=>{a("real-blur"),m.value=!1,(!r.isMenuOpen||h.value.enabled&&h.value.input)&&a("blur"),r.autoApply&&u.value.enabled&&_.value&&!r.isMenuOpen&&(a("set-input-date",_.value),a("select-date"),_.value=null)},z=C=>{je(C,c.value,!0),a("clear")},F=()=>{a("close")},y=C=>{if(C.key==="Tab"&&K(C),C.key==="Enter"&&R(C),C.key==="Escape"&&u.value.escClose&&F(),!u.value.enabled){if(C.code==="Tab")return;C.preventDefault()}},W=()=>{var C;(C=H.value)==null||C.focus({preventScroll:!0})},B=C=>{_.value=C},ne=C=>{C.key===he.tab&&K(C,!0)};return n({focusInput:W,setParsedDate:B}),(C,O)=>{var k,Q,ae;return e.openBlock(),e.createElementBlock("div",{onClick:ue},[C.$slots.trigger&&!C.$slots["dp-input"]&&!e.unref(h).enabled?e.renderSlot(C.$slots,"trigger",{key:0}):e.createCommentVNode("",!0),!C.$slots.trigger&&(!e.unref(h).enabled||e.unref(h).input)?(e.openBlock(),e.createElementBlock("div",Or,[C.$slots["dp-input"]&&!C.$slots.trigger&&(!e.unref(h).enabled||e.unref(h).enabled&&e.unref(h).input)?e.renderSlot(C.$slots,"dp-input",{key:0,value:t.inputValue,isMenuOpen:t.isMenuOpen,onInput:G,onEnter:R,onTab:K,onClear:z,onBlur:p,onKeypress:y,onPaste:X,onFocus:se,openMenu:()=>C.$emit("open"),closeMenu:()=>C.$emit("close"),toggleMenu:()=>C.$emit("toggle")}):e.createCommentVNode("",!0),C.$slots["dp-input"]?e.createCommentVNode("",!0):(e.openBlock(),e.createElementBlock("input",{key:1,id:C.uid?`dp-input-${C.uid}`:void 0,ref_key:"inputRef",ref:H,"data-test-id":"dp-input",name:C.name,class:e.normalizeClass(w.value),inputmode:e.unref(u).enabled?"text":"none",placeholder:C.placeholder,disabled:C.disabled,readonly:C.readonly,required:C.required,value:t.inputValue,autocomplete:C.autocomplete,"aria-label":(k=e.unref(d))==null?void 0:k.input,"aria-disabled":C.disabled||void 0,"aria-invalid":C.state===!1?!0:void 0,onInput:G,onBlur:p,onFocus:se,onKeypress:y,onKeydown:O[0]||(O[0]=A=>y(A)),onPaste:X},null,42,Yr)),e.createElementVNode("div",{onClick:O[3]||(O[3]=A=>a("toggle"))},[C.$slots["input-icon"]&&!C.hideInputIcon?(e.openBlock(),e.createElementBlock("span",{key:0,class:"dp__input_icon",onClick:O[1]||(O[1]=A=>a("toggle"))},[e.renderSlot(C.$slots,"input-icon")])):e.createCommentVNode("",!0),!C.$slots["input-icon"]&&!C.hideInputIcon&&!C.$slots["dp-input"]?(e.openBlock(),e.createBlock(e.unref(Fe),{key:1,"aria-label":(Q=e.unref(d))==null?void 0:Q.calendarIcon,class:"dp__input_icon dp__input_icons",onClick:O[2]||(O[2]=A=>a("toggle"))},null,8,["aria-label"])):e.createCommentVNode("",!0)]),C.$slots["clear-icon"]&&(C.alwaysClearable||t.inputValue&&C.clearable&&!C.disabled&&!C.readonly)?(e.openBlock(),e.createElementBlock("span",Vr,[e.renderSlot(C.$slots,"clear-icon",{clear:z})])):e.createCommentVNode("",!0),!C.$slots["clear-icon"]&&(C.alwaysClearable||C.clearable&&t.inputValue&&!C.disabled&&!C.readonly)?(e.openBlock(),e.createElementBlock("button",{key:3,"aria-label":(ae=e.unref(d))==null?void 0:ae.clearInput,class:"dp--clear-btn",type:"button",onKeydown:O[4]||(O[4]=A=>e.unref(Re)(A,()=>z(A),!0,ne)),onClick:O[5]||(O[5]=e.withModifiers(A=>z(A),["prevent"]))},[e.createVNode(e.unref(on),{class:"dp__input_icons","data-test-id":"clear-icon"})],40,_r)):e.createCommentVNode("",!0)])):e.createCommentVNode("",!0)])}}}),zr=typeof window<"u"?window:void 0,Qt=()=>{},Lr=t=>e.getCurrentScope()?(e.onScopeDispose(t),!0):!1,Hr=(t,n,o,a)=>{if(!t)return Qt;let r=Qt;const u=e.watch(()=>e.unref(t),h=>{r(),h&&(h.addEventListener(n,o,a),r=()=>{h.removeEventListener(n,o,a),r=Qt})},{immediate:!0,flush:"post"}),d=()=>{u(),r()};return Lr(d),d},Ur=(t,n,o,a={})=>{const{window:r=zr,event:u="pointerdown"}=a;return r?Hr(r,u,h=>{const c=Se(t),E=Se(n);!c||!E||c===h.target||h.composedPath().includes(c)||h.composedPath().includes(E)||o(h)},{passive:!0}):void 0},Wr=["data-dp-mobile"],jr=e.defineComponent({compatConfig:{MODE:3},__name:"VueDatePicker",props:{...ht},emits:["update:model-value","update:model-timezone-value","text-submit","closed","cleared","open","focus","blur","internal-model-change","recalculate-position","flow-step","update-month-year","invalid-select","invalid-fixed-range","tooltip-open","tooltip-close","time-picker-open","time-picker-close","am-pm-change","range-start","range-end","date-update","invalid-date","overlay-toggle","text-input"],setup(t,{expose:n,emit:o}){const a=o,r=t,u=e.useSlots(),d=e.ref(!1),h=e.toRef(r,"modelValue"),c=e.toRef(r,"timezone"),E=e.ref(null),f=e.ref(null),P=e.ref(null),g=e.ref(!1),$=e.ref(null),U=e.ref(!1),_=e.ref(!1),H=e.ref(!1),m=e.ref(!1),{setMenuFocused:Y,setShiftKey:w}=Bn(),{clearArrowNav:L}=qe(),{validateDate:q,isValidTime:J}=Qe(r),{defaultedTransitions:X,defaultedTextInput:T,defaultedInline:G,defaultedConfig:R,defaultedRange:K,defaultedMultiDates:se}=be(r),{menuTransition:ue,showTransition:p}=dt(X),{isMobile:z}=Vn(R);e.onMounted(()=>{A(r.modelValue),e.nextTick().then(()=>{if(!G.value.enabled){const M=O($.value);M==null||M.addEventListener("scroll",V),window==null||window.addEventListener("resize",oe)}}),G.value.enabled&&(d.value=!0),window==null||window.addEventListener("keyup",N),window==null||window.addEventListener("keydown",me)}),e.onUnmounted(()=>{if(!G.value.enabled){const M=O($.value);M==null||M.removeEventListener("scroll",V),window==null||window.removeEventListener("resize",oe)}window==null||window.removeEventListener("keyup",N),window==null||window.removeEventListener("keydown",me)});const F=Oe(u,"all",r.presetDates),y=Oe(u,"input");e.watch([h,c],()=>{A(h.value)},{deep:!0});const{openOnTop:W,menuStyle:B,xCorrect:ne,setMenuPosition:C,getScrollableParent:O,shadowRender:k}=Ar({menuRef:E,menuRefInner:f,inputRef:P,pickerWrapperRef:$,inline:G,emit:a,props:r,slots:u}),{inputValue:Q,internalModelValue:ae,parseExternalModelValue:A,emitModelValue:Z,formatInputValue:l,checkBeforeEmit:D}=Sa(a,r,g),re=e.computed(()=>({dp__main:!0,dp__theme_dark:r.dark,dp__theme_light:!r.dark,dp__flex_display:G.value.enabled,"dp--flex-display-collapsed":H.value,dp__flex_display_with_input:G.value.input})),S=e.computed(()=>r.dark?"dp__theme_dark":"dp__theme_light"),de=e.computed(()=>r.teleport?{to:typeof r.teleport=="boolean"?"body":r.teleport,disabled:!r.teleport||G.value.enabled}:{}),ce=e.computed(()=>({class:"dp__outer_menu_wrap"})),te=e.computed(()=>G.value.enabled&&(r.timePicker||r.monthPicker||r.yearPicker||r.quarterPicker)),s=()=>{var M,x;return((x=(M=P.value)==null?void 0:M.$el)==null?void 0:x.getBoundingClientRect())??{width:0,left:0,right:0}},V=()=>{d.value&&(R.value.closeOnScroll?Te():C())},oe=()=>{var x;d.value&&C();const M=((x=f.value)==null?void 0:x.$el.getBoundingClientRect().width)??0;H.value=document.body.offsetWidth<=M},N=M=>{M.key==="Tab"&&!G.value.enabled&&!r.teleport&&R.value.tabOutClosesMenu&&($.value.contains(document.activeElement)||Te()),_.value=M.shiftKey},me=M=>{_.value=M.shiftKey},fe=()=>{!r.disabled&&!r.readonly&&(k(_n,r),C(!1),d.value=!0,d.value&&a("open"),d.value||ie(),A(r.modelValue))},ge=()=>{var M,x;Q.value="",ie(),(M=f.value)==null||M.onValueCleared(),(x=P.value)==null||x.setParsedDate(null),a("update:model-value",null),a("update:model-timezone-value",null),a("cleared"),R.value.closeOnClearValue&&Te()},v=()=>{const M=ae.value;return!M||!Array.isArray(M)&&q(M)?!0:Array.isArray(M)?se.value.enabled||M.length===2&&q(M[0])&&q(M[1])?!0:K.value.partialRange&&!r.timePicker?q(M[0]):!1:!1},I=()=>{D()&&v()?(Z(),Te()):a("invalid-select",ae.value)},ye=M=>{Ce(),Z(),R.value.closeOnAutoApply&&!M&&Te()},Ce=()=>{P.value&&T.value.enabled&&P.value.setParsedDate(ae.value)},We=(M=!1)=>{r.autoApply&&J(ae.value)&&v()&&(K.value.enabled&&Array.isArray(ae.value)?(K.value.partialRange||ae.value.length===2)&&ye(M):ye(M))},ie=()=>{T.value.enabled||(ae.value=null)},Te=(M=!1)=>{M&&ae.value&&R.value.setDateOnMenuClose&&I(),G.value.enabled||(d.value&&(d.value=!1,ne.value=!1,Y(!1),w(!1),L(),a("closed"),Q.value&&A(h.value)),ie(),a("blur"))},Ye=(M,x,le=!1)=>{if(!M){ae.value=null;return}const Ne=Array.isArray(M)?!M.some(lt=>!q(lt)):q(M),Le=J(M);Ne&&Le?(m.value=!0,ae.value=M,x&&(U.value=le,I(),a("text-submit")),e.nextTick().then(()=>{m.value=!1})):a("invalid-date",M)},Jt=()=>{r.autoApply&&J(ae.value)&&Z(),Ce()},wt=()=>d.value?Te():fe(),Zt=M=>{ae.value=M},Ft=()=>{T.value.enabled&&(g.value=!0,l()),a("focus")},en=()=>{if(T.value.enabled&&(g.value=!1,A(r.modelValue),U.value)){const M=Qn($.value,_.value);M==null||M.focus()}a("blur")},tn=M=>{f.value&&f.value.updateMonthYear(0,{month:dn(M.month),year:dn(M.year)})},nn=M=>{A(M??r.modelValue)},an=(M,x)=>{var le;(le=f.value)==null||le.switchView(M,x)},In=(M,x)=>R.value.onClickOutside?R.value.onClickOutside(M,x):Te(!0),b=(M=0)=>{var x;(x=f.value)==null||x.handleFlow(M)},ee=()=>E;return Ur(E,P,M=>In(v,M)),n({closeMenu:Te,selectDate:I,clearValue:ge,openMenu:fe,onScroll:V,formatInputValue:l,updateInternalModelValue:Zt,setMonthYear:tn,parseModel:nn,switchView:an,toggleMenu:wt,handleFlow:b,getDpWrapMenuRef:ee}),(M,x)=>(e.openBlock(),e.createElementBlock("div",{ref_key:"pickerWrapperRef",ref:$,class:e.normalizeClass(re.value),"data-datepicker-instance":"","data-dp-mobile":e.unref(z)},[e.createVNode(Ir,e.mergeProps({ref_key:"inputRef",ref:P,"input-value":e.unref(Q),"onUpdate:inputValue":x[0]||(x[0]=le=>e.isRef(Q)?Q.value=le:null),"is-menu-open":d.value},M.$props,{onClear:ge,onOpen:fe,onSetInputDate:Ye,onSetEmptyDate:e.unref(Z),onSelectDate:I,onToggle:wt,onClose:Te,onFocus:Ft,onBlur:en,onRealBlur:x[1]||(x[1]=le=>g.value=!1),onTextInput:x[2]||(x[2]=le=>M.$emit("text-input",le))}),e.createSlots({_:2},[e.renderList(e.unref(y),(le,Ne)=>({name:le,fn:e.withCtx(Le=>[e.renderSlot(M.$slots,le,e.normalizeProps(e.guardReactiveProps(Le)))])}))]),1040,["input-value","is-menu-open","onSetEmptyDate"]),(e.openBlock(),e.createBlock(e.resolveDynamicComponent(M.teleport?e.Teleport:"div"),e.normalizeProps(e.guardReactiveProps(de.value)),{default:e.withCtx(()=>[e.createVNode(e.Transition,{name:e.unref(ue)(e.unref(W)),css:e.unref(p)&&!e.unref(G).enabled},{default:e.withCtx(()=>[d.value?(e.openBlock(),e.createElementBlock("div",e.mergeProps({key:0,ref_key:"dpWrapMenuRef",ref:E},ce.value,{class:{"dp--menu-wrapper":!e.unref(G).enabled},style:e.unref(G).enabled?void 0:e.unref(B)}),[e.createVNode(_n,e.mergeProps({ref_key:"dpMenuRef",ref:f},M.$props,{"internal-model-value":e.unref(ae),"onUpdate:internalModelValue":x[3]||(x[3]=le=>e.isRef(ae)?ae.value=le:null),class:{[S.value]:!0,"dp--menu-wrapper":M.teleport},"open-on-top":e.unref(W),"no-overlay-focus":te.value,collapse:H.value,"get-input-rect":s,"is-text-input-date":m.value,onClosePicker:Te,onSelectDate:I,onAutoApply:We,onTimeUpdate:Jt,onFlowStep:x[4]||(x[4]=le=>M.$emit("flow-step",le)),onUpdateMonthYear:x[5]||(x[5]=le=>M.$emit("update-month-year",le)),onInvalidSelect:x[6]||(x[6]=le=>M.$emit("invalid-select",e.unref(ae))),onAutoApplyInvalid:x[7]||(x[7]=le=>M.$emit("invalid-select",le)),onInvalidFixedRange:x[8]||(x[8]=le=>M.$emit("invalid-fixed-range",le)),onRecalculatePosition:e.unref(C),onTooltipOpen:x[9]||(x[9]=le=>M.$emit("tooltip-open",le)),onTooltipClose:x[10]||(x[10]=le=>M.$emit("tooltip-close",le)),onTimePickerOpen:x[11]||(x[11]=le=>M.$emit("time-picker-open",le)),onTimePickerClose:x[12]||(x[12]=le=>M.$emit("time-picker-close",le)),onAmPmChange:x[13]||(x[13]=le=>M.$emit("am-pm-change",le)),onRangeStart:x[14]||(x[14]=le=>M.$emit("range-start",le)),onRangeEnd:x[15]||(x[15]=le=>M.$emit("range-end",le)),onDateUpdate:x[16]||(x[16]=le=>M.$emit("date-update",le)),onInvalidDate:x[17]||(x[17]=le=>M.$emit("invalid-date",le)),onOverlayToggle:x[18]||(x[18]=le=>M.$emit("overlay-toggle",le)),onMenuBlur:x[19]||(x[19]=le=>M.$emit("blur"))}),e.createSlots({_:2},[e.renderList(e.unref(F),(le,Ne)=>({name:le,fn:e.withCtx(Le=>[e.renderSlot(M.$slots,le,e.normalizeProps(e.guardReactiveProps({...Le})))])}))]),1040,["internal-model-value","class","open-on-top","no-overlay-focus","collapse","is-text-input-date","onRecalculatePosition"])],16)):e.createCommentVNode("",!0)]),_:3},8,["name","css"])]),_:3},16))],10,Wr))}}),Xt=(()=>{const t=jr;return t.install=n=>{n.component("Vue3DatePicker",t)},t})();return Object.entries(Object.freeze(Object.defineProperty({__proto__:null,default:Xt},Symbol.toStringTag,{value:"Module"}))).forEach(([t,n])=>{t!=="default"&&(Xt[t]=n)}),Xt});
