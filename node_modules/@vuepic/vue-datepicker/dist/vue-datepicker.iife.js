var VueDatePicker=function(e){"use strict";var Mi=Object.defineProperty;var Ti=(e,We,wt)=>We in e?Mi(e,We,{enumerable:!0,configurable:!0,writable:!0,value:wt}):e[We]=wt;var oe=(e,We,wt)=>Ti(e,typeof We!="symbol"?We+"":We,wt);function We(){const t=e.useAttrs();return e.openBlock(),e.createElementBlock("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 32 32",fill:"currentColor","aria-hidden":"true",class:"dp__icon",role:"img",...t},[e.createElementVNode("path",{d:"M29.333 8c0-2.208-1.792-4-4-4h-18.667c-2.208 0-4 1.792-4 4v18.667c0 2.208 1.792 4 4 4h18.667c2.208 0 4-1.792 4-4v-18.667zM26.667 8v18.667c0 0.736-0.597 1.333-1.333 1.333 0 0-18.667 0-18.667 0-0.736 0-1.333-0.597-1.333-1.333 0 0 0-18.667 0-18.667 0-0.736 0.597-1.333 1.333-1.333 0 0 18.667 0 18.667 0 0.736 0 1.333 0.597 1.333 1.333z"}),e.createElementVNode("path",{d:"M20 2.667v5.333c0 0.736 0.597 1.333 1.333 1.333s1.333-0.597 1.333-1.333v-5.333c0-0.736-0.597-1.333-1.333-1.333s-1.333 0.597-1.333 1.333z"}),e.createElementVNode("path",{d:"M9.333 2.667v5.333c0 0.736 0.597 1.333 1.333 1.333s1.333-0.597 1.333-1.333v-5.333c0-0.736-0.597-1.333-1.333-1.333s-1.333 0.597-1.333 1.333z"}),e.createElementVNode("path",{d:"M4 14.667h24c0.736 0 1.333-0.597 1.333-1.333s-0.597-1.333-1.333-1.333h-24c-0.736 0-1.333 0.597-1.333 1.333s0.597 1.333 1.333 1.333z"})])}We.compatConfig={MODE:3};function wt(){return e.openBlock(),e.createElementBlock("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 32 32",fill:"currentColor","aria-hidden":"true",class:"dp__icon",role:"img"},[e.createElementVNode("path",{d:"M23.057 7.057l-16 16c-0.52 0.52-0.52 1.365 0 1.885s1.365 0.52 1.885 0l16-16c0.52-0.52 0.52-1.365 0-1.885s-1.365-0.52-1.885 0z"}),e.createElementVNode("path",{d:"M7.057 8.943l16 16c0.52 0.52 1.365 0.52 1.885 0s0.52-1.365 0-1.885l-16-16c-0.52-0.52-1.365-0.52-1.885 0s-0.52 1.365 0 1.885z"})])}wt.compatConfig={MODE:3};function un(){return e.openBlock(),e.createElementBlock("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 32 32",fill:"currentColor","aria-hidden":"true",class:"dp__icon",role:"img"},[e.createElementVNode("path",{d:"M20.943 23.057l-7.057-7.057c0 0 7.057-7.057 7.057-7.057 0.52-0.52 0.52-1.365 0-1.885s-1.365-0.52-1.885 0l-8 8c-0.521 0.521-0.521 1.365 0 1.885l8 8c0.52 0.52 1.365 0.52 1.885 0s0.52-1.365 0-1.885z"})])}un.compatConfig={MODE:3};function cn(){return e.openBlock(),e.createElementBlock("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 32 32",fill:"currentColor","aria-hidden":"true",class:"dp__icon",role:"img"},[e.createElementVNode("path",{d:"M12.943 24.943l8-8c0.521-0.521 0.521-1.365 0-1.885l-8-8c-0.52-0.52-1.365-0.52-1.885 0s-0.52 1.365 0 1.885l7.057 7.057c0 0-7.057 7.057-7.057 7.057-0.52 0.52-0.52 1.365 0 1.885s1.365 0.52 1.885 0z"})])}cn.compatConfig={MODE:3};function dn(){return e.openBlock(),e.createElementBlock("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 32 32",fill:"currentColor","aria-hidden":"true",class:"dp__icon",role:"img"},[e.createElementVNode("path",{d:"M16 1.333c-8.095 0-14.667 6.572-14.667 14.667s6.572 14.667 14.667 14.667c8.095 0 14.667-6.572 14.667-14.667s-6.572-14.667-14.667-14.667zM16 4c6.623 0 12 5.377 12 12s-5.377 12-12 12c-6.623 0-12-5.377-12-12s5.377-12 12-12z"}),e.createElementVNode("path",{d:"M14.667 8v8c0 0.505 0.285 0.967 0.737 1.193l5.333 2.667c0.658 0.329 1.46 0.062 1.789-0.596s0.062-1.46-0.596-1.789l-4.596-2.298c0 0 0-7.176 0-7.176 0-0.736-0.597-1.333-1.333-1.333s-1.333 0.597-1.333 1.333z"})])}dn.compatConfig={MODE:3};function fn(){return e.openBlock(),e.createElementBlock("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 32 32",fill:"currentColor","aria-hidden":"true",class:"dp__icon",role:"img"},[e.createElementVNode("path",{d:"M24.943 19.057l-8-8c-0.521-0.521-1.365-0.521-1.885 0l-8 8c-0.52 0.52-0.52 1.365 0 1.885s1.365 0.52 1.885 0l7.057-7.057c0 0 7.057 7.057 7.057 7.057 0.52 0.52 1.365 0.52 1.885 0s0.52-1.365 0-1.885z"})])}fn.compatConfig={MODE:3};function mn(){return e.openBlock(),e.createElementBlock("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 32 32",fill:"currentColor","aria-hidden":"true",class:"dp__icon",role:"img"},[e.createElementVNode("path",{d:"M7.057 12.943l8 8c0.521 0.521 1.365 0.521 1.885 0l8-8c0.52-0.52 0.52-1.365 0-1.885s-1.365-0.52-1.885 0l-7.057 7.057c0 0-7.057-7.057-7.057-7.057-0.52-0.52-1.365-0.52-1.885 0s-0.52 1.365 0 1.885z"})])}mn.compatConfig={MODE:3};const na=6048e5,fr=864e5,mr=6e4,aa=36e5,hr=1e3,ra=Symbol.for("constructDateFrom");function ke(t,r){return typeof t=="function"?t(r):t&&typeof t=="object"&&ra in t?t[ra](r):t instanceof Date?new t.constructor(r):new Date(r)}function ce(t,r){return ke(r||t,t)}function je(t,r,n){const a=ce(t,n==null?void 0:n.in);return isNaN(r)?ke((n==null?void 0:n.in)||t,NaN):(r&&a.setDate(a.getDate()+r),a)}function Ge(t,r,n){const a=ce(t,n==null?void 0:n.in);if(isNaN(r))return ke(t,NaN);if(!r)return a;const o=a.getDate(),l=ke(t,a.getTime());l.setMonth(a.getMonth()+r+1,0);const i=l.getDate();return o>=i?l:(a.setFullYear(l.getFullYear(),l.getMonth(),o),a)}function oa(t,r,n){const{years:a=0,months:o=0,weeks:l=0,days:i=0,hours:d=0,minutes:c=0,seconds:v=0}=r,f=ce(t,n==null?void 0:n.in),T=o||a?Ge(f,o+a*12):f,m=i||l?je(T,i+l*7):T,M=c+d*60,E=(v+M*60)*1e3;return ke(t,+m+E)}function gr(t,r,n){return ke(t,+ce(t)+r)}function yr(t,r,n){return gr(t,r*aa)}let pr={};function kt(){return pr}function Ke(t,r){var d,c,v,f;const n=kt(),a=(r==null?void 0:r.weekStartsOn)??((c=(d=r==null?void 0:r.locale)==null?void 0:d.options)==null?void 0:c.weekStartsOn)??n.weekStartsOn??((f=(v=n.locale)==null?void 0:v.options)==null?void 0:f.weekStartsOn)??0,o=ce(t,r==null?void 0:r.in),l=o.getDay(),i=(l<a?7:0)+l-a;return o.setDate(o.getDate()-i),o.setHours(0,0,0,0),o}function Ct(t,r){return Ke(t,{...r,weekStartsOn:1})}function la(t,r){const n=ce(t,r==null?void 0:r.in),a=n.getFullYear(),o=ke(n,0);o.setFullYear(a+1,0,4),o.setHours(0,0,0,0);const l=Ct(o),i=ke(n,0);i.setFullYear(a,0,4),i.setHours(0,0,0,0);const d=Ct(i);return n.getTime()>=l.getTime()?a+1:n.getTime()>=d.getTime()?a:a-1}function Xt(t){const r=ce(t),n=new Date(Date.UTC(r.getFullYear(),r.getMonth(),r.getDate(),r.getHours(),r.getMinutes(),r.getSeconds(),r.getMilliseconds()));return n.setUTCFullYear(r.getFullYear()),+t-+n}function Vt(t,...r){const n=ke.bind(null,r.find(a=>typeof a=="object"));return r.map(n)}function sa(t,r){const n=ce(t,r==null?void 0:r.in);return n.setHours(0,0,0,0),n}function ia(t,r,n){const[a,o]=Vt(n==null?void 0:n.in,t,r),l=sa(a),i=sa(o),d=+l-Xt(l),c=+i-Xt(i);return Math.round((d-c)/fr)}function wr(t,r){const n=la(t,r),a=ke(t,0);return a.setFullYear(n,0,4),a.setHours(0,0,0,0),Ct(a)}function kr(t,r,n){return Ge(t,r*3,n)}function hn(t,r,n){return Ge(t,r*12,n)}function ua(t,r){const n=+ce(t)-+ce(r);return n<0?-1:n>0?1:n}function ca(t){return t instanceof Date||typeof t=="object"&&Object.prototype.toString.call(t)==="[object Date]"}function It(t){return!(!ca(t)&&typeof t!="number"||isNaN(+ce(t)))}function da(t,r){const n=ce(t,r==null?void 0:r.in);return Math.trunc(n.getMonth()/3)+1}function br(t,r,n){const[a,o]=Vt(n==null?void 0:n.in,t,r);return a.getFullYear()-o.getFullYear()}function vr(t,r,n){const[a,o]=Vt(n==null?void 0:n.in,t,r),l=ua(a,o),i=Math.abs(br(a,o));a.setFullYear(1584),o.setFullYear(1584);const d=ua(a,o)===-l,c=l*(i-+d);return c===0?0:c}function fa(t,r){const[n,a]=Vt(t,r.start,r.end);return{start:n,end:a}}function ma(t,r){const{start:n,end:a}=fa(r==null?void 0:r.in,t);let o=+n>+a;const l=o?+n:+a,i=o?a:n;i.setHours(0,0,0,0);let d=1;const c=[];for(;+i<=l;)c.push(ke(n,i)),i.setDate(i.getDate()+d),i.setHours(0,0,0,0);return o?c.reverse():c}function bt(t,r){const n=ce(t,r==null?void 0:r.in),a=n.getMonth(),o=a-a%3;return n.setMonth(o,1),n.setHours(0,0,0,0),n}function Dr(t,r){const{start:n,end:a}=fa(r==null?void 0:r.in,t);let o=+n>+a;const l=o?+bt(n):+bt(a);let i=bt(o?a:n),d=1;const c=[];for(;+i<=l;)c.push(ke(n,i)),i=kr(i,d);return o?c.reverse():c}function Mr(t,r){const n=ce(t,r==null?void 0:r.in);return n.setDate(1),n.setHours(0,0,0,0),n}function ha(t,r){const n=ce(t,r==null?void 0:r.in),a=n.getFullYear();return n.setFullYear(a+1,0,0),n.setHours(23,59,59,999),n}function Ft(t,r){const n=ce(t,r==null?void 0:r.in);return n.setFullYear(n.getFullYear(),0,1),n.setHours(0,0,0,0),n}function ga(t,r){var d,c,v,f;const n=kt(),a=(r==null?void 0:r.weekStartsOn)??((c=(d=r==null?void 0:r.locale)==null?void 0:d.options)==null?void 0:c.weekStartsOn)??n.weekStartsOn??((f=(v=n.locale)==null?void 0:v.options)==null?void 0:f.weekStartsOn)??0,o=ce(t,r==null?void 0:r.in),l=o.getDay(),i=(l<a?-7:0)+6-(l-a);return o.setDate(o.getDate()+i),o.setHours(23,59,59,999),o}function ya(t,r){const n=ce(t,r==null?void 0:r.in),a=n.getMonth(),o=a-a%3+3;return n.setMonth(o,0),n.setHours(23,59,59,999),n}const Tr={lessThanXSeconds:{one:"less than a second",other:"less than {{count}} seconds"},xSeconds:{one:"1 second",other:"{{count}} seconds"},halfAMinute:"half a minute",lessThanXMinutes:{one:"less than a minute",other:"less than {{count}} minutes"},xMinutes:{one:"1 minute",other:"{{count}} minutes"},aboutXHours:{one:"about 1 hour",other:"about {{count}} hours"},xHours:{one:"1 hour",other:"{{count}} hours"},xDays:{one:"1 day",other:"{{count}} days"},aboutXWeeks:{one:"about 1 week",other:"about {{count}} weeks"},xWeeks:{one:"1 week",other:"{{count}} weeks"},aboutXMonths:{one:"about 1 month",other:"about {{count}} months"},xMonths:{one:"1 month",other:"{{count}} months"},aboutXYears:{one:"about 1 year",other:"about {{count}} years"},xYears:{one:"1 year",other:"{{count}} years"},overXYears:{one:"over 1 year",other:"over {{count}} years"},almostXYears:{one:"almost 1 year",other:"almost {{count}} years"}},Pr=(t,r,n)=>{let a;const o=Tr[t];return typeof o=="string"?a=o:r===1?a=o.one:a=o.other.replace("{{count}}",r.toString()),n!=null&&n.addSuffix?n.comparison&&n.comparison>0?"in "+a:a+" ago":a};function gn(t){return(r={})=>{const n=r.width?String(r.width):t.defaultWidth;return t.formats[n]||t.formats[t.defaultWidth]}}const Cr={full:"EEEE, MMMM do, y",long:"MMMM do, y",medium:"MMM d, y",short:"MM/dd/yyyy"},Br={full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},Sr={full:"{{date}} 'at' {{time}}",long:"{{date}} 'at' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},Ar={date:gn({formats:Cr,defaultWidth:"full"}),time:gn({formats:Br,defaultWidth:"full"}),dateTime:gn({formats:Sr,defaultWidth:"full"})},$r={lastWeek:"'last' eeee 'at' p",yesterday:"'yesterday at' p",today:"'today at' p",tomorrow:"'tomorrow at' p",nextWeek:"eeee 'at' p",other:"P"},Or=(t,r,n,a)=>$r[t];function Lt(t){return(r,n)=>{const a=n!=null&&n.context?String(n.context):"standalone";let o;if(a==="formatting"&&t.formattingValues){const i=t.defaultFormattingWidth||t.defaultWidth,d=n!=null&&n.width?String(n.width):i;o=t.formattingValues[d]||t.formattingValues[i]}else{const i=t.defaultWidth,d=n!=null&&n.width?String(n.width):t.defaultWidth;o=t.values[d]||t.values[i]}const l=t.argumentCallback?t.argumentCallback(r):r;return o[l]}}const Nr={narrow:["B","A"],abbreviated:["BC","AD"],wide:["Before Christ","Anno Domini"]},Er={narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1st quarter","2nd quarter","3rd quarter","4th quarter"]},Rr={narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],wide:["January","February","March","April","May","June","July","August","September","October","November","December"]},Yr={narrow:["S","M","T","W","T","F","S"],short:["Su","Mo","Tu","We","Th","Fr","Sa"],abbreviated:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],wide:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"]},_r={narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"}},xr={narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"}},Vr={ordinalNumber:(t,r)=>{const n=Number(t),a=n%100;if(a>20||a<10)switch(a%10){case 1:return n+"st";case 2:return n+"nd";case 3:return n+"rd"}return n+"th"},era:Lt({values:Nr,defaultWidth:"wide"}),quarter:Lt({values:Er,defaultWidth:"wide",argumentCallback:t=>t-1}),month:Lt({values:Rr,defaultWidth:"wide"}),day:Lt({values:Yr,defaultWidth:"wide"}),dayPeriod:Lt({values:_r,defaultWidth:"wide",formattingValues:xr,defaultFormattingWidth:"wide"})};function zt(t){return(r,n={})=>{const a=n.width,o=a&&t.matchPatterns[a]||t.matchPatterns[t.defaultMatchWidth],l=r.match(o);if(!l)return null;const i=l[0],d=a&&t.parsePatterns[a]||t.parsePatterns[t.defaultParseWidth],c=Array.isArray(d)?Fr(d,T=>T.test(i)):Ir(d,T=>T.test(i));let v;v=t.valueCallback?t.valueCallback(c):c,v=n.valueCallback?n.valueCallback(v):v;const f=r.slice(i.length);return{value:v,rest:f}}}function Ir(t,r){for(const n in t)if(Object.prototype.hasOwnProperty.call(t,n)&&r(t[n]))return n}function Fr(t,r){for(let n=0;n<t.length;n++)if(r(t[n]))return n}function Lr(t){return(r,n={})=>{const a=r.match(t.matchPattern);if(!a)return null;const o=a[0],l=r.match(t.parsePattern);if(!l)return null;let i=t.valueCallback?t.valueCallback(l[0]):l[0];i=n.valueCallback?n.valueCallback(i):i;const d=r.slice(o.length);return{value:i,rest:d}}}const zr=/^(\d+)(th|st|nd|rd)?/i,Hr=/\d+/i,Wr={narrow:/^(b|a)/i,abbreviated:/^(b\.?\s?c\.?|b\.?\s?c\.?\s?e\.?|a\.?\s?d\.?|c\.?\s?e\.?)/i,wide:/^(before christ|before common era|anno domini|common era)/i},qr={any:[/^b/i,/^(a|c)/i]},Ur={narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234](th|st|nd|rd)? quarter/i},jr={any:[/1/i,/2/i,/3/i,/4/i]},Qr={narrow:/^[jfmasond]/i,abbreviated:/^(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)/i,wide:/^(january|february|march|april|may|june|july|august|september|october|november|december)/i},Gr={narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^ap/i,/^may/i,/^jun/i,/^jul/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},Kr={narrow:/^[smtwf]/i,short:/^(su|mo|tu|we|th|fr|sa)/i,abbreviated:/^(sun|mon|tue|wed|thu|fri|sat)/i,wide:/^(sunday|monday|tuesday|wednesday|thursday|friday|saturday)/i},Xr={narrow:[/^s/i,/^m/i,/^t/i,/^w/i,/^t/i,/^f/i,/^s/i],any:[/^su/i,/^m/i,/^tu/i,/^w/i,/^th/i,/^f/i,/^sa/i]},Jr={narrow:/^(a|p|mi|n|(in the|at) (morning|afternoon|evening|night))/i,any:/^([ap]\.?\s?m\.?|midnight|noon|(in the|at) (morning|afternoon|evening|night))/i},Zr={any:{am:/^a/i,pm:/^p/i,midnight:/^mi/i,noon:/^no/i,morning:/morning/i,afternoon:/afternoon/i,evening:/evening/i,night:/night/i}},eo={ordinalNumber:Lr({matchPattern:zr,parsePattern:Hr,valueCallback:t=>parseInt(t,10)}),era:zt({matchPatterns:Wr,defaultMatchWidth:"wide",parsePatterns:qr,defaultParseWidth:"any"}),quarter:zt({matchPatterns:Ur,defaultMatchWidth:"wide",parsePatterns:jr,defaultParseWidth:"any",valueCallback:t=>t+1}),month:zt({matchPatterns:Qr,defaultMatchWidth:"wide",parsePatterns:Gr,defaultParseWidth:"any"}),day:zt({matchPatterns:Kr,defaultMatchWidth:"wide",parsePatterns:Xr,defaultParseWidth:"any"}),dayPeriod:zt({matchPatterns:Jr,defaultMatchWidth:"any",parsePatterns:Zr,defaultParseWidth:"any"})},pa={code:"en-US",formatDistance:Pr,formatLong:Ar,formatRelative:Or,localize:Vr,match:eo,options:{weekStartsOn:0,firstWeekContainsDate:1}};function to(t,r){const n=ce(t,r==null?void 0:r.in);return ia(n,Ft(n))+1}function yn(t,r){const n=ce(t,r==null?void 0:r.in),a=+Ct(n)-+wr(n);return Math.round(a/na)+1}function pn(t,r){var f,T,m,M;const n=ce(t,r==null?void 0:r.in),a=n.getFullYear(),o=kt(),l=(r==null?void 0:r.firstWeekContainsDate)??((T=(f=r==null?void 0:r.locale)==null?void 0:f.options)==null?void 0:T.firstWeekContainsDate)??o.firstWeekContainsDate??((M=(m=o.locale)==null?void 0:m.options)==null?void 0:M.firstWeekContainsDate)??1,i=ke((r==null?void 0:r.in)||t,0);i.setFullYear(a+1,0,l),i.setHours(0,0,0,0);const d=Ke(i,r),c=ke((r==null?void 0:r.in)||t,0);c.setFullYear(a,0,l),c.setHours(0,0,0,0);const v=Ke(c,r);return+n>=+d?a+1:+n>=+v?a:a-1}function no(t,r){var d,c,v,f;const n=kt(),a=(r==null?void 0:r.firstWeekContainsDate)??((c=(d=r==null?void 0:r.locale)==null?void 0:d.options)==null?void 0:c.firstWeekContainsDate)??n.firstWeekContainsDate??((f=(v=n.locale)==null?void 0:v.options)==null?void 0:f.firstWeekContainsDate)??1,o=pn(t,r),l=ke((r==null?void 0:r.in)||t,0);return l.setFullYear(o,0,a),l.setHours(0,0,0,0),Ke(l,r)}function wn(t,r){const n=ce(t,r==null?void 0:r.in),a=+Ke(n,r)-+no(n,r);return Math.round(a/na)+1}function be(t,r){const n=t<0?"-":"",a=Math.abs(t).toString().padStart(r,"0");return n+a}const ct={y(t,r){const n=t.getFullYear(),a=n>0?n:1-n;return be(r==="yy"?a%100:a,r.length)},M(t,r){const n=t.getMonth();return r==="M"?String(n+1):be(n+1,2)},d(t,r){return be(t.getDate(),r.length)},a(t,r){const n=t.getHours()/12>=1?"pm":"am";switch(r){case"a":case"aa":return n.toUpperCase();case"aaa":return n;case"aaaaa":return n[0];case"aaaa":default:return n==="am"?"a.m.":"p.m."}},h(t,r){return be(t.getHours()%12||12,r.length)},H(t,r){return be(t.getHours(),r.length)},m(t,r){return be(t.getMinutes(),r.length)},s(t,r){return be(t.getSeconds(),r.length)},S(t,r){const n=r.length,a=t.getMilliseconds(),o=Math.trunc(a*Math.pow(10,n-3));return be(o,r.length)}},Bt={am:"am",pm:"pm",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},wa={G:function(t,r,n){const a=t.getFullYear()>0?1:0;switch(r){case"G":case"GG":case"GGG":return n.era(a,{width:"abbreviated"});case"GGGGG":return n.era(a,{width:"narrow"});case"GGGG":default:return n.era(a,{width:"wide"})}},y:function(t,r,n){if(r==="yo"){const a=t.getFullYear(),o=a>0?a:1-a;return n.ordinalNumber(o,{unit:"year"})}return ct.y(t,r)},Y:function(t,r,n,a){const o=pn(t,a),l=o>0?o:1-o;if(r==="YY"){const i=l%100;return be(i,2)}return r==="Yo"?n.ordinalNumber(l,{unit:"year"}):be(l,r.length)},R:function(t,r){const n=la(t);return be(n,r.length)},u:function(t,r){const n=t.getFullYear();return be(n,r.length)},Q:function(t,r,n){const a=Math.ceil((t.getMonth()+1)/3);switch(r){case"Q":return String(a);case"QQ":return be(a,2);case"Qo":return n.ordinalNumber(a,{unit:"quarter"});case"QQQ":return n.quarter(a,{width:"abbreviated",context:"formatting"});case"QQQQQ":return n.quarter(a,{width:"narrow",context:"formatting"});case"QQQQ":default:return n.quarter(a,{width:"wide",context:"formatting"})}},q:function(t,r,n){const a=Math.ceil((t.getMonth()+1)/3);switch(r){case"q":return String(a);case"qq":return be(a,2);case"qo":return n.ordinalNumber(a,{unit:"quarter"});case"qqq":return n.quarter(a,{width:"abbreviated",context:"standalone"});case"qqqqq":return n.quarter(a,{width:"narrow",context:"standalone"});case"qqqq":default:return n.quarter(a,{width:"wide",context:"standalone"})}},M:function(t,r,n){const a=t.getMonth();switch(r){case"M":case"MM":return ct.M(t,r);case"Mo":return n.ordinalNumber(a+1,{unit:"month"});case"MMM":return n.month(a,{width:"abbreviated",context:"formatting"});case"MMMMM":return n.month(a,{width:"narrow",context:"formatting"});case"MMMM":default:return n.month(a,{width:"wide",context:"formatting"})}},L:function(t,r,n){const a=t.getMonth();switch(r){case"L":return String(a+1);case"LL":return be(a+1,2);case"Lo":return n.ordinalNumber(a+1,{unit:"month"});case"LLL":return n.month(a,{width:"abbreviated",context:"standalone"});case"LLLLL":return n.month(a,{width:"narrow",context:"standalone"});case"LLLL":default:return n.month(a,{width:"wide",context:"standalone"})}},w:function(t,r,n,a){const o=wn(t,a);return r==="wo"?n.ordinalNumber(o,{unit:"week"}):be(o,r.length)},I:function(t,r,n){const a=yn(t);return r==="Io"?n.ordinalNumber(a,{unit:"week"}):be(a,r.length)},d:function(t,r,n){return r==="do"?n.ordinalNumber(t.getDate(),{unit:"date"}):ct.d(t,r)},D:function(t,r,n){const a=to(t);return r==="Do"?n.ordinalNumber(a,{unit:"dayOfYear"}):be(a,r.length)},E:function(t,r,n){const a=t.getDay();switch(r){case"E":case"EE":case"EEE":return n.day(a,{width:"abbreviated",context:"formatting"});case"EEEEE":return n.day(a,{width:"narrow",context:"formatting"});case"EEEEEE":return n.day(a,{width:"short",context:"formatting"});case"EEEE":default:return n.day(a,{width:"wide",context:"formatting"})}},e:function(t,r,n,a){const o=t.getDay(),l=(o-a.weekStartsOn+8)%7||7;switch(r){case"e":return String(l);case"ee":return be(l,2);case"eo":return n.ordinalNumber(l,{unit:"day"});case"eee":return n.day(o,{width:"abbreviated",context:"formatting"});case"eeeee":return n.day(o,{width:"narrow",context:"formatting"});case"eeeeee":return n.day(o,{width:"short",context:"formatting"});case"eeee":default:return n.day(o,{width:"wide",context:"formatting"})}},c:function(t,r,n,a){const o=t.getDay(),l=(o-a.weekStartsOn+8)%7||7;switch(r){case"c":return String(l);case"cc":return be(l,r.length);case"co":return n.ordinalNumber(l,{unit:"day"});case"ccc":return n.day(o,{width:"abbreviated",context:"standalone"});case"ccccc":return n.day(o,{width:"narrow",context:"standalone"});case"cccccc":return n.day(o,{width:"short",context:"standalone"});case"cccc":default:return n.day(o,{width:"wide",context:"standalone"})}},i:function(t,r,n){const a=t.getDay(),o=a===0?7:a;switch(r){case"i":return String(o);case"ii":return be(o,r.length);case"io":return n.ordinalNumber(o,{unit:"day"});case"iii":return n.day(a,{width:"abbreviated",context:"formatting"});case"iiiii":return n.day(a,{width:"narrow",context:"formatting"});case"iiiiii":return n.day(a,{width:"short",context:"formatting"});case"iiii":default:return n.day(a,{width:"wide",context:"formatting"})}},a:function(t,r,n){const o=t.getHours()/12>=1?"pm":"am";switch(r){case"a":case"aa":return n.dayPeriod(o,{width:"abbreviated",context:"formatting"});case"aaa":return n.dayPeriod(o,{width:"abbreviated",context:"formatting"}).toLowerCase();case"aaaaa":return n.dayPeriod(o,{width:"narrow",context:"formatting"});case"aaaa":default:return n.dayPeriod(o,{width:"wide",context:"formatting"})}},b:function(t,r,n){const a=t.getHours();let o;switch(a===12?o=Bt.noon:a===0?o=Bt.midnight:o=a/12>=1?"pm":"am",r){case"b":case"bb":return n.dayPeriod(o,{width:"abbreviated",context:"formatting"});case"bbb":return n.dayPeriod(o,{width:"abbreviated",context:"formatting"}).toLowerCase();case"bbbbb":return n.dayPeriod(o,{width:"narrow",context:"formatting"});case"bbbb":default:return n.dayPeriod(o,{width:"wide",context:"formatting"})}},B:function(t,r,n){const a=t.getHours();let o;switch(a>=17?o=Bt.evening:a>=12?o=Bt.afternoon:a>=4?o=Bt.morning:o=Bt.night,r){case"B":case"BB":case"BBB":return n.dayPeriod(o,{width:"abbreviated",context:"formatting"});case"BBBBB":return n.dayPeriod(o,{width:"narrow",context:"formatting"});case"BBBB":default:return n.dayPeriod(o,{width:"wide",context:"formatting"})}},h:function(t,r,n){if(r==="ho"){let a=t.getHours()%12;return a===0&&(a=12),n.ordinalNumber(a,{unit:"hour"})}return ct.h(t,r)},H:function(t,r,n){return r==="Ho"?n.ordinalNumber(t.getHours(),{unit:"hour"}):ct.H(t,r)},K:function(t,r,n){const a=t.getHours()%12;return r==="Ko"?n.ordinalNumber(a,{unit:"hour"}):be(a,r.length)},k:function(t,r,n){let a=t.getHours();return a===0&&(a=24),r==="ko"?n.ordinalNumber(a,{unit:"hour"}):be(a,r.length)},m:function(t,r,n){return r==="mo"?n.ordinalNumber(t.getMinutes(),{unit:"minute"}):ct.m(t,r)},s:function(t,r,n){return r==="so"?n.ordinalNumber(t.getSeconds(),{unit:"second"}):ct.s(t,r)},S:function(t,r){return ct.S(t,r)},X:function(t,r,n){const a=t.getTimezoneOffset();if(a===0)return"Z";switch(r){case"X":return ba(a);case"XXXX":case"XX":return vt(a);case"XXXXX":case"XXX":default:return vt(a,":")}},x:function(t,r,n){const a=t.getTimezoneOffset();switch(r){case"x":return ba(a);case"xxxx":case"xx":return vt(a);case"xxxxx":case"xxx":default:return vt(a,":")}},O:function(t,r,n){const a=t.getTimezoneOffset();switch(r){case"O":case"OO":case"OOO":return"GMT"+ka(a,":");case"OOOO":default:return"GMT"+vt(a,":")}},z:function(t,r,n){const a=t.getTimezoneOffset();switch(r){case"z":case"zz":case"zzz":return"GMT"+ka(a,":");case"zzzz":default:return"GMT"+vt(a,":")}},t:function(t,r,n){const a=Math.trunc(+t/1e3);return be(a,r.length)},T:function(t,r,n){return be(+t,r.length)}};function ka(t,r=""){const n=t>0?"-":"+",a=Math.abs(t),o=Math.trunc(a/60),l=a%60;return l===0?n+String(o):n+String(o)+r+be(l,2)}function ba(t,r){return t%60===0?(t>0?"-":"+")+be(Math.abs(t)/60,2):vt(t,r)}function vt(t,r=""){const n=t>0?"-":"+",a=Math.abs(t),o=be(Math.trunc(a/60),2),l=be(a%60,2);return n+o+r+l}const va=(t,r)=>{switch(t){case"P":return r.date({width:"short"});case"PP":return r.date({width:"medium"});case"PPP":return r.date({width:"long"});case"PPPP":default:return r.date({width:"full"})}},Da=(t,r)=>{switch(t){case"p":return r.time({width:"short"});case"pp":return r.time({width:"medium"});case"ppp":return r.time({width:"long"});case"pppp":default:return r.time({width:"full"})}},kn={p:Da,P:(t,r)=>{const n=t.match(/(P+)(p+)?/)||[],a=n[1],o=n[2];if(!o)return va(t,r);let l;switch(a){case"P":l=r.dateTime({width:"short"});break;case"PP":l=r.dateTime({width:"medium"});break;case"PPP":l=r.dateTime({width:"long"});break;case"PPPP":default:l=r.dateTime({width:"full"});break}return l.replace("{{date}}",va(a,r)).replace("{{time}}",Da(o,r))}},ao=/^D+$/,ro=/^Y+$/,oo=["D","DD","YY","YYYY"];function Ma(t){return ao.test(t)}function Ta(t){return ro.test(t)}function bn(t,r,n){const a=lo(t,r,n);if(console.warn(a),oo.includes(t))throw new RangeError(a)}function lo(t,r,n){const a=t[0]==="Y"?"years":"days of the month";return`Use \`${t.toLowerCase()}\` instead of \`${t}\` (in \`${r}\`) for formatting ${a} to the input \`${n}\`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md`}const so=/[yYQqMLwIdDecihHKkms]o|(\w)\1*|''|'(''|[^'])+('|$)|./g,io=/P+p+|P+|p+|''|'(''|[^'])+('|$)|./g,uo=/^'([^]*?)'?$/,co=/''/g,fo=/[a-zA-Z]/;function nt(t,r,n){var f,T,m,M,_,E,L,h;const a=kt(),o=(n==null?void 0:n.locale)??a.locale??pa,l=(n==null?void 0:n.firstWeekContainsDate)??((T=(f=n==null?void 0:n.locale)==null?void 0:f.options)==null?void 0:T.firstWeekContainsDate)??a.firstWeekContainsDate??((M=(m=a.locale)==null?void 0:m.options)==null?void 0:M.firstWeekContainsDate)??1,i=(n==null?void 0:n.weekStartsOn)??((E=(_=n==null?void 0:n.locale)==null?void 0:_.options)==null?void 0:E.weekStartsOn)??a.weekStartsOn??((h=(L=a.locale)==null?void 0:L.options)==null?void 0:h.weekStartsOn)??0,d=ce(t,n==null?void 0:n.in);if(!It(d))throw new RangeError("Invalid time value");let c=r.match(io).map(A=>{const p=A[0];if(p==="p"||p==="P"){const x=kn[p];return x(A,o.formatLong)}return A}).join("").match(so).map(A=>{if(A==="''")return{isToken:!1,value:"'"};const p=A[0];if(p==="'")return{isToken:!1,value:mo(A)};if(wa[p])return{isToken:!0,value:A};if(p.match(fo))throw new RangeError("Format string contains an unescaped latin alphabet character `"+p+"`");return{isToken:!1,value:A}});o.localize.preprocessor&&(c=o.localize.preprocessor(d,c));const v={firstWeekContainsDate:l,weekStartsOn:i,locale:o};return c.map(A=>{if(!A.isToken)return A.value;const p=A.value;(!(n!=null&&n.useAdditionalWeekYearTokens)&&Ta(p)||!(n!=null&&n.useAdditionalDayOfYearTokens)&&Ma(p))&&bn(p,r,String(t));const x=wa[p[0]];return x(d,p,o.localize,v)}).join("")}function mo(t){const r=t.match(uo);return r?r[1].replace(co,"'"):t}function ho(t,r){return ce(t,r==null?void 0:r.in).getDay()}function go(t,r){const n=ce(t,r==null?void 0:r.in),a=n.getFullYear(),o=n.getMonth(),l=ke(n,0);return l.setFullYear(a,o+1,0),l.setHours(0,0,0,0),l.getDate()}function yo(){return Object.assign({},kt())}function lt(t,r){return ce(t,r==null?void 0:r.in).getHours()}function po(t,r){const n=ce(t,r==null?void 0:r.in).getDay();return n===0?7:n}function dt(t,r){return ce(t,r==null?void 0:r.in).getMinutes()}function ye(t,r){return ce(t,r==null?void 0:r.in).getMonth()}function St(t){return ce(t).getSeconds()}function fe(t,r){return ce(t,r==null?void 0:r.in).getFullYear()}function Dt(t,r){return+ce(t)>+ce(r)}function At(t,r){return+ce(t)<+ce(r)}function $t(t,r){return+ce(t)==+ce(r)}function wo(t,r){const n=ko(r)?new r(0):ke(r,0);return n.setFullYear(t.getFullYear(),t.getMonth(),t.getDate()),n.setHours(t.getHours(),t.getMinutes(),t.getSeconds(),t.getMilliseconds()),n}function ko(t){var r;return typeof t=="function"&&((r=t.prototype)==null?void 0:r.constructor)===t}const bo=10;class Pa{constructor(){oe(this,"subPriority",0)}validate(r,n){return!0}}class vo extends Pa{constructor(r,n,a,o,l){super(),this.value=r,this.validateValue=n,this.setValue=a,this.priority=o,l&&(this.subPriority=l)}validate(r,n){return this.validateValue(r,this.value,n)}set(r,n,a){return this.setValue(r,n,this.value,a)}}class Do extends Pa{constructor(n,a){super();oe(this,"priority",bo);oe(this,"subPriority",-1);this.context=n||(o=>ke(a,o))}set(n,a){return a.timestampIsSet?n:ke(n,wo(n,this.context))}}class we{run(r,n,a,o){const l=this.parse(r,n,a,o);return l?{setter:new vo(l.value,this.validate,this.set,this.priority,this.subPriority),rest:l.rest}:null}validate(r,n,a){return!0}}class Mo extends we{constructor(){super(...arguments);oe(this,"priority",140);oe(this,"incompatibleTokens",["R","u","t","T"])}parse(n,a,o){switch(a){case"G":case"GG":case"GGG":return o.era(n,{width:"abbreviated"})||o.era(n,{width:"narrow"});case"GGGGG":return o.era(n,{width:"narrow"});case"GGGG":default:return o.era(n,{width:"wide"})||o.era(n,{width:"abbreviated"})||o.era(n,{width:"narrow"})}}set(n,a,o){return a.era=o,n.setFullYear(o,0,1),n.setHours(0,0,0,0),n}}const $e={month:/^(1[0-2]|0?\d)/,date:/^(3[0-1]|[0-2]?\d)/,dayOfYear:/^(36[0-6]|3[0-5]\d|[0-2]?\d?\d)/,week:/^(5[0-3]|[0-4]?\d)/,hour23h:/^(2[0-3]|[0-1]?\d)/,hour24h:/^(2[0-4]|[0-1]?\d)/,hour11h:/^(1[0-1]|0?\d)/,hour12h:/^(1[0-2]|0?\d)/,minute:/^[0-5]?\d/,second:/^[0-5]?\d/,singleDigit:/^\d/,twoDigits:/^\d{1,2}/,threeDigits:/^\d{1,3}/,fourDigits:/^\d{1,4}/,anyDigitsSigned:/^-?\d+/,singleDigitSigned:/^-?\d/,twoDigitsSigned:/^-?\d{1,2}/,threeDigitsSigned:/^-?\d{1,3}/,fourDigitsSigned:/^-?\d{1,4}/},at={basicOptionalMinutes:/^([+-])(\d{2})(\d{2})?|Z/,basic:/^([+-])(\d{2})(\d{2})|Z/,basicOptionalSeconds:/^([+-])(\d{2})(\d{2})((\d{2}))?|Z/,extended:/^([+-])(\d{2}):(\d{2})|Z/,extendedOptionalSeconds:/^([+-])(\d{2}):(\d{2})(:(\d{2}))?|Z/};function Oe(t,r){return t&&{value:r(t.value),rest:t.rest}}function Be(t,r){const n=r.match(t);return n?{value:parseInt(n[0],10),rest:r.slice(n[0].length)}:null}function rt(t,r){const n=r.match(t);if(!n)return null;if(n[0]==="Z")return{value:0,rest:r.slice(1)};const a=n[1]==="+"?1:-1,o=n[2]?parseInt(n[2],10):0,l=n[3]?parseInt(n[3],10):0,i=n[5]?parseInt(n[5],10):0;return{value:a*(o*aa+l*mr+i*hr),rest:r.slice(n[0].length)}}function Ca(t){return Be($e.anyDigitsSigned,t)}function Se(t,r){switch(t){case 1:return Be($e.singleDigit,r);case 2:return Be($e.twoDigits,r);case 3:return Be($e.threeDigits,r);case 4:return Be($e.fourDigits,r);default:return Be(new RegExp("^\\d{1,"+t+"}"),r)}}function Jt(t,r){switch(t){case 1:return Be($e.singleDigitSigned,r);case 2:return Be($e.twoDigitsSigned,r);case 3:return Be($e.threeDigitsSigned,r);case 4:return Be($e.fourDigitsSigned,r);default:return Be(new RegExp("^-?\\d{1,"+t+"}"),r)}}function vn(t){switch(t){case"morning":return 4;case"evening":return 17;case"pm":case"noon":case"afternoon":return 12;case"am":case"midnight":case"night":default:return 0}}function Ba(t,r){const n=r>0,a=n?r:1-r;let o;if(a<=50)o=t||100;else{const l=a+50,i=Math.trunc(l/100)*100,d=t>=l%100;o=t+i-(d?100:0)}return n?o:1-o}function Sa(t){return t%400===0||t%4===0&&t%100!==0}class To extends we{constructor(){super(...arguments);oe(this,"priority",130);oe(this,"incompatibleTokens",["Y","R","u","w","I","i","e","c","t","T"])}parse(n,a,o){const l=i=>({year:i,isTwoDigitYear:a==="yy"});switch(a){case"y":return Oe(Se(4,n),l);case"yo":return Oe(o.ordinalNumber(n,{unit:"year"}),l);default:return Oe(Se(a.length,n),l)}}validate(n,a){return a.isTwoDigitYear||a.year>0}set(n,a,o){const l=n.getFullYear();if(o.isTwoDigitYear){const d=Ba(o.year,l);return n.setFullYear(d,0,1),n.setHours(0,0,0,0),n}const i=!("era"in a)||a.era===1?o.year:1-o.year;return n.setFullYear(i,0,1),n.setHours(0,0,0,0),n}}class Po extends we{constructor(){super(...arguments);oe(this,"priority",130);oe(this,"incompatibleTokens",["y","R","u","Q","q","M","L","I","d","D","i","t","T"])}parse(n,a,o){const l=i=>({year:i,isTwoDigitYear:a==="YY"});switch(a){case"Y":return Oe(Se(4,n),l);case"Yo":return Oe(o.ordinalNumber(n,{unit:"year"}),l);default:return Oe(Se(a.length,n),l)}}validate(n,a){return a.isTwoDigitYear||a.year>0}set(n,a,o,l){const i=pn(n,l);if(o.isTwoDigitYear){const c=Ba(o.year,i);return n.setFullYear(c,0,l.firstWeekContainsDate),n.setHours(0,0,0,0),Ke(n,l)}const d=!("era"in a)||a.era===1?o.year:1-o.year;return n.setFullYear(d,0,l.firstWeekContainsDate),n.setHours(0,0,0,0),Ke(n,l)}}class Co extends we{constructor(){super(...arguments);oe(this,"priority",130);oe(this,"incompatibleTokens",["G","y","Y","u","Q","q","M","L","w","d","D","e","c","t","T"])}parse(n,a){return Jt(a==="R"?4:a.length,n)}set(n,a,o){const l=ke(n,0);return l.setFullYear(o,0,4),l.setHours(0,0,0,0),Ct(l)}}class Bo extends we{constructor(){super(...arguments);oe(this,"priority",130);oe(this,"incompatibleTokens",["G","y","Y","R","w","I","i","e","c","t","T"])}parse(n,a){return Jt(a==="u"?4:a.length,n)}set(n,a,o){return n.setFullYear(o,0,1),n.setHours(0,0,0,0),n}}class So extends we{constructor(){super(...arguments);oe(this,"priority",120);oe(this,"incompatibleTokens",["Y","R","q","M","L","w","I","d","D","i","e","c","t","T"])}parse(n,a,o){switch(a){case"Q":case"QQ":return Se(a.length,n);case"Qo":return o.ordinalNumber(n,{unit:"quarter"});case"QQQ":return o.quarter(n,{width:"abbreviated",context:"formatting"})||o.quarter(n,{width:"narrow",context:"formatting"});case"QQQQQ":return o.quarter(n,{width:"narrow",context:"formatting"});case"QQQQ":default:return o.quarter(n,{width:"wide",context:"formatting"})||o.quarter(n,{width:"abbreviated",context:"formatting"})||o.quarter(n,{width:"narrow",context:"formatting"})}}validate(n,a){return a>=1&&a<=4}set(n,a,o){return n.setMonth((o-1)*3,1),n.setHours(0,0,0,0),n}}class Ao extends we{constructor(){super(...arguments);oe(this,"priority",120);oe(this,"incompatibleTokens",["Y","R","Q","M","L","w","I","d","D","i","e","c","t","T"])}parse(n,a,o){switch(a){case"q":case"qq":return Se(a.length,n);case"qo":return o.ordinalNumber(n,{unit:"quarter"});case"qqq":return o.quarter(n,{width:"abbreviated",context:"standalone"})||o.quarter(n,{width:"narrow",context:"standalone"});case"qqqqq":return o.quarter(n,{width:"narrow",context:"standalone"});case"qqqq":default:return o.quarter(n,{width:"wide",context:"standalone"})||o.quarter(n,{width:"abbreviated",context:"standalone"})||o.quarter(n,{width:"narrow",context:"standalone"})}}validate(n,a){return a>=1&&a<=4}set(n,a,o){return n.setMonth((o-1)*3,1),n.setHours(0,0,0,0),n}}class $o extends we{constructor(){super(...arguments);oe(this,"incompatibleTokens",["Y","R","q","Q","L","w","I","D","i","e","c","t","T"]);oe(this,"priority",110)}parse(n,a,o){const l=i=>i-1;switch(a){case"M":return Oe(Be($e.month,n),l);case"MM":return Oe(Se(2,n),l);case"Mo":return Oe(o.ordinalNumber(n,{unit:"month"}),l);case"MMM":return o.month(n,{width:"abbreviated",context:"formatting"})||o.month(n,{width:"narrow",context:"formatting"});case"MMMMM":return o.month(n,{width:"narrow",context:"formatting"});case"MMMM":default:return o.month(n,{width:"wide",context:"formatting"})||o.month(n,{width:"abbreviated",context:"formatting"})||o.month(n,{width:"narrow",context:"formatting"})}}validate(n,a){return a>=0&&a<=11}set(n,a,o){return n.setMonth(o,1),n.setHours(0,0,0,0),n}}class Oo extends we{constructor(){super(...arguments);oe(this,"priority",110);oe(this,"incompatibleTokens",["Y","R","q","Q","M","w","I","D","i","e","c","t","T"])}parse(n,a,o){const l=i=>i-1;switch(a){case"L":return Oe(Be($e.month,n),l);case"LL":return Oe(Se(2,n),l);case"Lo":return Oe(o.ordinalNumber(n,{unit:"month"}),l);case"LLL":return o.month(n,{width:"abbreviated",context:"standalone"})||o.month(n,{width:"narrow",context:"standalone"});case"LLLLL":return o.month(n,{width:"narrow",context:"standalone"});case"LLLL":default:return o.month(n,{width:"wide",context:"standalone"})||o.month(n,{width:"abbreviated",context:"standalone"})||o.month(n,{width:"narrow",context:"standalone"})}}validate(n,a){return a>=0&&a<=11}set(n,a,o){return n.setMonth(o,1),n.setHours(0,0,0,0),n}}function No(t,r,n){const a=ce(t,n==null?void 0:n.in),o=wn(a,n)-r;return a.setDate(a.getDate()-o*7),ce(a,n==null?void 0:n.in)}class Eo extends we{constructor(){super(...arguments);oe(this,"priority",100);oe(this,"incompatibleTokens",["y","R","u","q","Q","M","L","I","d","D","i","t","T"])}parse(n,a,o){switch(a){case"w":return Be($e.week,n);case"wo":return o.ordinalNumber(n,{unit:"week"});default:return Se(a.length,n)}}validate(n,a){return a>=1&&a<=53}set(n,a,o,l){return Ke(No(n,o,l),l)}}function Ro(t,r,n){const a=ce(t,n==null?void 0:n.in),o=yn(a,n)-r;return a.setDate(a.getDate()-o*7),a}class Yo extends we{constructor(){super(...arguments);oe(this,"priority",100);oe(this,"incompatibleTokens",["y","Y","u","q","Q","M","L","w","d","D","e","c","t","T"])}parse(n,a,o){switch(a){case"I":return Be($e.week,n);case"Io":return o.ordinalNumber(n,{unit:"week"});default:return Se(a.length,n)}}validate(n,a){return a>=1&&a<=53}set(n,a,o){return Ct(Ro(n,o))}}const _o=[31,28,31,30,31,30,31,31,30,31,30,31],xo=[31,29,31,30,31,30,31,31,30,31,30,31];class Vo extends we{constructor(){super(...arguments);oe(this,"priority",90);oe(this,"subPriority",1);oe(this,"incompatibleTokens",["Y","R","q","Q","w","I","D","i","e","c","t","T"])}parse(n,a,o){switch(a){case"d":return Be($e.date,n);case"do":return o.ordinalNumber(n,{unit:"date"});default:return Se(a.length,n)}}validate(n,a){const o=n.getFullYear(),l=Sa(o),i=n.getMonth();return l?a>=1&&a<=xo[i]:a>=1&&a<=_o[i]}set(n,a,o){return n.setDate(o),n.setHours(0,0,0,0),n}}class Io extends we{constructor(){super(...arguments);oe(this,"priority",90);oe(this,"subpriority",1);oe(this,"incompatibleTokens",["Y","R","q","Q","M","L","w","I","d","E","i","e","c","t","T"])}parse(n,a,o){switch(a){case"D":case"DD":return Be($e.dayOfYear,n);case"Do":return o.ordinalNumber(n,{unit:"date"});default:return Se(a.length,n)}}validate(n,a){const o=n.getFullYear();return Sa(o)?a>=1&&a<=366:a>=1&&a<=365}set(n,a,o){return n.setMonth(0,o),n.setHours(0,0,0,0),n}}function Dn(t,r,n){var T,m,M,_;const a=kt(),o=(n==null?void 0:n.weekStartsOn)??((m=(T=n==null?void 0:n.locale)==null?void 0:T.options)==null?void 0:m.weekStartsOn)??a.weekStartsOn??((_=(M=a.locale)==null?void 0:M.options)==null?void 0:_.weekStartsOn)??0,l=ce(t,n==null?void 0:n.in),i=l.getDay(),c=(r%7+7)%7,v=7-o,f=r<0||r>6?r-(i+v)%7:(c+v)%7-(i+v)%7;return je(l,f,n)}class Fo extends we{constructor(){super(...arguments);oe(this,"priority",90);oe(this,"incompatibleTokens",["D","i","e","c","t","T"])}parse(n,a,o){switch(a){case"E":case"EE":case"EEE":return o.day(n,{width:"abbreviated",context:"formatting"})||o.day(n,{width:"short",context:"formatting"})||o.day(n,{width:"narrow",context:"formatting"});case"EEEEE":return o.day(n,{width:"narrow",context:"formatting"});case"EEEEEE":return o.day(n,{width:"short",context:"formatting"})||o.day(n,{width:"narrow",context:"formatting"});case"EEEE":default:return o.day(n,{width:"wide",context:"formatting"})||o.day(n,{width:"abbreviated",context:"formatting"})||o.day(n,{width:"short",context:"formatting"})||o.day(n,{width:"narrow",context:"formatting"})}}validate(n,a){return a>=0&&a<=6}set(n,a,o,l){return n=Dn(n,o,l),n.setHours(0,0,0,0),n}}class Lo extends we{constructor(){super(...arguments);oe(this,"priority",90);oe(this,"incompatibleTokens",["y","R","u","q","Q","M","L","I","d","D","E","i","c","t","T"])}parse(n,a,o,l){const i=d=>{const c=Math.floor((d-1)/7)*7;return(d+l.weekStartsOn+6)%7+c};switch(a){case"e":case"ee":return Oe(Se(a.length,n),i);case"eo":return Oe(o.ordinalNumber(n,{unit:"day"}),i);case"eee":return o.day(n,{width:"abbreviated",context:"formatting"})||o.day(n,{width:"short",context:"formatting"})||o.day(n,{width:"narrow",context:"formatting"});case"eeeee":return o.day(n,{width:"narrow",context:"formatting"});case"eeeeee":return o.day(n,{width:"short",context:"formatting"})||o.day(n,{width:"narrow",context:"formatting"});case"eeee":default:return o.day(n,{width:"wide",context:"formatting"})||o.day(n,{width:"abbreviated",context:"formatting"})||o.day(n,{width:"short",context:"formatting"})||o.day(n,{width:"narrow",context:"formatting"})}}validate(n,a){return a>=0&&a<=6}set(n,a,o,l){return n=Dn(n,o,l),n.setHours(0,0,0,0),n}}class zo extends we{constructor(){super(...arguments);oe(this,"priority",90);oe(this,"incompatibleTokens",["y","R","u","q","Q","M","L","I","d","D","E","i","e","t","T"])}parse(n,a,o,l){const i=d=>{const c=Math.floor((d-1)/7)*7;return(d+l.weekStartsOn+6)%7+c};switch(a){case"c":case"cc":return Oe(Se(a.length,n),i);case"co":return Oe(o.ordinalNumber(n,{unit:"day"}),i);case"ccc":return o.day(n,{width:"abbreviated",context:"standalone"})||o.day(n,{width:"short",context:"standalone"})||o.day(n,{width:"narrow",context:"standalone"});case"ccccc":return o.day(n,{width:"narrow",context:"standalone"});case"cccccc":return o.day(n,{width:"short",context:"standalone"})||o.day(n,{width:"narrow",context:"standalone"});case"cccc":default:return o.day(n,{width:"wide",context:"standalone"})||o.day(n,{width:"abbreviated",context:"standalone"})||o.day(n,{width:"short",context:"standalone"})||o.day(n,{width:"narrow",context:"standalone"})}}validate(n,a){return a>=0&&a<=6}set(n,a,o,l){return n=Dn(n,o,l),n.setHours(0,0,0,0),n}}function Ho(t,r,n){const a=ce(t,n==null?void 0:n.in),o=po(a,n),l=r-o;return je(a,l,n)}class Wo extends we{constructor(){super(...arguments);oe(this,"priority",90);oe(this,"incompatibleTokens",["y","Y","u","q","Q","M","L","w","d","D","E","e","c","t","T"])}parse(n,a,o){const l=i=>i===0?7:i;switch(a){case"i":case"ii":return Se(a.length,n);case"io":return o.ordinalNumber(n,{unit:"day"});case"iii":return Oe(o.day(n,{width:"abbreviated",context:"formatting"})||o.day(n,{width:"short",context:"formatting"})||o.day(n,{width:"narrow",context:"formatting"}),l);case"iiiii":return Oe(o.day(n,{width:"narrow",context:"formatting"}),l);case"iiiiii":return Oe(o.day(n,{width:"short",context:"formatting"})||o.day(n,{width:"narrow",context:"formatting"}),l);case"iiii":default:return Oe(o.day(n,{width:"wide",context:"formatting"})||o.day(n,{width:"abbreviated",context:"formatting"})||o.day(n,{width:"short",context:"formatting"})||o.day(n,{width:"narrow",context:"formatting"}),l)}}validate(n,a){return a>=1&&a<=7}set(n,a,o){return n=Ho(n,o),n.setHours(0,0,0,0),n}}class qo extends we{constructor(){super(...arguments);oe(this,"priority",80);oe(this,"incompatibleTokens",["b","B","H","k","t","T"])}parse(n,a,o){switch(a){case"a":case"aa":case"aaa":return o.dayPeriod(n,{width:"abbreviated",context:"formatting"})||o.dayPeriod(n,{width:"narrow",context:"formatting"});case"aaaaa":return o.dayPeriod(n,{width:"narrow",context:"formatting"});case"aaaa":default:return o.dayPeriod(n,{width:"wide",context:"formatting"})||o.dayPeriod(n,{width:"abbreviated",context:"formatting"})||o.dayPeriod(n,{width:"narrow",context:"formatting"})}}set(n,a,o){return n.setHours(vn(o),0,0,0),n}}class Uo extends we{constructor(){super(...arguments);oe(this,"priority",80);oe(this,"incompatibleTokens",["a","B","H","k","t","T"])}parse(n,a,o){switch(a){case"b":case"bb":case"bbb":return o.dayPeriod(n,{width:"abbreviated",context:"formatting"})||o.dayPeriod(n,{width:"narrow",context:"formatting"});case"bbbbb":return o.dayPeriod(n,{width:"narrow",context:"formatting"});case"bbbb":default:return o.dayPeriod(n,{width:"wide",context:"formatting"})||o.dayPeriod(n,{width:"abbreviated",context:"formatting"})||o.dayPeriod(n,{width:"narrow",context:"formatting"})}}set(n,a,o){return n.setHours(vn(o),0,0,0),n}}class jo extends we{constructor(){super(...arguments);oe(this,"priority",80);oe(this,"incompatibleTokens",["a","b","t","T"])}parse(n,a,o){switch(a){case"B":case"BB":case"BBB":return o.dayPeriod(n,{width:"abbreviated",context:"formatting"})||o.dayPeriod(n,{width:"narrow",context:"formatting"});case"BBBBB":return o.dayPeriod(n,{width:"narrow",context:"formatting"});case"BBBB":default:return o.dayPeriod(n,{width:"wide",context:"formatting"})||o.dayPeriod(n,{width:"abbreviated",context:"formatting"})||o.dayPeriod(n,{width:"narrow",context:"formatting"})}}set(n,a,o){return n.setHours(vn(o),0,0,0),n}}class Qo extends we{constructor(){super(...arguments);oe(this,"priority",70);oe(this,"incompatibleTokens",["H","K","k","t","T"])}parse(n,a,o){switch(a){case"h":return Be($e.hour12h,n);case"ho":return o.ordinalNumber(n,{unit:"hour"});default:return Se(a.length,n)}}validate(n,a){return a>=1&&a<=12}set(n,a,o){const l=n.getHours()>=12;return l&&o<12?n.setHours(o+12,0,0,0):!l&&o===12?n.setHours(0,0,0,0):n.setHours(o,0,0,0),n}}class Go extends we{constructor(){super(...arguments);oe(this,"priority",70);oe(this,"incompatibleTokens",["a","b","h","K","k","t","T"])}parse(n,a,o){switch(a){case"H":return Be($e.hour23h,n);case"Ho":return o.ordinalNumber(n,{unit:"hour"});default:return Se(a.length,n)}}validate(n,a){return a>=0&&a<=23}set(n,a,o){return n.setHours(o,0,0,0),n}}class Ko extends we{constructor(){super(...arguments);oe(this,"priority",70);oe(this,"incompatibleTokens",["h","H","k","t","T"])}parse(n,a,o){switch(a){case"K":return Be($e.hour11h,n);case"Ko":return o.ordinalNumber(n,{unit:"hour"});default:return Se(a.length,n)}}validate(n,a){return a>=0&&a<=11}set(n,a,o){return n.getHours()>=12&&o<12?n.setHours(o+12,0,0,0):n.setHours(o,0,0,0),n}}class Xo extends we{constructor(){super(...arguments);oe(this,"priority",70);oe(this,"incompatibleTokens",["a","b","h","H","K","t","T"])}parse(n,a,o){switch(a){case"k":return Be($e.hour24h,n);case"ko":return o.ordinalNumber(n,{unit:"hour"});default:return Se(a.length,n)}}validate(n,a){return a>=1&&a<=24}set(n,a,o){const l=o<=24?o%24:o;return n.setHours(l,0,0,0),n}}class Jo extends we{constructor(){super(...arguments);oe(this,"priority",60);oe(this,"incompatibleTokens",["t","T"])}parse(n,a,o){switch(a){case"m":return Be($e.minute,n);case"mo":return o.ordinalNumber(n,{unit:"minute"});default:return Se(a.length,n)}}validate(n,a){return a>=0&&a<=59}set(n,a,o){return n.setMinutes(o,0,0),n}}class Zo extends we{constructor(){super(...arguments);oe(this,"priority",50);oe(this,"incompatibleTokens",["t","T"])}parse(n,a,o){switch(a){case"s":return Be($e.second,n);case"so":return o.ordinalNumber(n,{unit:"second"});default:return Se(a.length,n)}}validate(n,a){return a>=0&&a<=59}set(n,a,o){return n.setSeconds(o,0),n}}class el extends we{constructor(){super(...arguments);oe(this,"priority",30);oe(this,"incompatibleTokens",["t","T"])}parse(n,a){const o=l=>Math.trunc(l*Math.pow(10,-a.length+3));return Oe(Se(a.length,n),o)}set(n,a,o){return n.setMilliseconds(o),n}}class tl extends we{constructor(){super(...arguments);oe(this,"priority",10);oe(this,"incompatibleTokens",["t","T","x"])}parse(n,a){switch(a){case"X":return rt(at.basicOptionalMinutes,n);case"XX":return rt(at.basic,n);case"XXXX":return rt(at.basicOptionalSeconds,n);case"XXXXX":return rt(at.extendedOptionalSeconds,n);case"XXX":default:return rt(at.extended,n)}}set(n,a,o){return a.timestampIsSet?n:ke(n,n.getTime()-Xt(n)-o)}}class nl extends we{constructor(){super(...arguments);oe(this,"priority",10);oe(this,"incompatibleTokens",["t","T","X"])}parse(n,a){switch(a){case"x":return rt(at.basicOptionalMinutes,n);case"xx":return rt(at.basic,n);case"xxxx":return rt(at.basicOptionalSeconds,n);case"xxxxx":return rt(at.extendedOptionalSeconds,n);case"xxx":default:return rt(at.extended,n)}}set(n,a,o){return a.timestampIsSet?n:ke(n,n.getTime()-Xt(n)-o)}}class al extends we{constructor(){super(...arguments);oe(this,"priority",40);oe(this,"incompatibleTokens","*")}parse(n){return Ca(n)}set(n,a,o){return[ke(n,o*1e3),{timestampIsSet:!0}]}}class rl extends we{constructor(){super(...arguments);oe(this,"priority",20);oe(this,"incompatibleTokens","*")}parse(n){return Ca(n)}set(n,a,o){return[ke(n,o),{timestampIsSet:!0}]}}const ol={G:new Mo,y:new To,Y:new Po,R:new Co,u:new Bo,Q:new So,q:new Ao,M:new $o,L:new Oo,w:new Eo,I:new Yo,d:new Vo,D:new Io,E:new Fo,e:new Lo,c:new zo,i:new Wo,a:new qo,b:new Uo,B:new jo,h:new Qo,H:new Go,K:new Ko,k:new Xo,m:new Jo,s:new Zo,S:new el,X:new tl,x:new nl,t:new al,T:new rl},ll=/[yYQqMLwIdDecihHKkms]o|(\w)\1*|''|'(''|[^'])+('|$)|./g,sl=/P+p+|P+|p+|''|'(''|[^'])+('|$)|./g,il=/^'([^]*?)'?$/,ul=/''/g,cl=/\S/,dl=/[a-zA-Z]/;function Mn(t,r,n,a){var L,h,A,p,x,U,G,Q;const o=()=>ke((a==null?void 0:a.in)||n,NaN),l=yo(),i=(a==null?void 0:a.locale)??l.locale??pa,d=(a==null?void 0:a.firstWeekContainsDate)??((h=(L=a==null?void 0:a.locale)==null?void 0:L.options)==null?void 0:h.firstWeekContainsDate)??l.firstWeekContainsDate??((p=(A=l.locale)==null?void 0:A.options)==null?void 0:p.firstWeekContainsDate)??1,c=(a==null?void 0:a.weekStartsOn)??((U=(x=a==null?void 0:a.locale)==null?void 0:x.options)==null?void 0:U.weekStartsOn)??l.weekStartsOn??((Q=(G=l.locale)==null?void 0:G.options)==null?void 0:Q.weekStartsOn)??0;if(!r)return t?o():ce(n,a==null?void 0:a.in);const v={firstWeekContainsDate:d,weekStartsOn:c,locale:i},f=[new Do(a==null?void 0:a.in,n)],T=r.match(sl).map(g=>{const R=g[0];if(R in kn){const O=kn[R];return O(g,i.formatLong)}return g}).join("").match(ll),m=[];for(let g of T){!(a!=null&&a.useAdditionalWeekYearTokens)&&Ta(g)&&bn(g,r,t),!(a!=null&&a.useAdditionalDayOfYearTokens)&&Ma(g)&&bn(g,r,t);const R=g[0],O=ol[R];if(O){const{incompatibleTokens:W}=O;if(Array.isArray(W)){const ue=m.find(y=>W.includes(y.token)||y.token===R);if(ue)throw new RangeError(`The format string mustn't contain \`${ue.fullToken}\` and \`${g}\` at the same time`)}else if(O.incompatibleTokens==="*"&&m.length>0)throw new RangeError(`The format string mustn't contain \`${g}\` and any other token at the same time`);m.push({token:R,fullToken:g});const le=O.run(t,g,i.match,v);if(!le)return o();f.push(le.setter),t=le.rest}else{if(R.match(dl))throw new RangeError("Format string contains an unescaped latin alphabet character `"+R+"`");if(g==="''"?g="'":R==="'"&&(g=fl(g)),t.indexOf(g)===0)t=t.slice(g.length);else return o()}}if(t.length>0&&cl.test(t))return o();const M=f.map(g=>g.priority).sort((g,R)=>R-g).filter((g,R,O)=>O.indexOf(g)===R).map(g=>f.filter(R=>R.priority===g).sort((R,O)=>O.subPriority-R.subPriority)).map(g=>g[0]);let _=ce(n,a==null?void 0:a.in);if(isNaN(+_))return o();const E={};for(const g of M){if(!g.validate(_,v))return o();const R=g.set(_,E,v);Array.isArray(R)?(_=R[0],Object.assign(E,R[1])):_=R}return _}function fl(t){return t.match(il)[1].replace(ul,"'")}function Aa(t,r,n){const[a,o]=Vt(n==null?void 0:n.in,t,r);return+bt(a)==+bt(o)}function $a(t,r,n){return je(t,-r,n)}function Oa(t,r,n){const a=ce(t,n==null?void 0:n.in),o=a.getFullYear(),l=a.getDate(),i=ke(t,0);i.setFullYear(o,r,15),i.setHours(0,0,0,0);const d=go(i);return a.setMonth(r,Math.min(l,d)),a}function ve(t,r,n){let a=ce(t,n==null?void 0:n.in);return isNaN(+a)?ke(t,NaN):(r.year!=null&&a.setFullYear(r.year),r.month!=null&&(a=Oa(a,r.month)),r.date!=null&&a.setDate(r.date),r.hours!=null&&a.setHours(r.hours),r.minutes!=null&&a.setMinutes(r.minutes),r.seconds!=null&&a.setSeconds(r.seconds),r.milliseconds!=null&&a.setMilliseconds(r.milliseconds),a)}function ml(t,r,n){const a=ce(t,n==null?void 0:n.in);return a.setHours(r),a}function Na(t,r,n){const a=ce(t,n==null?void 0:n.in);return a.setMilliseconds(r),a}function hl(t,r,n){const a=ce(t,n==null?void 0:n.in);return a.setMinutes(r),a}function Ea(t,r,n){const a=ce(t,n==null?void 0:n.in);return a.setSeconds(r),a}function ot(t,r,n){const a=ce(t,n==null?void 0:n.in);return isNaN(+a)?ke(t,NaN):(a.setFullYear(r),a)}function Ot(t,r,n){return Ge(t,-r,n)}function gl(t,r,n){const{years:a=0,months:o=0,weeks:l=0,days:i=0,hours:d=0,minutes:c=0,seconds:v=0}=r,f=Ot(t,o+a*12,n),T=$a(f,i+l*7,n),m=c+d*60,_=(v+m*60)*1e3;return ke(t,+T-_)}function Ra(t,r,n){return hn(t,-r,n)}const qe=(t,r)=>r?new Date(t.toLocaleString("en-US",{timeZone:r})):new Date(t),Tn=(t,r,n)=>{const a=Pn(t,r,n);return a||q()},yl=(t,r,n)=>{const a=r.dateInTz?qe(new Date(t),r.dateInTz):q(t);return n?xe(a,!0):a},Pn=(t,r,n)=>{if(!t)return null;const a=n?xe(q(t),!0):q(t);return r?r.exactMatch?yl(t,r,n):qe(a,r.timezone):a},pl=t=>{const n=new Date(t.getFullYear(),0,1).getTimezoneOffset();return t.getTimezoneOffset()<n},wl=(t,r)=>{if(!t)return 0;const n=new Date,a=new Date(n.toLocaleString("en-US",{timeZone:"UTC"})),o=new Date(n.toLocaleString("en-US",{timeZone:t})),i=(pl(r??o)?o:r??o).getTimezoneOffset()/60;return(+a-+o)/(1e3*60*60)-i};var Xe=(t=>(t.month="month",t.year="year",t))(Xe||{}),Je=(t=>(t.top="top",t.bottom="bottom",t))(Je||{}),Mt=(t=>(t.header="header",t.calendar="calendar",t.timePicker="timePicker",t))(Mt||{}),Ie=(t=>(t.month="month",t.year="year",t.calendar="calendar",t.time="time",t.minutes="minutes",t.hours="hours",t.seconds="seconds",t))(Ie||{});const kl=["timestamp","date","iso"];var Le=(t=>(t.up="up",t.down="down",t.left="left",t.right="right",t))(Le||{}),Te=(t=>(t.arrowUp="ArrowUp",t.arrowDown="ArrowDown",t.arrowLeft="ArrowLeft",t.arrowRight="ArrowRight",t.enter="Enter",t.space=" ",t.esc="Escape",t.tab="Tab",t.home="Home",t.end="End",t.pageUp="PageUp",t.pageDown="PageDown",t))(Te||{}),Nt=(t=>(t.MONTH_AND_YEAR="MM-yyyy",t.YEAR="yyyy",t.DATE="dd-MM-yyyy",t))(Nt||{});function Ya(t){return r=>new Intl.DateTimeFormat(t,{weekday:"short",timeZone:"UTC"}).format(new Date(`2017-01-0${r}T00:00:00+00:00`)).slice(0,2)}function bl(t){return r=>nt(qe(new Date(`2017-01-0${r}T00:00:00+00:00`),"UTC"),"EEEEEE",{locale:t})}const vl=(t,r,n)=>{const a=[1,2,3,4,5,6,7];let o;if(t!==null)try{o=a.map(bl(t))}catch{o=a.map(Ya(r))}else o=a.map(Ya(r));const l=o.slice(0,n),i=o.slice(n+1,o.length);return[o[n]].concat(...i).concat(...l)},Cn=(t,r,n)=>{const a=[];for(let o=+t[0];o<=+t[1];o++)a.push({value:+o,text:La(o,r)});return n?a.reverse():a},_a=(t,r,n)=>{const a=[1,2,3,4,5,6,7,8,9,10,11,12].map(l=>{const i=l<10?`0${l}`:l;return new Date(`2017-${i}-01T00:00:00+00:00`)});if(t!==null)try{const l=n==="long"?"LLLL":"LLL";return a.map((i,d)=>{const c=nt(qe(i,"UTC"),l,{locale:t});return{text:c.charAt(0).toUpperCase()+c.substring(1),value:d}})}catch{}const o=new Intl.DateTimeFormat(r,{month:n,timeZone:"UTC"});return a.map((l,i)=>{const d=o.format(l);return{text:d.charAt(0).toUpperCase()+d.substring(1),value:i}})},Dl=t=>[12,1,2,3,4,5,6,7,8,9,10,11,12,1,2,3,4,5,6,7,8,9,10,11][t],Ye=t=>{const r=e.unref(t);return r!=null&&r.$el?r==null?void 0:r.$el:r},Ml=t=>({type:"dot",...t??{}}),xa=t=>Array.isArray(t)?!!t[0]&&!!t[1]:!1,Bn={prop:t=>`"${t}" prop must be enabled!`,dateArr:t=>`You need to use array as "model-value" binding in order to support "${t}"`},Re=t=>t,Va=t=>t===0?t:!t||isNaN(+t)?null:+t,Ia=t=>t===null,Fa=t=>{if(t)return[...t.querySelectorAll("input, button, select, textarea, a[href]")][0]},Tl=t=>{const r=[],n=a=>a.filter(o=>o);for(let a=0;a<t.length;a+=3){const o=[t[a],t[a+1],t[a+2]];r.push(n(o))}return r},Ht=(t,r,n)=>{const a=n!=null,o=r!=null;if(!a&&!o)return!1;const l=+n,i=+r;return a&&o?+t>l||+t<i:a?+t>l:o?+t<i:!1},Et=(t,r)=>Tl(t).map(n=>n.map(a=>{const{active:o,disabled:l,isBetween:i,highlighted:d}=r(a);return{...a,active:o,disabled:l,className:{dp__overlay_cell_active:o,dp__overlay_cell:!o,dp__overlay_cell_disabled:l,dp__overlay_cell_pad:!0,dp__overlay_cell_active_disabled:l&&o,dp__cell_in_between:i,"dp--highlighted":d}}})),ft=(t,r,n=!1)=>{t&&r.allowStopPropagation&&(n&&t.stopImmediatePropagation(),t.stopPropagation())},Pl=()=>["a[href]","area[href]","input:not([disabled]):not([type='hidden'])","select:not([disabled])","textarea:not([disabled])","button:not([disabled])","[tabindex]:not([tabindex='-1'])","[data-datepicker-instance]"].join(", ");function Cl(t,r){let n=[...document.querySelectorAll(Pl())];n=n.filter(o=>!t.contains(o)||o.hasAttribute("data-datepicker-instance"));const a=n.indexOf(t);if(a>=0&&(r?a-1>=0:a+1<=n.length))return n[a+(r?-1:1)]}const Sn=(t,r)=>t==null?void 0:t.querySelector(`[data-dp-element="${r}"]`),La=(t,r)=>new Intl.NumberFormat(r,{useGrouping:!1,style:"decimal"}).format(t),An=(t,r)=>nt(t,r??Nt.DATE),$n=t=>Array.isArray(t),Zt=(t,r,n)=>r.get(An(t,n)),Bl=(t,r)=>t?r?r instanceof Map?!!Zt(t,r):r(q(t)):!1:!0,ze=(t,r,n=!1,a)=>{if(t.key===Te.enter||t.key===Te.space)return n&&t.preventDefault(),r();if(a)return a(t)},Sl=()=>"ontouchstart"in window||navigator.maxTouchPoints>0,Al=(t,r)=>t?Nt.MONTH_AND_YEAR:r?Nt.YEAR:Nt.DATE,za=t=>t<10?`0${t}`:t,Ha=(t,r,n,a,o,l)=>{const i=Mn(t,r.slice(0,t.length),new Date,{locale:l});return It(i)&&ca(i)?a||o?i:ve(i,{hours:+n.hours,minutes:+(n==null?void 0:n.minutes),seconds:+(n==null?void 0:n.seconds),milliseconds:0}):null},$l=(t,r,n,a,o,l)=>{const i=Array.isArray(n)?n[0]:n;if(typeof r=="string")return Ha(t,r,i,a,o,l);if(Array.isArray(r)){let d=null;for(const c of r)if(d=Ha(t,c,i,a,o,l),d)break;return d}return typeof r=="function"?r(t):null},q=t=>t?new Date(t):new Date,Ol=(t,r,n)=>{if(r){const o=(t.getMonth()+1).toString().padStart(2,"0"),l=t.getDate().toString().padStart(2,"0"),i=t.getHours().toString().padStart(2,"0"),d=t.getMinutes().toString().padStart(2,"0"),c=n?t.getSeconds().toString().padStart(2,"0"):"00";return`${t.getFullYear()}-${o}-${l}T${i}:${d}:${c}.000Z`}const a=Date.UTC(t.getUTCFullYear(),t.getUTCMonth(),t.getUTCDate(),t.getUTCHours(),t.getUTCMinutes(),t.getUTCSeconds());return new Date(a).toISOString()},xe=(t,r)=>{const n=q(JSON.parse(JSON.stringify(t))),a=ve(n,{hours:0,minutes:0,seconds:0,milliseconds:0});return r?Mr(a):a},mt=(t,r,n,a)=>{let o=t?q(t):q();return(r||r===0)&&(o=ml(o,+r)),(n||n===0)&&(o=hl(o,+n)),(a||a===0)&&(o=Ea(o,+a)),Na(o,0)},Ae=(t,r)=>!t||!r?!1:At(xe(t),xe(r)),pe=(t,r)=>!t||!r?!1:$t(xe(t),xe(r)),Ne=(t,r)=>!t||!r?!1:Dt(xe(t),xe(r)),Wt=(t,r,n)=>t!=null&&t[0]&&(t!=null&&t[1])?Ne(n,t[0])&&Ae(n,t[1]):t!=null&&t[0]&&r?Ne(n,t[0])&&Ae(n,r)||Ae(n,t[0])&&Ne(n,r):!1,Ze=t=>{const r=ve(new Date(t),{date:1});return xe(r)},On=(t,r,n)=>r&&(n||n===0)?Object.fromEntries(["hours","minutes","seconds"].map(a=>a===r?[a,n]:[a,isNaN(+t[a])?void 0:+t[a]])):{hours:isNaN(+t.hours)?void 0:+t.hours,minutes:isNaN(+t.minutes)?void 0:+t.minutes,seconds:isNaN(+t.seconds)?void 0:+t.seconds},Tt=t=>({hours:lt(t),minutes:dt(t),seconds:St(t)}),Wa=(t,r)=>{if(r){const n=fe(q(r));if(n>t)return 12;if(n===t)return ye(q(r))}},qa=(t,r)=>{if(r){const n=fe(q(r));return n<t?-1:n===t?ye(q(r)):void 0}},Rt=t=>{if(t)return fe(q(t))},Ua=(t,r)=>{const n=Ne(t,r)?r:t,a=Ne(r,t)?r:t;return ma({start:n,end:a})},Nl=t=>{const r=Ge(t,1);return{month:ye(r),year:fe(r)}},st=(t,r)=>{const n=Ke(t,{weekStartsOn:+r}),a=ga(t,{weekStartsOn:+r});return[n,a]},ja=(t,r)=>{const n={hours:lt(q()),minutes:dt(q()),seconds:r?St(q()):0};return Object.assign(n,t)},ht=(t,r,n)=>[ve(q(t),{date:1}),ve(q(),{month:r,year:n,date:1})],it=(t,r,n)=>{let a=t?q(t):q();return(r||r===0)&&(a=Oa(a,r)),n&&(a=ot(a,n)),a},Qa=(t,r,n,a,o)=>{if(!a||o&&!r||!o&&!n)return!1;const l=o?Ge(t,1):Ot(t,1),i=[ye(l),fe(l)];return o?!Rl(...i,r):!El(...i,n)},El=(t,r,n)=>Ae(...ht(n,t,r))||pe(...ht(n,t,r)),Rl=(t,r,n)=>Ne(...ht(n,t,r))||pe(...ht(n,t,r)),Ga=(t,r,n,a,o,l,i)=>{if(typeof r=="function"&&!i)return r(t);const d=n?{locale:n}:void 0;return Array.isArray(t)?`${nt(t[0],l,d)}${o&&!t[1]?"":a}${t[1]?nt(t[1],l,d):""}`:nt(t,l,d)},Yt=t=>{if(t)return null;throw new Error(Bn.prop("partial-range"))},en=(t,r)=>{if(r)return t();throw new Error(Bn.prop("range"))},Nn=t=>Array.isArray(t)?It(t[0])&&(t[1]?It(t[1]):!0):t?It(t):!1,Yl=(t,r)=>ve(r??q(),{hours:+t.hours||0,minutes:+t.minutes||0,seconds:+t.seconds||0}),En=(t,r,n,a)=>{if(!t)return!0;if(a){const o=n==="max"?At(t,r):Dt(t,r),l={seconds:0,milliseconds:0};return o||$t(ve(t,l),ve(r,l))}return n==="max"?t.getTime()<=r.getTime():t.getTime()>=r.getTime()},Rn=(t,r,n)=>t?Yl(t,r):q(n??r),Ka=(t,r,n,a,o)=>{if(Array.isArray(a)){const i=Rn(t,a[0],r),d=Rn(t,a[1],r);return En(a[0],i,n,!!r)&&En(a[1],d,n,!!r)&&o}const l=Rn(t,a,r);return En(a,l,n,!!r)&&o},Yn=t=>ve(q(),Tt(t)),_l=(t,r,n)=>{if(t instanceof Map){const a=`${za(n+1)}-${r}`;return t.size?t.has(a):!1}return!1},xl=(t,r,n)=>{if(t instanceof Map){const a=`${za(n+1)}-${r}`;return t.size?t.has(a):!0}return!0},Xa=(t,r,n)=>typeof t=="function"?t({month:r,year:n}):!!t.months.find(a=>a.month===r&&a.year===n),_n=(t,r)=>typeof t=="function"?t(r):t.years.includes(r),xn=t=>`dp-${nt(t,"yyyy-MM-dd")}`,Ja=(t,r)=>{const n=$a(xe(r),t),a=je(xe(r),t);return{before:n,after:a}},qt=e.reactive({menuFocused:!1,shiftKeyInMenu:!1}),Za=()=>{const t=a=>{qt.menuFocused=a},r=a=>{qt.shiftKeyInMenu!==a&&(qt.shiftKeyInMenu=a)};return{control:e.computed(()=>({shiftKeyInMenu:qt.shiftKeyInMenu,menuFocused:qt.menuFocused})),setMenuFocused:t,setShiftKey:r}},Pe=e.reactive({monthYear:[],calendar:[],time:[],actionRow:[],selectionGrid:[],timePicker:{0:[],1:[]},monthPicker:[]}),Vn=e.ref(null),tn=e.ref(!1),In=e.ref(!1),Fn=e.ref(!1),Ln=e.ref(!1),Fe=e.ref(0),Ee=e.ref(0),gt=()=>{const t=e.computed(()=>tn.value?[...Pe.selectionGrid,Pe.actionRow].filter(h=>h.length):In.value?[...Pe.timePicker[0],...Pe.timePicker[1],Ln.value?[]:[Vn.value],Pe.actionRow].filter(h=>h.length):Fn.value?[...Pe.monthPicker,Pe.actionRow]:[Pe.monthYear,...Pe.calendar,Pe.time,Pe.actionRow].filter(h=>h.length)),r=h=>{Fe.value=h?Fe.value+1:Fe.value-1;let A=null;t.value[Ee.value]&&(A=t.value[Ee.value][Fe.value]),!A&&t.value[Ee.value+(h?1:-1)]?(Ee.value=Ee.value+(h?1:-1),Fe.value=h?0:t.value[Ee.value].length-1):A||(Fe.value=h?Fe.value-1:Fe.value+1)},n=h=>{if(Ee.value===0&&!h||Ee.value===t.value.length&&h)return;Ee.value=h?Ee.value+1:Ee.value-1,t.value[Ee.value]?t.value[Ee.value]&&!t.value[Ee.value][Fe.value]&&Fe.value!==0&&(Fe.value=t.value[Ee.value].length-1):Ee.value=h?Ee.value-1:Ee.value+1},a=h=>{let A=null;t.value[Ee.value]&&(A=t.value[Ee.value][Fe.value]),A?A.focus({preventScroll:!tn.value}):Fe.value=h?Fe.value-1:Fe.value+1},o=()=>{r(!0),a(!0)},l=()=>{r(!1),a(!1)},i=()=>{n(!1),a(!0)},d=()=>{n(!0),a(!0)},c=(h,A)=>{Pe[A]=h},v=(h,A)=>{Pe[A]=h},f=()=>{Fe.value=0,Ee.value=0};return{buildMatrix:c,buildMultiLevelMatrix:v,setTimePickerBackRef:h=>{Vn.value=h},setSelectionGrid:h=>{tn.value=h,f(),h||(Pe.selectionGrid=[])},setTimePicker:(h,A=!1)=>{In.value=h,Ln.value=A,f(),h||(Pe.timePicker[0]=[],Pe.timePicker[1]=[])},setTimePickerElements:(h,A=0)=>{Pe.timePicker[A]=h},arrowRight:o,arrowLeft:l,arrowUp:i,arrowDown:d,clearArrowNav:()=>{Pe.monthYear=[],Pe.calendar=[],Pe.time=[],Pe.actionRow=[],Pe.selectionGrid=[],Pe.timePicker[0]=[],Pe.timePicker[1]=[],tn.value=!1,In.value=!1,Ln.value=!1,Fn.value=!1,f(),Vn.value=null},setMonthPicker:h=>{Fn.value=h,f()},refSets:Pe}},er=t=>({menuAppearTop:"dp-menu-appear-top",menuAppearBottom:"dp-menu-appear-bottom",open:"dp-slide-down",close:"dp-slide-up",next:"calendar-next",previous:"calendar-prev",vNext:"dp-slide-up",vPrevious:"dp-slide-down",...t??{}}),Vl=t=>({toggleOverlay:"Toggle overlay",menu:"Datepicker menu",input:"Datepicker input",openTimePicker:"Open time picker",closeTimePicker:"Close time Picker",incrementValue:r=>`Increment ${r}`,decrementValue:r=>`Decrement ${r}`,openTpOverlay:r=>`Open ${r} overlay`,amPmButton:"Switch AM/PM mode",openYearsOverlay:"Open years overlay",openMonthsOverlay:"Open months overlay",nextMonth:"Next month",prevMonth:"Previous month",nextYear:"Next year",prevYear:"Previous year",day:void 0,weekDay:void 0,clearInput:"Clear value",calendarIcon:"Calendar icon",timePicker:"Time picker",monthPicker:r=>`Month picker${r?" overlay":""}`,yearPicker:r=>`Year picker${r?" overlay":""}`,timeOverlay:r=>`${r} overlay`,...t??{}}),tr=t=>t?typeof t=="boolean"?t?2:0:+t>=2?+t:2:0,Il=t=>{const r=typeof t=="object"&&t,n={static:!0,solo:!1};if(!t)return{...n,count:tr(!1)};const a=r?t:{},o=r?a.count??!0:t,l=tr(o);return Object.assign(n,a,{count:l})},Fl=(t,r,n)=>t||(typeof n=="string"?n:r),Ll=t=>typeof t=="boolean"?t?er({}):!1:er(t),zl=t=>{const r={enterSubmit:!0,tabSubmit:!0,openMenu:"open",selectOnFocus:!1,rangeSeparator:" - ",escClose:!0};return typeof t=="object"?{...r,...t??{},enabled:!0}:{...r,enabled:t}},Hl=t=>({months:[],years:[],times:{hours:[],minutes:[],seconds:[]},...t??{}}),Wl=t=>({showSelect:!0,showCancel:!0,showNow:!1,showPreview:!0,...t??{}}),ql=t=>{const r={input:!1};return typeof t=="object"?{...r,...t??{},enabled:!0}:{enabled:t,...r}},Ul=t=>({...{allowStopPropagation:!0,closeOnScroll:!1,modeHeight:255,allowPreventDefault:!1,closeOnClearValue:!0,closeOnAutoApply:!0,noSwipe:!1,keepActionRow:!1,onClickOutside:void 0,tabOutClosesMenu:!0,arrowLeft:void 0,keepViewOnOffsetClick:!1,timeArrowHoldThreshold:0,shadowDom:!1,mobileBreakpoint:600,setDateOnMenuClose:!1},...t??{}}),jl=t=>{const r={dates:Array.isArray(t)?t.map(n=>q(n)):[],years:[],months:[],quarters:[],weeks:[],weekdays:[],options:{highlightDisabled:!1}};return typeof t=="function"?t:{...r,...t??{}}},Ql=t=>typeof t=="object"?{type:(t==null?void 0:t.type)??"local",hideOnOffsetDates:(t==null?void 0:t.hideOnOffsetDates)??!1}:{type:t,hideOnOffsetDates:!1},Gl=t=>{const r={noDisabledRange:!1,showLastInRange:!0,minMaxRawRange:!1,partialRange:!0,disableTimeRangeValidation:!1,maxRange:void 0,minRange:void 0,autoRange:void 0,fixedStart:!1,fixedEnd:!1};return typeof t=="object"?{enabled:!0,...r,...t}:{enabled:t,...r}},Kl=t=>t?typeof t=="string"?{timezone:t,exactMatch:!1,dateInTz:void 0,emitTimezone:void 0,convertModel:!0}:{timezone:t.timezone,exactMatch:t.exactMatch??!1,dateInTz:t.dateInTz??void 0,emitTimezone:t.emitTimezone??void 0,convertModel:t.convertModel??!0}:{timezone:void 0,exactMatch:!1,emitTimezone:void 0},zn=(t,r,n,a)=>new Map(t.map(o=>{const l=Tn(o,r,a);return[An(l,n),l]})),Xl=(t,r)=>t.length?new Map(t.map(n=>{const a=Tn(n.date,r);return[An(a,Nt.DATE),n]})):null,Jl=t=>{var n;const r=Al(t.isMonthPicker,t.isYearPicker);return{minDate:Pn(t.minDate,t.timezone,t.isSpecific),maxDate:Pn(t.maxDate,t.timezone,t.isSpecific),disabledDates:$n(t.disabledDates)?zn(t.disabledDates,t.timezone,r,t.isSpecific):t.disabledDates,allowedDates:$n(t.allowedDates)?zn(t.allowedDates,t.timezone,r,t.isSpecific):null,highlight:typeof t.highlight=="object"&&$n((n=t.highlight)==null?void 0:n.dates)?zn(t.highlight.dates,t.timezone,r):t.highlight,markers:Xl(t.markers,t.timezone)}},Zl=t=>typeof t=="boolean"?{enabled:t,dragSelect:!0,limit:null}:{enabled:!!t,limit:t.limit?+t.limit:null,dragSelect:t.dragSelect??!0},es=t=>({...Object.fromEntries(Object.keys(t).map(n=>{const a=n,o=t[a],l=typeof t[a]=="string"?{[o]:!0}:Object.fromEntries(o.map(i=>[i,!0]));return[n,l]}))}),Ce=t=>{const r=()=>{const G=t.enableSeconds?":ss":"",Q=t.enableMinutes?":mm":"";return t.is24?`HH${Q}${G}`:`hh${Q}${G} aa`},n=()=>{var G;return t.format?t.format:t.monthPicker?"MM/yyyy":t.timePicker?r():t.weekPicker?`${((G=L.value)==null?void 0:G.type)==="iso"?"II":"ww"}-RR`:t.yearPicker?"yyyy":t.quarterPicker?"QQQ/yyyy":t.enableTimePicker?`MM/dd/yyyy, ${r()}`:"MM/dd/yyyy"},a=G=>ja(G,t.enableSeconds),o=()=>x.value.enabled?t.startTime&&Array.isArray(t.startTime)?[a(t.startTime[0]),a(t.startTime[1])]:null:t.startTime&&!Array.isArray(t.startTime)?a(t.startTime):null,l=e.computed(()=>Il(t.multiCalendars)),i=e.computed(()=>o()),d=e.computed(()=>Vl(t.ariaLabels)),c=e.computed(()=>Hl(t.filters)),v=e.computed(()=>Ll(t.transitions)),f=e.computed(()=>Wl(t.actionRow)),T=e.computed(()=>Fl(t.previewFormat,t.format,n())),m=e.computed(()=>zl(t.textInput)),M=e.computed(()=>ql(t.inline)),_=e.computed(()=>Ul(t.config)),E=e.computed(()=>jl(t.highlight)),L=e.computed(()=>Ql(t.weekNumbers)),h=e.computed(()=>Kl(t.timezone)),A=e.computed(()=>Zl(t.multiDates)),p=e.computed(()=>Jl({minDate:t.minDate,maxDate:t.maxDate,disabledDates:t.disabledDates,allowedDates:t.allowedDates,highlight:E.value,markers:t.markers,timezone:h.value,isSpecific:t.monthPicker||t.yearPicker||t.quarterPicker,isMonthPicker:t.monthPicker,isYearPicker:t.yearPicker})),x=e.computed(()=>Gl(t.range)),U=e.computed(()=>es(t.ui));return{defaultedTransitions:v,defaultedMultiCalendars:l,defaultedStartTime:i,defaultedAriaLabels:d,defaultedFilters:c,defaultedActionRow:f,defaultedPreviewFormat:T,defaultedTextInput:m,defaultedInline:M,defaultedConfig:_,defaultedHighlight:E,defaultedWeekNumbers:L,defaultedRange:x,propDates:p,defaultedTz:h,defaultedMultiDates:A,defaultedUI:U,getDefaultPattern:n,getDefaultStartTime:o}},ts=(t,r,n)=>{const a=e.ref(),{defaultedTextInput:o,defaultedRange:l,defaultedTz:i,defaultedMultiDates:d,getDefaultPattern:c}=Ce(r),v=e.ref(""),f=e.toRef(r,"format"),T=e.toRef(r,"formatLocale");e.watch(a,()=>{typeof r.onInternalModelChange=="function"&&t("internal-model-change",a.value,N(!0))},{deep:!0}),e.watch(l,(s,C)=>{s.enabled!==C.enabled&&(a.value=null)}),e.watch(f,()=>{H()});const m=s=>i.value.timezone&&i.value.convertModel?qe(s,i.value.timezone):s,M=s=>{if(i.value.timezone&&i.value.convertModel){const C=wl(i.value.timezone,s);return yr(s,C)}return s},_=(s,C,ae=!1)=>Ga(s,r.format,r.formatLocale,o.value.rangeSeparator,r.modelAuto,C??c(),ae),E=s=>s?r.modelType?te(s):{hours:lt(s),minutes:dt(s),seconds:r.enableSeconds?St(s):0}:null,L=s=>r.modelType?te(s):{month:ye(s),year:fe(s)},h=s=>Array.isArray(s)?d.value.enabled?s.map(C=>A(C,ot(q(),C))):en(()=>[ot(q(),s[0]),s[1]?ot(q(),s[1]):Yt(l.value.partialRange)],l.value.enabled):ot(q(),+s),A=(s,C)=>(typeof s=="string"||typeof s=="number")&&r.modelType?$(s):C,p=s=>Array.isArray(s)?[A(s[0],mt(null,+s[0].hours,+s[0].minutes,s[0].seconds)),A(s[1],mt(null,+s[1].hours,+s[1].minutes,s[1].seconds))]:A(s,mt(null,s.hours,s.minutes,s.seconds)),x=s=>{const C=ve(q(),{date:1});return Array.isArray(s)?d.value.enabled?s.map(ae=>A(ae,it(C,+ae.month,+ae.year))):en(()=>[A(s[0],it(C,+s[0].month,+s[0].year)),A(s[1],s[1]?it(C,+s[1].month,+s[1].year):Yt(l.value.partialRange))],l.value.enabled):A(s,it(C,+s.month,+s.year))},U=s=>{if(Array.isArray(s))return s.map(C=>$(C));throw new Error(Bn.dateArr("multi-dates"))},G=s=>{if(Array.isArray(s)&&l.value.enabled){const C=s[0],ae=s[1];return[q(Array.isArray(C)?C[0]:null),Array.isArray(ae)&&ae.length?q(ae[0]):null]}return q(s[0])},Q=s=>r.modelAuto?Array.isArray(s)?[$(s[0]),$(s[1])]:r.autoApply?[$(s)]:[$(s),null]:Array.isArray(s)?en(()=>s[1]?[$(s[0]),s[1]?$(s[1]):Yt(l.value.partialRange)]:[$(s[0])],l.value.enabled):$(s),g=()=>{Array.isArray(a.value)&&l.value.enabled&&a.value.length===1&&a.value.push(Yt(l.value.partialRange))},R=()=>{const s=a.value;return[te(s[0]),s[1]?te(s[1]):Yt(l.value.partialRange)]},O=()=>a.value[1]?R():te(Re(a.value[0])),W=()=>(a.value||[]).map(s=>te(s)),le=(s=!1)=>(s||g(),r.modelAuto?O():d.value.enabled?W():Array.isArray(a.value)?en(()=>R(),l.value.enabled):te(Re(a.value))),ue=s=>!s||Array.isArray(s)&&!s.length?null:r.timePicker?p(Re(s)):r.monthPicker?x(Re(s)):r.yearPicker?h(Re(s)):d.value.enabled?U(Re(s)):r.weekPicker?G(Re(s)):Q(Re(s)),y=s=>{const C=ue(s);Nn(Re(C))?(a.value=Re(C),H()):(a.value=null,v.value="")},z=()=>{const s=C=>nt(C,o.value.format);return`${s(a.value[0])} ${o.value.rangeSeparator} ${a.value[1]?s(a.value[1]):""}`},J=()=>n.value&&a.value?Array.isArray(a.value)?z():nt(a.value,o.value.format):_(a.value),w=()=>a.value?d.value.enabled?a.value.map(s=>_(s)).join("; "):o.value.enabled&&typeof o.value.format=="string"?J():_(a.value):"",H=()=>{!r.format||typeof r.format=="string"||o.value.enabled&&typeof o.value.format=="string"?v.value=w():v.value=r.format(a.value)},$=s=>{if(r.utc){const C=new Date(s);return r.utc==="preserve"?new Date(C.getTime()+C.getTimezoneOffset()*6e4):C}return r.modelType?kl.includes(r.modelType)?m(new Date(s)):r.modelType==="format"&&(typeof r.format=="string"||!r.format)?m(Mn(s,c(),new Date,{locale:T.value})):m(Mn(s,r.modelType,new Date,{locale:T.value})):m(new Date(s))},te=s=>s?r.utc?Ol(s,r.utc==="preserve",r.enableSeconds):r.modelType?r.modelType==="timestamp"?+M(s):r.modelType==="iso"?M(s).toISOString():r.modelType==="format"&&(typeof r.format=="string"||!r.format)?_(M(s)):_(M(s),r.modelType,!0):M(s):"",S=(s,C=!1,ae=!1)=>{if(ae)return s;if(t("update:model-value",s),i.value.emitTimezone&&C){const B=Array.isArray(s)?s.map(me=>qe(Re(me),i.value.emitTimezone)):qe(Re(s),i.value.emitTimezone);t("update:model-timezone-value",B)}},V=s=>Array.isArray(a.value)?d.value.enabled?a.value.map(C=>s(C)):[s(a.value[0]),a.value[1]?s(a.value[1]):Yt(l.value.partialRange)]:s(Re(a.value)),k=()=>{if(Array.isArray(a.value)){const s=st(a.value[0],r.weekStart),C=a.value[1]?st(a.value[1],r.weekStart):[];return[s.map(ae=>q(ae)),C.map(ae=>q(ae))]}return st(a.value,r.weekStart).map(s=>q(s))},K=(s,C)=>S(Re(V(s)),!1,C),ne=s=>{const C=k();return s?C:t("update:model-value",k())},N=(s=!1)=>(s||H(),r.monthPicker?K(L,s):r.timePicker?K(E,s):r.yearPicker?K(fe,s):r.weekPicker?ne(s):S(le(s),!0,s));return{inputValue:v,internalModelValue:a,checkBeforeEmit:()=>a.value?l.value.enabled?l.value.partialRange?a.value.length>=1:a.value.length===2:!!a.value:!1,parseExternalModelValue:y,formatInputValue:H,emitModelValue:N}},ns=(t,r)=>{const{defaultedFilters:n,propDates:a}=Ce(t),{validateMonthYearInRange:o}=pt(t),l=(f,T)=>{let m=f;return n.value.months.includes(ye(m))?(m=T?Ge(f,1):Ot(f,1),l(m,T)):m},i=(f,T)=>{let m=f;return n.value.years.includes(fe(m))?(m=T?hn(f,1):Ra(f,1),i(m,T)):m},d=(f,T=!1)=>{const m=ve(q(),{month:t.month,year:t.year});let M=f?Ge(m,1):Ot(m,1);t.disableYearSelect&&(M=ot(M,t.year));let _=ye(M),E=fe(M);n.value.months.includes(_)&&(M=l(M,f),_=ye(M),E=fe(M)),n.value.years.includes(E)&&(M=i(M,f),E=fe(M)),o(_,E,f,t.preventMinMaxNavigation)&&c(_,E,T)},c=(f,T,m)=>{r("update-month-year",{month:f,year:T,fromNav:m})},v=e.computed(()=>f=>Qa(ve(q(),{month:t.month,year:t.year}),a.value.maxDate,a.value.minDate,t.preventMinMaxNavigation,f));return{handleMonthYearChange:d,isDisabled:v,updateMonthYear:c}},nn={multiCalendars:{type:[Boolean,Number,String,Object],default:void 0},modelValue:{type:[String,Date,Array,Object,Number],default:null},modelType:{type:String,default:null},position:{type:String,default:"center"},dark:{type:Boolean,default:!1},format:{type:[String,Function],default:()=>null},autoPosition:{type:[Boolean,String],default:!0},altPosition:{type:Function,default:null},transitions:{type:[Boolean,Object],default:!0},formatLocale:{type:Object,default:null},utc:{type:[Boolean,String],default:!1},ariaLabels:{type:Object,default:()=>({})},offset:{type:[Number,String],default:10},hideNavigation:{type:Array,default:()=>[]},timezone:{type:[String,Object],default:null},vertical:{type:Boolean,default:!1},disableMonthYearSelect:{type:Boolean,default:!1},disableYearSelect:{type:Boolean,default:!1},dayClass:{type:Function,default:null},yearRange:{type:Array,default:()=>[1900,2100]},enableTimePicker:{type:Boolean,default:!0},autoApply:{type:Boolean,default:!1},disabledDates:{type:[Array,Function],default:()=>[]},monthNameFormat:{type:String,default:"short"},startDate:{type:[Date,String],default:null},startTime:{type:[Object,Array],default:null},hideOffsetDates:{type:Boolean,default:!1},noToday:{type:Boolean,default:!1},disabledWeekDays:{type:Array,default:()=>[]},allowedDates:{type:Array,default:null},nowButtonLabel:{type:String,default:"Now"},markers:{type:Array,default:()=>[]},escClose:{type:Boolean,default:!0},spaceConfirm:{type:Boolean,default:!0},monthChangeOnArrows:{type:Boolean,default:!0},presetDates:{type:Array,default:()=>[]},flow:{type:Array,default:()=>[]},partialFlow:{type:Boolean,default:!1},preventMinMaxNavigation:{type:Boolean,default:!1},reverseYears:{type:Boolean,default:!1},weekPicker:{type:Boolean,default:!1},filters:{type:Object,default:()=>({})},arrowNavigation:{type:Boolean,default:!1},highlight:{type:[Function,Object],default:null},teleport:{type:[Boolean,String,Object],default:null},teleportCenter:{type:Boolean,default:!1},locale:{type:String,default:"en-Us"},weekNumName:{type:String,default:"W"},weekStart:{type:[Number,String],default:1},weekNumbers:{type:[String,Function,Object],default:null},monthChangeOnScroll:{type:[Boolean,String],default:!0},dayNames:{type:[Function,Array],default:null},monthPicker:{type:Boolean,default:!1},customProps:{type:Object,default:null},yearPicker:{type:Boolean,default:!1},modelAuto:{type:Boolean,default:!1},selectText:{type:String,default:"Select"},cancelText:{type:String,default:"Cancel"},previewFormat:{type:[String,Function],default:()=>""},multiDates:{type:[Object,Boolean],default:!1},ignoreTimeValidation:{type:Boolean,default:!1},minDate:{type:[Date,String],default:null},maxDate:{type:[Date,String],default:null},minTime:{type:Object,default:null},maxTime:{type:Object,default:null},name:{type:String,default:null},placeholder:{type:String,default:""},hideInputIcon:{type:Boolean,default:!1},clearable:{type:Boolean,default:!0},alwaysClearable:{type:Boolean,default:!1},state:{type:Boolean,default:null},required:{type:Boolean,default:!1},autocomplete:{type:String,default:"off"},timePicker:{type:Boolean,default:!1},enableSeconds:{type:Boolean,default:!1},is24:{type:Boolean,default:!0},noHoursOverlay:{type:Boolean,default:!1},noMinutesOverlay:{type:Boolean,default:!1},noSecondsOverlay:{type:Boolean,default:!1},hoursGridIncrement:{type:[String,Number],default:1},minutesGridIncrement:{type:[String,Number],default:5},secondsGridIncrement:{type:[String,Number],default:5},hoursIncrement:{type:[Number,String],default:1},minutesIncrement:{type:[Number,String],default:1},secondsIncrement:{type:[Number,String],default:1},range:{type:[Boolean,Object],default:!1},uid:{type:String,default:null},disabled:{type:Boolean,default:!1},readonly:{type:Boolean,default:!1},inline:{type:[Boolean,Object],default:!1},textInput:{type:[Boolean,Object],default:!1},sixWeeks:{type:[Boolean,String],default:!1},actionRow:{type:Object,default:()=>({})},focusStartDate:{type:Boolean,default:!1},disabledTimes:{type:[Function,Array],default:void 0},timePickerInline:{type:Boolean,default:!1},calendar:{type:Function,default:null},config:{type:Object,default:void 0},quarterPicker:{type:Boolean,default:!1},yearFirst:{type:Boolean,default:!1},loading:{type:Boolean,default:!1},onInternalModelChange:{type:[Function,Object],default:null},enableMinutes:{type:Boolean,default:!0},ui:{type:Object,default:()=>({})}},et={...nn,shadow:{type:Boolean,default:!1},flowStep:{type:Number,default:0},internalModelValue:{type:[Date,Array],default:null},noOverlayFocus:{type:Boolean,default:!1},collapse:{type:Boolean,default:!1},menuWrapRef:{type:Object,default:null},getInputRect:{type:Function,default:()=>({})},isTextInputDate:{type:Boolean,default:!1},isMobile:{type:Boolean,default:void 0}},as=["title"],rs=["disabled"],os=e.defineComponent({compatConfig:{MODE:3},__name:"ActionRow",props:{menuMount:{type:Boolean,default:!1},calendarWidth:{type:Number,default:0},...et},emits:["close-picker","select-date","select-now","invalid-select"],setup(t,{emit:r}){const n=r,a=t,{defaultedActionRow:o,defaultedPreviewFormat:l,defaultedMultiCalendars:i,defaultedTextInput:d,defaultedInline:c,defaultedRange:v,defaultedMultiDates:f}=Ce(a),{isTimeValid:T,isMonthValid:m}=pt(a),{buildMatrix:M}=gt(),_=e.ref(null),E=e.ref(null),L=e.ref(!1),h=e.ref({}),A=e.ref(null),p=e.ref(null);e.onMounted(()=>{a.arrowNavigation&&M([Ye(_),Ye(E)],"actionRow"),x(),window.addEventListener("resize",x)}),e.onUnmounted(()=>{window.removeEventListener("resize",x)});const x=()=>{L.value=!1,setTimeout(()=>{var J,w;const y=(J=A.value)==null?void 0:J.getBoundingClientRect(),z=(w=p.value)==null?void 0:w.getBoundingClientRect();y&&z&&(h.value.maxWidth=`${z.width-y.width-20}px`),L.value=!0},0)},U=e.computed(()=>v.value.enabled&&!v.value.partialRange&&a.internalModelValue?a.internalModelValue.length===2:!0),G=e.computed(()=>!T.value(a.internalModelValue)||!m.value(a.internalModelValue)||!U.value),Q=()=>{const y=l.value;return a.timePicker||a.monthPicker,y(Re(a.internalModelValue))},g=()=>{const y=a.internalModelValue;return i.value.count>0?`${R(y[0])} - ${R(y[1])}`:[R(y[0]),R(y[1])]},R=y=>Ga(y,l.value,a.formatLocale,d.value.rangeSeparator,a.modelAuto,l.value),O=e.computed(()=>!a.internalModelValue||!a.menuMount?"":typeof l.value=="string"?Array.isArray(a.internalModelValue)?a.internalModelValue.length===2&&a.internalModelValue[1]?g():f.value.enabled?a.internalModelValue.map(y=>`${R(y)}`):a.modelAuto?`${R(a.internalModelValue[0])}`:`${R(a.internalModelValue[0])} -`:R(a.internalModelValue):Q()),W=()=>f.value.enabled?"; ":" - ",le=e.computed(()=>Array.isArray(O.value)?O.value.join(W()):O.value),ue=()=>{T.value(a.internalModelValue)&&m.value(a.internalModelValue)&&U.value?n("select-date"):n("invalid-select")};return(y,z)=>(e.openBlock(),e.createElementBlock("div",{ref_key:"actionRowRef",ref:p,class:"dp__action_row"},[y.$slots["action-row"]?e.renderSlot(y.$slots,"action-row",e.normalizeProps(e.mergeProps({key:0},{internalModelValue:y.internalModelValue,disabled:G.value,selectDate:()=>y.$emit("select-date"),closePicker:()=>y.$emit("close-picker")}))):(e.openBlock(),e.createElementBlock(e.Fragment,{key:1},[e.unref(o).showPreview?(e.openBlock(),e.createElementBlock("div",{key:0,class:"dp__selection_preview",title:le.value,style:e.normalizeStyle(h.value)},[y.$slots["action-preview"]&&L.value?e.renderSlot(y.$slots,"action-preview",{key:0,value:y.internalModelValue}):e.createCommentVNode("",!0),!y.$slots["action-preview"]&&L.value?(e.openBlock(),e.createElementBlock(e.Fragment,{key:1},[e.createTextVNode(e.toDisplayString(le.value),1)],64)):e.createCommentVNode("",!0)],12,as)):e.createCommentVNode("",!0),e.createElementVNode("div",{ref_key:"actionBtnContainer",ref:A,class:"dp__action_buttons","data-dp-element":"action-row"},[y.$slots["action-buttons"]?e.renderSlot(y.$slots,"action-buttons",{key:0,value:y.internalModelValue}):e.createCommentVNode("",!0),y.$slots["action-buttons"]?e.createCommentVNode("",!0):(e.openBlock(),e.createElementBlock(e.Fragment,{key:1},[!e.unref(c).enabled&&e.unref(o).showCancel?(e.openBlock(),e.createElementBlock("button",{key:0,ref_key:"cancelButtonRef",ref:_,type:"button",class:"dp__action_button dp__action_cancel",onClick:z[0]||(z[0]=J=>y.$emit("close-picker")),onKeydown:z[1]||(z[1]=J=>e.unref(ze)(J,()=>y.$emit("close-picker")))},e.toDisplayString(y.cancelText),545)):e.createCommentVNode("",!0),e.unref(o).showNow?(e.openBlock(),e.createElementBlock("button",{key:1,type:"button",class:"dp__action_button dp__action_cancel",onClick:z[2]||(z[2]=J=>y.$emit("select-now")),onKeydown:z[3]||(z[3]=J=>e.unref(ze)(J,()=>y.$emit("select-now")))},e.toDisplayString(y.nowButtonLabel),33)):e.createCommentVNode("",!0),e.unref(o).showSelect?(e.openBlock(),e.createElementBlock("button",{key:2,ref_key:"selectButtonRef",ref:E,type:"button",class:"dp__action_button dp__action_select",disabled:G.value,"data-test-id":"select-button",onKeydown:z[4]||(z[4]=J=>e.unref(ze)(J,()=>ue())),onClick:ue},e.toDisplayString(y.selectText),41,rs)):e.createCommentVNode("",!0)],64))],512)],64))],512))}}),ls=["role","aria-label","tabindex"],ss={class:"dp__selection_grid_header"},is=["aria-selected","aria-disabled","data-test-id","onClick","onKeydown","onMouseover"],us=["aria-label"],Ut=e.defineComponent({__name:"SelectionOverlay",props:{items:{},type:{},isLast:{type:Boolean},arrowNavigation:{type:Boolean},skipButtonRef:{type:Boolean},headerRefs:{},hideNavigation:{},escClose:{type:Boolean},useRelative:{type:Boolean},height:{},textInput:{type:[Boolean,Object]},config:{},noOverlayFocus:{type:Boolean},focusValue:{},menuWrapRef:{},ariaLabels:{},overlayLabel:{}},emits:["selected","toggle","reset-flow","hover-value"],setup(t,{expose:r,emit:n}){const{setSelectionGrid:a,buildMultiLevelMatrix:o,setMonthPicker:l}=gt(),i=n,d=t,{defaultedAriaLabels:c,defaultedTextInput:v,defaultedConfig:f}=Ce(d),{hideNavigationButtons:T}=on(),m=e.ref(!1),M=e.ref(null),_=e.ref(null),E=e.ref([]),L=e.ref(),h=e.ref(null),A=e.ref(0),p=e.ref(null);e.onBeforeUpdate(()=>{M.value=null}),e.onMounted(()=>{e.nextTick().then(()=>W()),d.noOverlayFocus||U(),x(!0)}),e.onUnmounted(()=>x(!1));const x=V=>{var k;d.arrowNavigation&&((k=d.headerRefs)!=null&&k.length?l(V):a(V))},U=()=>{var k;const V=Ye(_);V&&(v.value.enabled||(M.value?(k=M.value)==null||k.focus({preventScroll:!0}):V.focus({preventScroll:!0})),m.value=V.clientHeight<V.scrollHeight)},G=e.computed(()=>({dp__overlay:!0,"dp--overlay-absolute":!d.useRelative,"dp--overlay-relative":d.useRelative})),Q=e.computed(()=>d.useRelative?{height:`${d.height}px`,width:"var(--dp-menu-min-width)"}:void 0),g=e.computed(()=>({dp__overlay_col:!0})),R=e.computed(()=>({dp__btn:!0,dp__button:!0,dp__overlay_action:!0,dp__over_action_scroll:m.value,dp__button_bottom:d.isLast})),O=e.computed(()=>{var V,k;return{dp__overlay_container:!0,dp__container_flex:((V=d.items)==null?void 0:V.length)<=6,dp__container_block:((k=d.items)==null?void 0:k.length)>6}});e.watch(()=>d.items,()=>W(!1),{deep:!0});const W=(V=!0)=>{e.nextTick().then(()=>{const k=Ye(M),K=Ye(_),ne=Ye(h),N=Ye(p),X=ne?ne.getBoundingClientRect().height:0;K&&(K.getBoundingClientRect().height?A.value=K.getBoundingClientRect().height-X:A.value=f.value.modeHeight-X),k&&N&&V&&(N.scrollTop=k.offsetTop-N.offsetTop-(A.value/2-k.getBoundingClientRect().height)-X)})},le=V=>{V.disabled||i("selected",V.value)},ue=()=>{i("toggle"),i("reset-flow")},y=()=>{d.escClose&&ue()},z=(V,k,K,ne)=>{V&&((k.active||k.value===d.focusValue)&&(M.value=V),d.arrowNavigation&&(Array.isArray(E.value[K])?E.value[K][ne]=V:E.value[K]=[V],J()))},J=()=>{var k,K;const V=(k=d.headerRefs)!=null&&k.length?[d.headerRefs].concat(E.value):E.value.concat([d.skipButtonRef?[]:[h.value]]);o(Re(V),(K=d.headerRefs)!=null&&K.length?"monthPicker":"selectionGrid")},w=V=>{d.arrowNavigation||ft(V,f.value,!0)},H=V=>{L.value=V,i("hover-value",V)},$=()=>{if(ue(),!d.isLast){const V=Sn(d.menuWrapRef??null,"action-row");if(V){const k=Fa(V);k==null||k.focus()}}},te=V=>{switch(V.key){case Te.esc:return y();case Te.arrowLeft:return w(V);case Te.arrowRight:return w(V);case Te.arrowUp:return w(V);case Te.arrowDown:return w(V);default:return}},S=V=>{if(V.key===Te.enter)return ue();if(V.key===Te.tab)return $()};return r({focusGrid:U}),(V,k)=>{var K;return e.openBlock(),e.createElementBlock("div",{ref_key:"gridWrapRef",ref:_,class:e.normalizeClass(G.value),style:e.normalizeStyle(Q.value),role:V.useRelative?void 0:"dialog","aria-label":V.overlayLabel,tabindex:V.useRelative?void 0:"0",onKeydown:te,onClick:k[0]||(k[0]=e.withModifiers(()=>{},["prevent"]))},[e.createElementVNode("div",{ref_key:"containerRef",ref:p,class:e.normalizeClass(O.value),style:e.normalizeStyle({"--dp-overlay-height":`${A.value}px`}),role:"grid"},[e.createElementVNode("div",ss,[e.renderSlot(V.$slots,"header")]),V.$slots.overlay?e.renderSlot(V.$slots,"overlay",{key:0}):(e.openBlock(!0),e.createElementBlock(e.Fragment,{key:1},e.renderList(V.items,(ne,N)=>(e.openBlock(),e.createElementBlock("div",{key:N,class:e.normalizeClass(["dp__overlay_row",{dp__flex_row:V.items.length>=3}]),role:"row"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(ne,(X,s)=>(e.openBlock(),e.createElementBlock("div",{key:X.value,ref_for:!0,ref:C=>z(C,X,N,s),role:"gridcell",class:e.normalizeClass(g.value),"aria-selected":X.active||void 0,"aria-disabled":X.disabled||void 0,tabindex:"0","data-test-id":X.text,onClick:e.withModifiers(C=>le(X),["prevent"]),onKeydown:C=>e.unref(ze)(C,()=>le(X),!0),onMouseover:C=>H(X.value)},[e.createElementVNode("div",{class:e.normalizeClass(X.className)},[V.$slots.item?e.renderSlot(V.$slots,"item",{key:0,item:X}):e.createCommentVNode("",!0),V.$slots.item?e.createCommentVNode("",!0):(e.openBlock(),e.createElementBlock(e.Fragment,{key:1},[e.createTextVNode(e.toDisplayString(X.text),1)],64))],2)],42,is))),128))],2))),128))],6),V.$slots["button-icon"]?e.withDirectives((e.openBlock(),e.createElementBlock("button",{key:0,ref_key:"toggleButton",ref:h,type:"button","aria-label":(K=e.unref(c))==null?void 0:K.toggleOverlay,class:e.normalizeClass(R.value),tabindex:"0",onClick:ue,onKeydown:S},[e.renderSlot(V.$slots,"button-icon")],42,us)),[[e.vShow,!e.unref(T)(V.hideNavigation,V.type)]]):e.createCommentVNode("",!0)],46,ls)}}}),cs=["data-dp-mobile"],an=e.defineComponent({__name:"InstanceWrap",props:{multiCalendars:{},stretch:{type:Boolean},collapse:{type:Boolean},isMobile:{type:Boolean}},setup(t){const r=t,n=e.computed(()=>r.multiCalendars>0?[...Array(r.multiCalendars).keys()]:[0]),a=e.computed(()=>({dp__instance_calendar:r.multiCalendars>0}));return(o,l)=>(e.openBlock(),e.createElementBlock("div",{class:e.normalizeClass({dp__menu_inner:!o.stretch,"dp--menu--inner-stretched":o.stretch,dp__flex_display:o.multiCalendars>0,"dp--flex-display-collapsed":o.collapse}),"data-dp-mobile":o.isMobile},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(n.value,(i,d)=>(e.openBlock(),e.createElementBlock("div",{key:i,class:e.normalizeClass(a.value)},[e.renderSlot(o.$slots,"default",{instance:i,index:d})],2))),128))],10,cs))}}),ds=["data-dp-element","aria-label","aria-disabled"],jt=e.defineComponent({compatConfig:{MODE:3},__name:"ArrowBtn",props:{ariaLabel:{},elName:{},disabled:{type:Boolean}},emits:["activate","set-ref"],setup(t,{emit:r}){const n=r,a=e.ref(null);return e.onMounted(()=>n("set-ref",a)),(o,l)=>(e.openBlock(),e.createElementBlock("button",{ref_key:"elRef",ref:a,type:"button","data-dp-element":o.elName,class:"dp__btn dp--arrow-btn-nav",tabindex:"0","aria-label":o.ariaLabel,"aria-disabled":o.disabled||void 0,onClick:l[0]||(l[0]=i=>o.$emit("activate")),onKeydown:l[1]||(l[1]=i=>e.unref(ze)(i,()=>o.$emit("activate"),!0))},[e.createElementVNode("span",{class:e.normalizeClass(["dp__inner_nav",{dp__inner_nav_disabled:o.disabled}])},[e.renderSlot(o.$slots,"default")],2)],40,ds))}}),fs=["aria-label","data-test-id"],nr=e.defineComponent({__name:"YearModePicker",props:{...et,showYearPicker:{type:Boolean,default:!1},items:{type:Array,default:()=>[]},instance:{type:Number,default:0},year:{type:Number,default:0},isDisabled:{type:Function,default:()=>!1}},emits:["toggle-year-picker","year-select","handle-year"],setup(t,{emit:r}){const n=r,a=t,{showRightIcon:o,showLeftIcon:l}=on(),{defaultedConfig:i,defaultedMultiCalendars:d,defaultedAriaLabels:c,defaultedTransitions:v,defaultedUI:f}=Ce(a),{showTransition:T,transitionName:m}=Qt(v),M=e.ref(!1),_=(h=!1,A)=>{M.value=!M.value,n("toggle-year-picker",{flow:h,show:A})},E=h=>{M.value=!1,n("year-select",h)},L=(h=!1)=>{n("handle-year",h)};return(h,A)=>{var p,x,U,G,Q;return e.openBlock(),e.createElementBlock(e.Fragment,null,[e.createElementVNode("div",{class:e.normalizeClass(["dp--year-mode-picker",{"dp--hidden-el":M.value}])},[e.unref(l)(e.unref(d),t.instance)?(e.openBlock(),e.createBlock(jt,{key:0,ref:"mpPrevIconRef","aria-label":(p=e.unref(c))==null?void 0:p.prevYear,disabled:t.isDisabled(!1),class:e.normalizeClass((x=e.unref(f))==null?void 0:x.navBtnPrev),onActivate:A[0]||(A[0]=g=>L(!1))},{default:e.withCtx(()=>[h.$slots["arrow-left"]?e.renderSlot(h.$slots,"arrow-left",{key:0}):e.createCommentVNode("",!0),h.$slots["arrow-left"]?e.createCommentVNode("",!0):(e.openBlock(),e.createBlock(e.unref(un),{key:1}))]),_:3},8,["aria-label","disabled","class"])):e.createCommentVNode("",!0),e.createElementVNode("button",{ref:"mpYearButtonRef",class:"dp__btn dp--year-select",type:"button","aria-label":`${t.year}-${(U=e.unref(c))==null?void 0:U.openYearsOverlay}`,"data-test-id":`year-mode-btn-${t.instance}`,onClick:A[1]||(A[1]=()=>_(!1)),onKeydown:A[2]||(A[2]=e.withKeys(()=>_(!1),["enter"]))},[h.$slots.year?e.renderSlot(h.$slots,"year",{key:0,year:t.year}):e.createCommentVNode("",!0),h.$slots.year?e.createCommentVNode("",!0):(e.openBlock(),e.createElementBlock(e.Fragment,{key:1},[e.createTextVNode(e.toDisplayString(t.year),1)],64))],40,fs),e.unref(o)(e.unref(d),t.instance)?(e.openBlock(),e.createBlock(jt,{key:1,ref:"mpNextIconRef","aria-label":(G=e.unref(c))==null?void 0:G.nextYear,disabled:t.isDisabled(!0),class:e.normalizeClass((Q=e.unref(f))==null?void 0:Q.navBtnNext),onActivate:A[3]||(A[3]=g=>L(!0))},{default:e.withCtx(()=>[h.$slots["arrow-right"]?e.renderSlot(h.$slots,"arrow-right",{key:0}):e.createCommentVNode("",!0),h.$slots["arrow-right"]?e.createCommentVNode("",!0):(e.openBlock(),e.createBlock(e.unref(cn),{key:1}))]),_:3},8,["aria-label","disabled","class"])):e.createCommentVNode("",!0)],2),e.createVNode(e.Transition,{name:e.unref(m)(t.showYearPicker),css:e.unref(T)},{default:e.withCtx(()=>{var g,R;return[t.showYearPicker?(e.openBlock(),e.createBlock(Ut,{key:0,items:t.items,"text-input":h.textInput,"esc-close":h.escClose,config:h.config,"is-last":h.autoApply&&!e.unref(i).keepActionRow,"hide-navigation":h.hideNavigation,"aria-labels":h.ariaLabels,"overlay-label":(R=(g=e.unref(c))==null?void 0:g.yearPicker)==null?void 0:R.call(g,!0),type:"year",onToggle:_,onSelected:A[4]||(A[4]=O=>E(O))},e.createSlots({"button-icon":e.withCtx(()=>[h.$slots["calendar-icon"]?e.renderSlot(h.$slots,"calendar-icon",{key:0}):e.createCommentVNode("",!0),h.$slots["calendar-icon"]?e.createCommentVNode("",!0):(e.openBlock(),e.createBlock(e.unref(We),{key:1}))]),_:2},[h.$slots["year-overlay-value"]?{name:"item",fn:e.withCtx(({item:O})=>[e.renderSlot(h.$slots,"year-overlay-value",{text:O.text,value:O.value})]),key:"0"}:void 0]),1032,["items","text-input","esc-close","config","is-last","hide-navigation","aria-labels","overlay-label"])):e.createCommentVNode("",!0)]}),_:3},8,["name","css"])],64)}}}),Hn=(t,r,n)=>{if(r.value&&Array.isArray(r.value))if(r.value.some(a=>pe(t,a))){const a=r.value.filter(o=>!pe(o,t));r.value=a.length?a:null}else(n&&+n>r.value.length||!n)&&r.value.push(t);else r.value=[t]},Wn=(t,r,n)=>{let a=t.value?t.value.slice():[];return a.length===2&&a[1]!==null&&(a=[]),a.length?(Ae(r,a[0])?a.unshift(r):a[1]=r,n("range-end",r)):(a=[r],n("range-start",r)),a},rn=(t,r,n,a)=>{t&&(t[0]&&t[1]&&n&&r("auto-apply"),t[0]&&!t[1]&&a&&n&&r("auto-apply"))},ar=t=>{Array.isArray(t.value)&&t.value.length<=2&&t.range?t.modelValue.value=t.value.map(r=>qe(q(r),t.timezone)):Array.isArray(t.value)||(t.modelValue.value=qe(q(t.value),t.timezone))},rr=(t,r,n,a)=>Array.isArray(r.value)&&(r.value.length===2||r.value.length===1&&a.value.partialRange)?a.value.fixedStart&&(Ne(t,r.value[0])||pe(t,r.value[0]))?[r.value[0],t]:a.value.fixedEnd&&(Ae(t,r.value[1])||pe(t,r.value[1]))?[t,r.value[1]]:(n("invalid-fixed-range",t),r.value):[],or=({multiCalendars:t,range:r,highlight:n,propDates:a,calendars:o,modelValue:l,props:i,filters:d,year:c,month:v,emit:f})=>{const T=e.computed(()=>Cn(i.yearRange,i.locale,i.reverseYears)),m=e.ref([!1]),M=e.computed(()=>(O,W)=>{const le=ve(Ze(new Date),{month:v.value(O),year:c.value(O)}),ue=W?ha(le):Ft(le);return Qa(ue,a.value.maxDate,a.value.minDate,i.preventMinMaxNavigation,W)}),_=()=>Array.isArray(l.value)&&t.value.solo&&l.value[1],E=()=>{for(let O=0;O<t.value.count;O++)if(O===0)o.value[O]=o.value[0];else if(O===t.value.count-1&&_())o.value[O]={month:ye(l.value[1]),year:fe(l.value[1])};else{const W=ve(q(),o.value[O-1]);o.value[O]={month:ye(W),year:fe(hn(W,1))}}},L=O=>{if(!O)return E();const W=ve(q(),o.value[O]);return o.value[0].year=fe(Ra(W,t.value.count-1)),E()},h=(O,W)=>{const le=vr(W,O);return r.value.showLastInRange&&le>1?W:O},A=O=>i.focusStartDate||t.value.solo?O[0]:O[1]?h(O[0],O[1]):O[0],p=()=>{if(l.value){const O=Array.isArray(l.value)?A(l.value):l.value;o.value[0]={month:ye(O),year:fe(O)}}},x=()=>{p(),t.value.count&&E()};e.watch(l,(O,W)=>{i.isTextInputDate&&JSON.stringify(O??{})!==JSON.stringify(W??{})&&x()}),e.onMounted(()=>{x()});const U=(O,W)=>{o.value[W].year=O,f("update-month-year",{instance:W,year:O,month:o.value[W].month}),t.value.count&&!t.value.solo&&L(W)},G=e.computed(()=>O=>Et(T.value,W=>{var z;const le=c.value(O)===W.value,ue=Ht(W.value,Rt(a.value.minDate),Rt(a.value.maxDate))||((z=d.value.years)==null?void 0:z.includes(c.value(O))),y=_n(n.value,W.value);return{active:le,disabled:ue,highlighted:y}})),Q=(O,W)=>{U(O,W),R(W)},g=(O,W=!1)=>{if(!M.value(O,W)){const le=W?c.value(O)+1:c.value(O)-1;U(le,O)}},R=(O,W=!1,le)=>{W||f("reset-flow"),le!==void 0?m.value[O]=le:m.value[O]=!m.value[O],m.value[O]?f("overlay-toggle",{open:!0,overlay:Ie.year}):(f("overlay-closed"),f("overlay-toggle",{open:!1,overlay:Ie.year}))};return{isDisabled:M,groupedYears:G,showYearPicker:m,selectYear:U,toggleYearPicker:R,handleYearSelect:Q,handleYear:g}},ms=(t,r)=>{const{defaultedMultiCalendars:n,defaultedAriaLabels:a,defaultedTransitions:o,defaultedConfig:l,defaultedRange:i,defaultedHighlight:d,propDates:c,defaultedTz:v,defaultedFilters:f,defaultedMultiDates:T}=Ce(t),m=()=>{t.isTextInputDate&&x(fe(q(t.startDate)),0)},{modelValue:M,year:_,month:E,calendars:L}=Gt(t,r,m),h=e.computed(()=>_a(t.formatLocale,t.locale,t.monthNameFormat)),A=e.ref(null),{checkMinMaxRange:p}=pt(t),{selectYear:x,groupedYears:U,showYearPicker:G,toggleYearPicker:Q,handleYearSelect:g,handleYear:R,isDisabled:O}=or({modelValue:M,multiCalendars:n,range:i,highlight:d,calendars:L,year:_,propDates:c,month:E,filters:f,props:t,emit:r});e.onMounted(()=>{t.startDate&&(M.value&&t.focusStartDate||!M.value)&&x(fe(q(t.startDate)),0)});const W=N=>N?{month:ye(N),year:fe(N)}:{month:null,year:null},le=()=>M.value?Array.isArray(M.value)?M.value.map(N=>W(N)):W(M.value):W(),ue=(N,X)=>{const s=L.value[N],C=le();return Array.isArray(C)?C.some(ae=>ae.year===(s==null?void 0:s.year)&&ae.month===X):(s==null?void 0:s.year)===C.year&&X===C.month},y=(N,X,s)=>{var ae,B;const C=le();return Array.isArray(C)?_.value(X)===((ae=C[s])==null?void 0:ae.year)&&N===((B=C[s])==null?void 0:B.month):!1},z=(N,X)=>{if(i.value.enabled){const s=le();if(Array.isArray(M.value)&&Array.isArray(s)){const C=y(N,X,0)||y(N,X,1),ae=it(Ze(q()),N,_.value(X));return Wt(M.value,A.value,ae)&&!C}return!1}return!1},J=e.computed(()=>N=>Et(h.value,X=>{var me;const s=ue(N,X.value),C=Ht(X.value,Wa(_.value(N),c.value.minDate),qa(_.value(N),c.value.maxDate))||_l(c.value.disabledDates,_.value(N),X.value)||((me=f.value.months)==null?void 0:me.includes(X.value))||!xl(c.value.allowedDates,_.value(N),X.value),ae=z(X.value,N),B=Xa(d.value,X.value,_.value(N));return{active:s,disabled:C,isBetween:ae,highlighted:B}})),w=(N,X)=>it(Ze(q()),N,_.value(X)),H=(N,X)=>{const s=M.value?M.value:Ze(new Date);M.value=it(s,N,_.value(X)),r("auto-apply"),r("update-flow-step")},$=(N,X)=>{const s=w(N,X);i.value.fixedEnd||i.value.fixedStart?M.value=rr(s,M,r,i):M.value?p(s,M.value)&&(M.value=Wn(M,w(N,X),r)):M.value=[w(N,X)],e.nextTick().then(()=>{rn(M.value,r,t.autoApply,t.modelAuto)})},te=(N,X)=>{Hn(w(N,X),M,T.value.limit),r("auto-apply",!0)},S=(N,X)=>(L.value[X].month=N,k(X,L.value[X].year,N),T.value.enabled?te(N,X):i.value.enabled?$(N,X):H(N,X)),V=(N,X)=>{x(N,X),k(X,N,null)},k=(N,X,s)=>{let C=s;if(!C&&C!==0){const ae=le();C=Array.isArray(ae)?ae[N].month:ae.month}r("update-month-year",{instance:N,year:X,month:C})};return{groupedMonths:J,groupedYears:U,year:_,isDisabled:O,defaultedMultiCalendars:n,defaultedAriaLabels:a,defaultedTransitions:o,defaultedConfig:l,showYearPicker:G,modelValue:M,presetDate:(N,X)=>{ar({value:N,modelValue:M,range:i.value.enabled,timezone:X?void 0:v.value.timezone}),r("auto-apply")},setHoverDate:(N,X)=>{A.value=w(N,X)},selectMonth:S,selectYear:V,toggleYearPicker:Q,handleYearSelect:g,handleYear:R,getModelMonthYear:le}},hs=e.defineComponent({compatConfig:{MODE:3},__name:"MonthPicker",props:{...et},emits:["update:internal-model-value","overlay-closed","reset-flow","range-start","range-end","auto-apply","update-month-year","update-flow-step","mount","invalid-fixed-range","overlay-toggle"],setup(t,{expose:r,emit:n}){const a=n,o=e.useSlots(),l=Ue(o,"yearMode"),i=t;e.onMounted(()=>{i.shadow||a("mount",null)});const{groupedMonths:d,groupedYears:c,year:v,isDisabled:f,defaultedMultiCalendars:T,defaultedConfig:m,showYearPicker:M,modelValue:_,presetDate:E,setHoverDate:L,selectMonth:h,selectYear:A,toggleYearPicker:p,handleYearSelect:x,handleYear:U,getModelMonthYear:G}=ms(i,a);return r({getSidebarProps:()=>({modelValue:_,year:v,getModelMonthYear:G,selectMonth:h,selectYear:A,handleYear:U}),presetDate:E,toggleYearPicker:g=>p(0,g)}),(g,R)=>(e.openBlock(),e.createBlock(an,{"multi-calendars":e.unref(T).count,collapse:g.collapse,stretch:"","is-mobile":g.isMobile},{default:e.withCtx(({instance:O})=>[g.$slots["top-extra"]?e.renderSlot(g.$slots,"top-extra",{key:0,value:g.internalModelValue}):e.createCommentVNode("",!0),g.$slots["month-year"]?e.renderSlot(g.$slots,"month-year",e.normalizeProps(e.mergeProps({key:1},{year:e.unref(v),months:e.unref(d)(O),years:e.unref(c)(O),selectMonth:e.unref(h),selectYear:e.unref(A),instance:O}))):(e.openBlock(),e.createBlock(Ut,{key:2,items:e.unref(d)(O),"arrow-navigation":g.arrowNavigation,"is-last":g.autoApply&&!e.unref(m).keepActionRow,"esc-close":g.escClose,height:e.unref(m).modeHeight,config:g.config,"no-overlay-focus":!!(g.noOverlayFocus||g.textInput),"use-relative":"",type:"month",onSelected:W=>e.unref(h)(W,O),onHoverValue:W=>e.unref(L)(W,O)},e.createSlots({header:e.withCtx(()=>[e.createVNode(nr,e.mergeProps(g.$props,{items:e.unref(c)(O),instance:O,"show-year-picker":e.unref(M)[O],year:e.unref(v)(O),"is-disabled":W=>e.unref(f)(O,W),onHandleYear:W=>e.unref(U)(O,W),onYearSelect:W=>e.unref(x)(W,O),onToggleYearPicker:W=>e.unref(p)(O,W==null?void 0:W.flow,W==null?void 0:W.show)}),e.createSlots({_:2},[e.renderList(e.unref(l),(W,le)=>({name:W,fn:e.withCtx(ue=>[e.renderSlot(g.$slots,W,e.normalizeProps(e.guardReactiveProps(ue)))])}))]),1040,["items","instance","show-year-picker","year","is-disabled","onHandleYear","onYearSelect","onToggleYearPicker"])]),_:2},[g.$slots["month-overlay-value"]?{name:"item",fn:e.withCtx(({item:W})=>[e.renderSlot(g.$slots,"month-overlay-value",{text:W.text,value:W.value})]),key:"0"}:void 0]),1032,["items","arrow-navigation","is-last","esc-close","height","config","no-overlay-focus","onSelected","onHoverValue"]))]),_:3},8,["multi-calendars","collapse","is-mobile"]))}}),gs=(t,r)=>{const n=()=>{t.isTextInputDate&&(f.value=fe(q(t.startDate)))},{modelValue:a}=Gt(t,r,n),o=e.ref(null),{defaultedHighlight:l,defaultedMultiDates:i,defaultedFilters:d,defaultedRange:c,propDates:v}=Ce(t),f=e.ref();e.onMounted(()=>{t.startDate&&(a.value&&t.focusStartDate||!a.value)&&(f.value=fe(q(t.startDate)))});const T=p=>Array.isArray(a.value)?a.value.some(x=>fe(x)===p):a.value?fe(a.value)===p:!1,m=p=>c.value.enabled&&Array.isArray(a.value)?Wt(a.value,o.value,L(p)):!1,M=p=>v.value.allowedDates instanceof Map?v.value.allowedDates.size?v.value.allowedDates.has(`${p}`):!1:!0,_=p=>v.value.disabledDates instanceof Map?v.value.disabledDates.size?v.value.disabledDates.has(`${p}`):!1:!0,E=e.computed(()=>Et(Cn(t.yearRange,t.locale,t.reverseYears),p=>{const x=T(p.value),U=Ht(p.value,Rt(v.value.minDate),Rt(v.value.maxDate))||d.value.years.includes(p.value)||!M(p.value)||_(p.value),G=m(p.value)&&!x,Q=_n(l.value,p.value);return{active:x,disabled:U,isBetween:G,highlighted:Q}})),L=p=>ot(Ze(Ft(new Date)),p);return{groupedYears:E,modelValue:a,focusYear:f,setHoverValue:p=>{o.value=ot(Ze(new Date),p)},selectYear:p=>{var x;if(r("update-month-year",{instance:0,year:p}),i.value.enabled)return a.value?Array.isArray(a.value)&&(((x=a.value)==null?void 0:x.map(G=>fe(G))).includes(p)?a.value=a.value.filter(G=>fe(G)!==p):a.value.push(ot(xe(q()),p))):a.value=[ot(xe(Ft(q())),p)],r("auto-apply",!0);c.value.enabled?(a.value=Wn(a,L(p),r),e.nextTick().then(()=>{rn(a.value,r,t.autoApply,t.modelAuto)})):(a.value=L(p),r("auto-apply"))}}},ys=e.defineComponent({compatConfig:{MODE:3},__name:"YearPicker",props:{...et},emits:["update:internal-model-value","reset-flow","range-start","range-end","auto-apply","update-month-year"],setup(t,{expose:r,emit:n}){const a=n,o=t,{groupedYears:l,modelValue:i,focusYear:d,selectYear:c,setHoverValue:v}=gs(o,a),{defaultedConfig:f}=Ce(o);return r({getSidebarProps:()=>({modelValue:i,selectYear:c})}),(m,M)=>(e.openBlock(),e.createElementBlock("div",null,[m.$slots["top-extra"]?e.renderSlot(m.$slots,"top-extra",{key:0,value:m.internalModelValue}):e.createCommentVNode("",!0),m.$slots["month-year"]?e.renderSlot(m.$slots,"month-year",e.normalizeProps(e.mergeProps({key:1},{years:e.unref(l),selectYear:e.unref(c)}))):(e.openBlock(),e.createBlock(Ut,{key:2,items:e.unref(l),"is-last":m.autoApply&&!e.unref(f).keepActionRow,height:e.unref(f).modeHeight,config:m.config,"no-overlay-focus":!!(m.noOverlayFocus||m.textInput),"focus-value":e.unref(d),type:"year","use-relative":"",onSelected:e.unref(c),onHoverValue:e.unref(v)},e.createSlots({_:2},[m.$slots["year-overlay-value"]?{name:"item",fn:e.withCtx(({item:_})=>[e.renderSlot(m.$slots,"year-overlay-value",{text:_.text,value:_.value})]),key:"0"}:void 0]),1032,["items","is-last","height","config","no-overlay-focus","focus-value","onSelected","onHoverValue"]))]))}}),ps={key:0,class:"dp__time_input"},ws=["data-compact","data-collapsed"],ks=["data-test-id","aria-label","onKeydown","onClick","onMousedown"],bs=["aria-label","disabled","data-test-id","onKeydown","onClick"],vs=["data-test-id","aria-label","onKeydown","onClick","onMousedown"],Ds={key:0},Ms=["aria-label","data-compact"],Ts=e.defineComponent({compatConfig:{MODE:3},__name:"TimeInput",props:{hours:{type:Number,default:0},minutes:{type:Number,default:0},seconds:{type:Number,default:0},closeTimePickerBtn:{type:Object,default:null},order:{type:Number,default:0},disabledTimesConfig:{type:Function,default:null},validateTime:{type:Function,default:()=>!1},...et},emits:["set-hours","set-minutes","update:hours","update:minutes","update:seconds","reset-flow","mounted","overlay-closed","overlay-opened","am-pm-change"],setup(t,{expose:r,emit:n}){const a=n,o=t,{setTimePickerElements:l,setTimePickerBackRef:i}=gt(),{defaultedAriaLabels:d,defaultedTransitions:c,defaultedFilters:v,defaultedConfig:f,defaultedRange:T,defaultedMultiCalendars:m}=Ce(o),{transitionName:M,showTransition:_}=Qt(c),E=e.reactive({hours:!1,minutes:!1,seconds:!1}),L=e.ref("AM"),h=e.ref(null),A=e.ref([]),p=e.ref(),x=e.ref(!1);e.onMounted(()=>{a("mounted")});const U=u=>ve(new Date,{hours:u.hours,minutes:u.minutes,seconds:o.enableSeconds?u.seconds:0,milliseconds:0}),G=e.computed(()=>u=>$(u,o[u])||g(u,o[u])),Q=e.computed(()=>({hours:o.hours,minutes:o.minutes,seconds:o.seconds})),g=(u,I)=>T.value.enabled&&!T.value.disableTimeRangeValidation?!o.validateTime(u,I):!1,R=(u,I)=>{if(T.value.enabled&&!T.value.disableTimeRangeValidation){const se=I?+o[`${u}Increment`]:-+o[`${u}Increment`],Y=o[u]+se;return!o.validateTime(u,Y)}return!1},O=e.computed(()=>u=>!K(+o[u]+ +o[`${u}Increment`],u)||R(u,!0)),W=e.computed(()=>u=>!K(+o[u]-+o[`${u}Increment`],u)||R(u,!1)),le=(u,I)=>oa(ve(q(),u),I),ue=(u,I)=>gl(ve(q(),u),I),y=e.computed(()=>({dp__time_col:!0,dp__time_col_block:!o.timePickerInline,dp__time_col_reg_block:!o.enableSeconds&&o.is24&&!o.timePickerInline,dp__time_col_reg_inline:!o.enableSeconds&&o.is24&&o.timePickerInline,dp__time_col_reg_with_button:!o.enableSeconds&&!o.is24,dp__time_col_sec:o.enableSeconds&&o.is24,dp__time_col_sec_with_button:o.enableSeconds&&!o.is24})),z=e.computed(()=>o.timePickerInline&&T.value.enabled&&!m.value.count),J=e.computed(()=>{const u=[{type:"hours"}];return o.enableMinutes&&u.push({type:"",separator:!0},{type:"minutes"}),o.enableSeconds&&u.push({type:"",separator:!0},{type:"seconds"}),u}),w=e.computed(()=>J.value.filter(u=>!u.separator)),H=e.computed(()=>u=>{if(u==="hours"){const I=ae(+o.hours);return{text:I<10?`0${I}`:`${I}`,value:I}}return{text:o[u]<10?`0${o[u]}`:`${o[u]}`,value:o[u]}}),$=(u,I)=>{var Y;if(!o.disabledTimesConfig)return!1;const se=o.disabledTimesConfig(o.order,u==="hours"?I:void 0);return se[u]?!!((Y=se[u])!=null&&Y.includes(I)):!0},te=(u,I)=>I!=="hours"||L.value==="AM"?u:u+12,S=u=>{const I=o.is24?24:12,se=u==="hours"?I:60,Y=+o[`${u}GridIncrement`],ge=u==="hours"&&!o.is24?Y:0,he=[];for(let De=ge;De<se;De+=Y)he.push({value:o.is24?De:te(De,u),text:De<10?`0${De}`:`${De}`});return u==="hours"&&!o.is24&&he.unshift({value:L.value==="PM"?12:0,text:"12"}),Et(he,De=>({active:!1,disabled:v.value.times[u].includes(De.value)||!K(De.value,u)||$(u,De.value)||g(u,De.value)}))},V=u=>u>=0?u:59,k=u=>u>=0?u:23,K=(u,I)=>{const se=o.minTime?U(On(o.minTime)):null,Y=o.maxTime?U(On(o.maxTime)):null,ge=U(On(Q.value,I,I==="minutes"||I==="seconds"?V(u):k(u)));return se&&Y?(At(ge,Y)||$t(ge,Y))&&(Dt(ge,se)||$t(ge,se)):se?Dt(ge,se)||$t(ge,se):Y?At(ge,Y)||$t(ge,Y):!0},ne=u=>o[`no${u[0].toUpperCase()+u.slice(1)}Overlay`],N=u=>{ne(u)||(E[u]=!E[u],E[u]?(x.value=!0,a("overlay-opened",u)):(x.value=!1,a("overlay-closed",u)))},X=u=>u==="hours"?lt:u==="minutes"?dt:St,s=()=>{p.value&&clearTimeout(p.value)},C=(u,I=!0,se)=>{const Y=I?le:ue,ge=I?+o[`${u}Increment`]:-+o[`${u}Increment`];K(+o[u]+ge,u)&&a(`update:${u}`,X(u)(Y({[u]:+o[u]},{[u]:+o[`${u}Increment`]}))),!(se!=null&&se.keyboard)&&f.value.timeArrowHoldThreshold&&(p.value=setTimeout(()=>{C(u,I)},f.value.timeArrowHoldThreshold))},ae=u=>o.is24?u:(u>=12?L.value="PM":L.value="AM",Dl(u)),B=()=>{L.value==="PM"?(L.value="AM",a("update:hours",o.hours-12)):(L.value="PM",a("update:hours",o.hours+12)),a("am-pm-change",L.value)},me=u=>{E[u]=!0},de=(u,I,se)=>{if(u&&o.arrowNavigation){Array.isArray(A.value[I])?A.value[I][se]=u:A.value[I]=[u];const Y=A.value.reduce((ge,he)=>he.map((De,D)=>[...ge[D]||[],he[D]]),[]);i(o.closeTimePickerBtn),h.value&&(Y[1]=Y[1].concat(h.value)),l(Y,o.order)}},ee=(u,I)=>(N(u),a(`update:${u}`,I));return r({openChildCmp:me}),(u,I)=>{var se;return u.disabled?e.createCommentVNode("",!0):(e.openBlock(),e.createElementBlock("div",ps,[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(J.value,(Y,ge)=>{var he,De,D;return e.openBlock(),e.createElementBlock("div",{key:ge,class:e.normalizeClass(y.value),"data-compact":z.value&&!u.enableSeconds,"data-collapsed":z.value&&u.enableSeconds},[Y.separator?(e.openBlock(),e.createElementBlock(e.Fragment,{key:0},[x.value?e.createCommentVNode("",!0):(e.openBlock(),e.createElementBlock(e.Fragment,{key:0},[e.createTextVNode(":")],64))],64)):(e.openBlock(),e.createElementBlock(e.Fragment,{key:1},[e.createElementVNode("button",{ref_for:!0,ref:F=>de(F,ge,0),type:"button",class:e.normalizeClass({dp__btn:!0,dp__inc_dec_button:!u.timePickerInline,dp__inc_dec_button_inline:u.timePickerInline,dp__tp_inline_btn_top:u.timePickerInline,dp__inc_dec_button_disabled:O.value(Y.type),"dp--hidden-el":x.value}),"data-test-id":`${Y.type}-time-inc-btn-${o.order}`,"aria-label":(he=e.unref(d))==null?void 0:he.incrementValue(Y.type),tabindex:"0",onKeydown:F=>e.unref(ze)(F,()=>C(Y.type,!0,{keyboard:!0}),!0),onClick:F=>e.unref(f).timeArrowHoldThreshold?void 0:C(Y.type,!0),onMousedown:F=>e.unref(f).timeArrowHoldThreshold?C(Y.type,!0):void 0,onMouseup:s},[o.timePickerInline?(e.openBlock(),e.createElementBlock(e.Fragment,{key:1},[u.$slots["tp-inline-arrow-up"]?e.renderSlot(u.$slots,"tp-inline-arrow-up",{key:0}):(e.openBlock(),e.createElementBlock(e.Fragment,{key:1},[I[2]||(I[2]=e.createElementVNode("span",{class:"dp__tp_inline_btn_bar dp__tp_btn_in_l"},null,-1)),I[3]||(I[3]=e.createElementVNode("span",{class:"dp__tp_inline_btn_bar dp__tp_btn_in_r"},null,-1))],64))],64)):(e.openBlock(),e.createElementBlock(e.Fragment,{key:0},[u.$slots["arrow-up"]?e.renderSlot(u.$slots,"arrow-up",{key:0}):e.createCommentVNode("",!0),u.$slots["arrow-up"]?e.createCommentVNode("",!0):(e.openBlock(),e.createBlock(e.unref(fn),{key:1}))],64))],42,ks),e.createElementVNode("button",{ref_for:!0,ref:F=>de(F,ge,1),type:"button","aria-label":`${H.value(Y.type).text}-${(De=e.unref(d))==null?void 0:De.openTpOverlay(Y.type)}`,class:e.normalizeClass({dp__time_display:!0,dp__time_display_block:!u.timePickerInline,dp__time_display_inline:u.timePickerInline,"dp--time-invalid":G.value(Y.type),"dp--time-overlay-btn":!G.value(Y.type),"dp--hidden-el":x.value}),disabled:ne(Y.type),tabindex:"0","data-test-id":`${Y.type}-toggle-overlay-btn-${o.order}`,onKeydown:F=>e.unref(ze)(F,()=>N(Y.type),!0),onClick:F=>N(Y.type)},[u.$slots[Y.type]?e.renderSlot(u.$slots,Y.type,{key:0,text:H.value(Y.type).text,value:H.value(Y.type).value}):e.createCommentVNode("",!0),u.$slots[Y.type]?e.createCommentVNode("",!0):(e.openBlock(),e.createElementBlock(e.Fragment,{key:1},[e.createTextVNode(e.toDisplayString(H.value(Y.type).text),1)],64))],42,bs),e.createElementVNode("button",{ref_for:!0,ref:F=>de(F,ge,2),type:"button",class:e.normalizeClass({dp__btn:!0,dp__inc_dec_button:!u.timePickerInline,dp__inc_dec_button_inline:u.timePickerInline,dp__tp_inline_btn_bottom:u.timePickerInline,dp__inc_dec_button_disabled:W.value(Y.type),"dp--hidden-el":x.value}),"data-test-id":`${Y.type}-time-dec-btn-${o.order}`,"aria-label":(D=e.unref(d))==null?void 0:D.decrementValue(Y.type),tabindex:"0",onKeydown:F=>e.unref(ze)(F,()=>C(Y.type,!1,{keyboard:!0}),!0),onClick:F=>e.unref(f).timeArrowHoldThreshold?void 0:C(Y.type,!1),onMousedown:F=>e.unref(f).timeArrowHoldThreshold?C(Y.type,!1):void 0,onMouseup:s},[o.timePickerInline?(e.openBlock(),e.createElementBlock(e.Fragment,{key:1},[u.$slots["tp-inline-arrow-down"]?e.renderSlot(u.$slots,"tp-inline-arrow-down",{key:0}):(e.openBlock(),e.createElementBlock(e.Fragment,{key:1},[I[4]||(I[4]=e.createElementVNode("span",{class:"dp__tp_inline_btn_bar dp__tp_btn_in_l"},null,-1)),I[5]||(I[5]=e.createElementVNode("span",{class:"dp__tp_inline_btn_bar dp__tp_btn_in_r"},null,-1))],64))],64)):(e.openBlock(),e.createElementBlock(e.Fragment,{key:0},[u.$slots["arrow-down"]?e.renderSlot(u.$slots,"arrow-down",{key:0}):e.createCommentVNode("",!0),u.$slots["arrow-down"]?e.createCommentVNode("",!0):(e.openBlock(),e.createBlock(e.unref(mn),{key:1}))],64))],42,vs)],64))],10,ws)}),128)),u.is24?e.createCommentVNode("",!0):(e.openBlock(),e.createElementBlock("div",Ds,[u.$slots["am-pm-button"]?e.renderSlot(u.$slots,"am-pm-button",{key:0,toggle:B,value:L.value}):e.createCommentVNode("",!0),u.$slots["am-pm-button"]?e.createCommentVNode("",!0):(e.openBlock(),e.createElementBlock("button",{key:1,ref_key:"amPmButton",ref:h,type:"button",class:"dp__pm_am_button",role:"button","aria-label":(se=e.unref(d))==null?void 0:se.amPmButton,tabindex:"0","data-compact":z.value,onClick:B,onKeydown:I[0]||(I[0]=Y=>e.unref(ze)(Y,()=>B(),!0))},e.toDisplayString(L.value),41,Ms))])),(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(w.value,(Y,ge)=>(e.openBlock(),e.createBlock(e.Transition,{key:ge,name:e.unref(M)(E[Y.type]),css:e.unref(_)},{default:e.withCtx(()=>{var he,De;return[E[Y.type]?(e.openBlock(),e.createBlock(Ut,{key:0,items:S(Y.type),"is-last":u.autoApply&&!e.unref(f).keepActionRow,"esc-close":u.escClose,type:Y.type,"text-input":u.textInput,config:u.config,"arrow-navigation":u.arrowNavigation,"aria-labels":u.ariaLabels,"overlay-label":(De=(he=e.unref(d)).timeOverlay)==null?void 0:De.call(he,Y.type),onSelected:D=>ee(Y.type,D),onToggle:D=>N(Y.type),onResetFlow:I[1]||(I[1]=D=>u.$emit("reset-flow"))},e.createSlots({"button-icon":e.withCtx(()=>[u.$slots["clock-icon"]?e.renderSlot(u.$slots,"clock-icon",{key:0}):e.createCommentVNode("",!0),u.$slots["clock-icon"]?e.createCommentVNode("",!0):(e.openBlock(),e.createBlock(e.resolveDynamicComponent(u.timePickerInline?e.unref(We):e.unref(dn)),{key:1}))]),_:2},[u.$slots[`${Y.type}-overlay-value`]?{name:"item",fn:e.withCtx(({item:D})=>[e.renderSlot(u.$slots,`${Y.type}-overlay-value`,{text:D.text,value:D.value})]),key:"0"}:void 0,u.$slots[`${Y.type}-overlay-header`]?{name:"header",fn:e.withCtx(()=>[e.renderSlot(u.$slots,`${Y.type}-overlay-header`,{toggle:()=>N(Y.type)})]),key:"1"}:void 0]),1032,["items","is-last","esc-close","type","text-input","config","arrow-navigation","aria-labels","overlay-label","onSelected","onToggle"])):e.createCommentVNode("",!0)]}),_:2},1032,["name","css"]))),128))]))}}}),Ps=["data-dp-mobile"],Cs=["aria-label","tabindex"],Bs=["role","aria-label","tabindex"],Ss=["aria-label"],lr=e.defineComponent({compatConfig:{MODE:3},__name:"TimePicker",props:{hours:{type:[Number,Array],default:0},minutes:{type:[Number,Array],default:0},seconds:{type:[Number,Array],default:0},disabledTimesConfig:{type:Function,default:null},validateTime:{type:Function,default:()=>!1},...et},emits:["update:hours","update:minutes","update:seconds","mount","reset-flow","overlay-opened","overlay-closed","am-pm-change"],setup(t,{expose:r,emit:n}){const a=n,o=t,{buildMatrix:l,setTimePicker:i}=gt(),d=e.useSlots(),{defaultedTransitions:c,defaultedAriaLabels:v,defaultedTextInput:f,defaultedConfig:T,defaultedRange:m}=Ce(o),{transitionName:M,showTransition:_}=Qt(c),{hideNavigationButtons:E}=on(),L=e.ref(null),h=e.ref(null),A=e.ref([]),p=e.ref(null),x=e.ref(!1);e.onMounted(()=>{a("mount"),!o.timePicker&&o.arrowNavigation?l([Ye(L.value)],"time"):i(!0,o.timePicker)});const U=e.computed(()=>m.value.enabled&&o.modelAuto?xa(o.internalModelValue):!0),G=e.ref(!1),Q=$=>({hours:Array.isArray(o.hours)?o.hours[$]:o.hours,minutes:Array.isArray(o.minutes)?o.minutes[$]:o.minutes,seconds:Array.isArray(o.seconds)?o.seconds[$]:o.seconds}),g=e.computed(()=>{const $=[];if(m.value.enabled)for(let te=0;te<2;te++)$.push(Q(te));else $.push(Q(0));return $}),R=($,te=!1,S="")=>{te||a("reset-flow"),G.value=$,a($?"overlay-opened":"overlay-closed",Ie.time),o.arrowNavigation&&i($),e.nextTick(()=>{S!==""&&A.value[0]&&A.value[0].openChildCmp(S)})},O=e.computed(()=>({dp__btn:!0,dp__button:!0,dp__button_bottom:o.autoApply&&!T.value.keepActionRow})),W=Ue(d,"timePicker"),le=($,te,S)=>m.value.enabled?te===0?[$,g.value[1][S]]:[g.value[0][S],$]:$,ue=$=>{a("update:hours",$)},y=$=>{a("update:minutes",$)},z=$=>{a("update:seconds",$)},J=()=>{if(p.value&&!f.value.enabled&&!o.noOverlayFocus){const $=Fa(p.value);$&&$.focus({preventScroll:!0})}},w=$=>{x.value=!1,a("overlay-closed",$)},H=$=>{x.value=!0,a("overlay-opened",$)};return r({toggleTimePicker:R}),($,te)=>{var S;return e.openBlock(),e.createElementBlock("div",{class:"dp--tp-wrap","data-dp-mobile":$.isMobile},[!$.timePicker&&!$.timePickerInline?e.withDirectives((e.openBlock(),e.createElementBlock("button",{key:0,ref_key:"openTimePickerBtn",ref:L,type:"button",class:e.normalizeClass({...O.value,"dp--hidden-el":G.value}),"aria-label":(S=e.unref(v))==null?void 0:S.openTimePicker,tabindex:$.noOverlayFocus?void 0:0,"data-test-id":"open-time-picker-btn",onKeydown:te[0]||(te[0]=V=>e.unref(ze)(V,()=>R(!0))),onClick:te[1]||(te[1]=V=>R(!0))},[$.$slots["clock-icon"]?e.renderSlot($.$slots,"clock-icon",{key:0}):e.createCommentVNode("",!0),$.$slots["clock-icon"]?e.createCommentVNode("",!0):(e.openBlock(),e.createBlock(e.unref(dn),{key:1}))],42,Cs)),[[e.vShow,!e.unref(E)($.hideNavigation,"time")]]):e.createCommentVNode("",!0),e.createVNode(e.Transition,{name:e.unref(M)(G.value),css:e.unref(_)&&!$.timePickerInline},{default:e.withCtx(()=>{var V,k;return[G.value||$.timePicker||$.timePickerInline?(e.openBlock(),e.createElementBlock("div",{key:0,ref_key:"overlayRef",ref:p,role:$.timePickerInline?void 0:"dialog",class:e.normalizeClass({dp__overlay:!$.timePickerInline,"dp--overlay-absolute":!o.timePicker&&!$.timePickerInline,"dp--overlay-relative":o.timePicker}),style:e.normalizeStyle($.timePicker?{height:`${e.unref(T).modeHeight}px`}:void 0),"aria-label":(V=e.unref(v))==null?void 0:V.timePicker,tabindex:$.timePickerInline?void 0:0},[e.createElementVNode("div",{class:e.normalizeClass($.timePickerInline?"dp__time_picker_inline_container":"dp__overlay_container dp__container_flex dp__time_picker_overlay_container"),style:{display:"flex"}},[$.$slots["time-picker-overlay"]?e.renderSlot($.$slots,"time-picker-overlay",{key:0,hours:t.hours,minutes:t.minutes,seconds:t.seconds,setHours:ue,setMinutes:y,setSeconds:z}):e.createCommentVNode("",!0),$.$slots["time-picker-overlay"]?e.createCommentVNode("",!0):(e.openBlock(),e.createElementBlock("div",{key:1,class:e.normalizeClass($.timePickerInline?"dp__flex":"dp__overlay_row dp__flex_row")},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(g.value,(K,ne)=>e.withDirectives((e.openBlock(),e.createBlock(Ts,e.mergeProps({key:ne,ref_for:!0},{...$.$props,order:ne,hours:K.hours,minutes:K.minutes,seconds:K.seconds,closeTimePickerBtn:h.value,disabledTimesConfig:t.disabledTimesConfig,disabled:ne===0?e.unref(m).fixedStart:e.unref(m).fixedEnd},{ref_for:!0,ref_key:"timeInputRefs",ref:A,"validate-time":(N,X)=>t.validateTime(N,le(X,ne,N)),"onUpdate:hours":N=>ue(le(N,ne,"hours")),"onUpdate:minutes":N=>y(le(N,ne,"minutes")),"onUpdate:seconds":N=>z(le(N,ne,"seconds")),onMounted:J,onOverlayClosed:w,onOverlayOpened:H,onAmPmChange:te[2]||(te[2]=N=>$.$emit("am-pm-change",N))}),e.createSlots({_:2},[e.renderList(e.unref(W),(N,X)=>({name:N,fn:e.withCtx(s=>[e.renderSlot($.$slots,N,e.mergeProps({ref_for:!0},s))])}))]),1040,["validate-time","onUpdate:hours","onUpdate:minutes","onUpdate:seconds"])),[[e.vShow,ne===0?!0:U.value]])),128))],2)),!$.timePicker&&!$.timePickerInline?e.withDirectives((e.openBlock(),e.createElementBlock("button",{key:2,ref_key:"closeTimePickerBtn",ref:h,type:"button",class:e.normalizeClass({...O.value,"dp--hidden-el":x.value}),"aria-label":(k=e.unref(v))==null?void 0:k.closeTimePicker,tabindex:"0",onKeydown:te[3]||(te[3]=K=>e.unref(ze)(K,()=>R(!1))),onClick:te[4]||(te[4]=K=>R(!1))},[$.$slots["calendar-icon"]?e.renderSlot($.$slots,"calendar-icon",{key:0}):e.createCommentVNode("",!0),$.$slots["calendar-icon"]?e.createCommentVNode("",!0):(e.openBlock(),e.createBlock(e.unref(We),{key:1}))],42,Ss)),[[e.vShow,!e.unref(E)($.hideNavigation,"time")]]):e.createCommentVNode("",!0)],2)],14,Bs)):e.createCommentVNode("",!0)]}),_:3},8,["name","css"])],8,Ps)}}}),sr=(t,r,n,a)=>{const{defaultedRange:o}=Ce(t),l=(p,x)=>Array.isArray(r[p])?r[p][x]:r[p],i=p=>t.enableSeconds?Array.isArray(r.seconds)?r.seconds[p]:r.seconds:0,d=(p,x)=>p?x!==void 0?mt(p,l("hours",x),l("minutes",x),i(x)):mt(p,r.hours,r.minutes,i()):Ea(q(),i(x)),c=(p,x)=>{r[p]=x},v=e.computed(()=>t.modelAuto&&o.value.enabled?Array.isArray(n.value)?n.value.length>1:!1:o.value.enabled),f=(p,x)=>{const U=Object.fromEntries(Object.keys(r).map(G=>G===p?[G,x]:[G,r[G]].slice()));if(v.value&&!o.value.disableTimeRangeValidation){const G=g=>n.value?mt(n.value[g],U.hours[g],U.minutes[g],U.seconds[g]):null,Q=g=>Na(n.value[g],0);return!(pe(G(0),G(1))&&(Dt(G(0),Q(1))||At(G(1),Q(0))))}return!0},T=(p,x)=>{f(p,x)&&(c(p,x),a&&a())},m=p=>{T("hours",p)},M=p=>{T("minutes",p)},_=p=>{T("seconds",p)},E=(p,x,U,G)=>{x&&m(p),!x&&!U&&M(p),U&&_(p),n.value&&G(n.value)},L=p=>{if(p){const x=Array.isArray(p),U=x?[+p[0].hours,+p[1].hours]:+p.hours,G=x?[+p[0].minutes,+p[1].minutes]:+p.minutes,Q=x?[+p[0].seconds,+p[1].seconds]:+p.seconds;c("hours",U),c("minutes",G),t.enableSeconds&&c("seconds",Q)}},h=(p,x)=>{const U={hours:Array.isArray(r.hours)?r.hours[p]:r.hours,disabledArr:[]};return(x||x===0)&&(U.hours=x),Array.isArray(t.disabledTimes)&&(U.disabledArr=o.value.enabled&&Array.isArray(t.disabledTimes[p])?t.disabledTimes[p]:t.disabledTimes),U},A=e.computed(()=>(p,x)=>{var U;if(Array.isArray(t.disabledTimes)){const{disabledArr:G,hours:Q}=h(p,x),g=G.filter(R=>+R.hours===Q);return((U=g[0])==null?void 0:U.minutes)==="*"?{hours:[Q],minutes:void 0,seconds:void 0}:{hours:[],minutes:(g==null?void 0:g.map(R=>+R.minutes))??[],seconds:(g==null?void 0:g.map(R=>R.seconds?+R.seconds:void 0))??[]}}return{hours:[],minutes:[],seconds:[]}});return{setTime:c,updateHours:m,updateMinutes:M,updateSeconds:_,getSetDateTime:d,updateTimeValues:E,getSecondsValue:i,assignStartTime:L,validateTime:f,disabledTimesConfig:A}},As=(t,r)=>{const n=()=>{t.isTextInputDate&&x()},{modelValue:a,time:o}=Gt(t,r,n),{defaultedStartTime:l,defaultedRange:i,defaultedTz:d}=Ce(t),{updateTimeValues:c,getSetDateTime:v,setTime:f,assignStartTime:T,disabledTimesConfig:m,validateTime:M}=sr(t,o,a,_);function _(){r("update-flow-step")}const E=Q=>{const{hours:g,minutes:R,seconds:O}=Q;return{hours:+g,minutes:+R,seconds:O?+O:0}},L=()=>{if(t.startTime){if(Array.isArray(t.startTime)){const g=E(t.startTime[0]),R=E(t.startTime[1]);return[ve(q(),g),ve(q(),R)]}const Q=E(t.startTime);return ve(q(),Q)}return i.value.enabled?[null,null]:null},h=()=>{if(i.value.enabled){const[Q,g]=L();a.value=[qe(v(Q,0),d.value.timezone),qe(v(g,1),d.value.timezone)]}else a.value=qe(v(L()),d.value.timezone)},A=Q=>Array.isArray(Q)?[Tt(q(Q[0])),Tt(q(Q[1]))]:[Tt(Q??q())],p=(Q,g,R)=>{f("hours",Q),f("minutes",g),f("seconds",t.enableSeconds?R:0)},x=()=>{const[Q,g]=A(a.value);return i.value.enabled?p([Q.hours,g.hours],[Q.minutes,g.minutes],[Q.seconds,g.seconds]):p(Q.hours,Q.minutes,Q.seconds)};e.onMounted(()=>{if(!t.shadow)return T(l.value),a.value?x():h()});const U=()=>{Array.isArray(a.value)?a.value=a.value.map((Q,g)=>Q&&v(Q,g)):a.value=v(a.value),r("time-update")};return{modelValue:a,time:o,disabledTimesConfig:m,updateTime:(Q,g=!0,R=!1)=>{c(Q,g,R,U)},validateTime:M}},$s=e.defineComponent({compatConfig:{MODE:3},__name:"TimePickerSolo",props:{...et},emits:["update:internal-model-value","time-update","am-pm-change","mount","reset-flow","update-flow-step","overlay-toggle"],setup(t,{expose:r,emit:n}){const a=n,o=t,l=e.useSlots(),i=Ue(l,"timePicker"),d=e.ref(null),{time:c,modelValue:v,disabledTimesConfig:f,updateTime:T,validateTime:m}=As(o,a);return e.onMounted(()=>{o.shadow||a("mount",null)}),r({getSidebarProps:()=>({modelValue:v,time:c,updateTime:T}),toggleTimePicker:(E,L=!1,h="")=>{var A;(A=d.value)==null||A.toggleTimePicker(E,L,h)}}),(E,L)=>(e.openBlock(),e.createBlock(an,{"multi-calendars":0,stretch:"","is-mobile":E.isMobile},{default:e.withCtx(()=>[e.createVNode(lr,e.mergeProps({ref_key:"tpRef",ref:d},E.$props,{hours:e.unref(c).hours,minutes:e.unref(c).minutes,seconds:e.unref(c).seconds,"internal-model-value":E.internalModelValue,"disabled-times-config":e.unref(f),"validate-time":e.unref(m),"onUpdate:hours":L[0]||(L[0]=h=>e.unref(T)(h)),"onUpdate:minutes":L[1]||(L[1]=h=>e.unref(T)(h,!1)),"onUpdate:seconds":L[2]||(L[2]=h=>e.unref(T)(h,!1,!0)),onAmPmChange:L[3]||(L[3]=h=>E.$emit("am-pm-change",h)),onResetFlow:L[4]||(L[4]=h=>E.$emit("reset-flow")),onOverlayClosed:L[5]||(L[5]=h=>E.$emit("overlay-toggle",{open:!1,overlay:h})),onOverlayOpened:L[6]||(L[6]=h=>E.$emit("overlay-toggle",{open:!0,overlay:h}))}),e.createSlots({_:2},[e.renderList(e.unref(i),(h,A)=>({name:h,fn:e.withCtx(p=>[e.renderSlot(E.$slots,h,e.normalizeProps(e.guardReactiveProps(p)))])}))]),1040,["hours","minutes","seconds","internal-model-value","disabled-times-config","validate-time"])]),_:3},8,["is-mobile"]))}}),Os={class:"dp--header-wrap"},Ns={key:0,class:"dp__month_year_wrap"},Es={key:0},Rs={class:"dp__month_year_wrap"},Ys=["data-dp-element","aria-label","data-test-id","onClick","onKeydown"],_s=e.defineComponent({compatConfig:{MODE:3},__name:"DpHeader",props:{month:{type:Number,default:0},year:{type:Number,default:0},instance:{type:Number,default:0},years:{type:Array,default:()=>[]},months:{type:Array,default:()=>[]},...et},emits:["update-month-year","mount","reset-flow","overlay-closed","overlay-opened"],setup(t,{expose:r,emit:n}){const a=n,o=t,{defaultedTransitions:l,defaultedAriaLabels:i,defaultedMultiCalendars:d,defaultedFilters:c,defaultedConfig:v,defaultedHighlight:f,propDates:T,defaultedUI:m}=Ce(o),{transitionName:M,showTransition:_}=Qt(l),{buildMatrix:E}=gt(),{handleMonthYearChange:L,isDisabled:h,updateMonthYear:A}=ns(o,a),{showLeftIcon:p,showRightIcon:x}=on(),U=e.ref(!1),G=e.ref(!1),Q=e.ref(!1),g=e.ref([null,null,null,null]);e.onMounted(()=>{a("mount")});const R=k=>({get:()=>o[k],set:K=>{const ne=k===Xe.month?Xe.year:Xe.month;a("update-month-year",{[k]:K,[ne]:o[ne]}),k===Xe.month?w(!0):H(!0)}}),O=e.computed(R(Xe.month)),W=e.computed(R(Xe.year)),le=e.computed(()=>k=>({month:o.month,year:o.year,items:k===Xe.month?o.months:o.years,instance:o.instance,updateMonthYear:A,toggle:k===Xe.month?w:H})),ue=e.computed(()=>{const k=o.months.find(K=>K.value===o.month);return k||{text:"",value:0}}),y=e.computed(()=>Et(o.months,k=>{const K=o.month===k.value,ne=Ht(k.value,Wa(o.year,T.value.minDate),qa(o.year,T.value.maxDate))||c.value.months.includes(k.value),N=Xa(f.value,k.value,o.year);return{active:K,disabled:ne,highlighted:N}})),z=e.computed(()=>Et(o.years,k=>{const K=o.year===k.value,ne=Ht(k.value,Rt(T.value.minDate),Rt(T.value.maxDate))||c.value.years.includes(k.value),N=_n(f.value,k.value);return{active:K,disabled:ne,highlighted:N}})),J=(k,K,ne)=>{ne!==void 0?k.value=ne:k.value=!k.value,k.value?(Q.value=!0,a("overlay-opened",K)):(Q.value=!1,a("overlay-closed",K))},w=(k=!1,K)=>{$(k),J(U,Ie.month,K)},H=(k=!1,K)=>{$(k),J(G,Ie.year,K)},$=k=>{k||a("reset-flow")},te=(k,K)=>{o.arrowNavigation&&(g.value[K]=Ye(k),E(g.value,"monthYear"))},S=e.computed(()=>{var k,K,ne,N,X,s;return[{type:Xe.month,index:1,toggle:w,modelValue:O.value,updateModelValue:C=>O.value=C,text:ue.value.text,showSelectionGrid:U.value,items:y.value,ariaLabel:(k=i.value)==null?void 0:k.openMonthsOverlay,overlayLabel:((ne=(K=i.value).monthPicker)==null?void 0:ne.call(K,!0))??void 0},{type:Xe.year,index:2,toggle:H,modelValue:W.value,updateModelValue:C=>W.value=C,text:La(o.year,o.locale),showSelectionGrid:G.value,items:z.value,ariaLabel:(N=i.value)==null?void 0:N.openYearsOverlay,overlayLabel:((s=(X=i.value).yearPicker)==null?void 0:s.call(X,!0))??void 0}]}),V=e.computed(()=>o.disableYearSelect?[S.value[0]]:o.yearFirst?[...S.value].reverse():S.value);return r({toggleMonthPicker:w,toggleYearPicker:H,handleMonthYearChange:L}),(k,K)=>{var ne,N,X,s,C,ae;return e.openBlock(),e.createElementBlock("div",Os,[k.$slots["month-year"]?(e.openBlock(),e.createElementBlock("div",Ns,[e.renderSlot(k.$slots,"month-year",e.normalizeProps(e.guardReactiveProps({month:t.month,year:t.year,months:t.months,years:t.years,updateMonthYear:e.unref(A),handleMonthYearChange:e.unref(L),instance:t.instance,isDisabled:e.unref(h)})))])):(e.openBlock(),e.createElementBlock(e.Fragment,{key:1},[k.$slots["top-extra"]?(e.openBlock(),e.createElementBlock("div",Es,[e.renderSlot(k.$slots,"top-extra",{value:k.internalModelValue})])):e.createCommentVNode("",!0),e.createElementVNode("div",Rs,[e.unref(p)(e.unref(d),t.instance)&&!k.vertical?(e.openBlock(),e.createBlock(jt,{key:0,"aria-label":(ne=e.unref(i))==null?void 0:ne.prevMonth,disabled:e.unref(h)(!1),class:e.normalizeClass((N=e.unref(m))==null?void 0:N.navBtnPrev),"el-name":"action-prev",onActivate:K[0]||(K[0]=B=>e.unref(L)(!1,!0)),onSetRef:K[1]||(K[1]=B=>te(B,0))},{default:e.withCtx(()=>[k.$slots["arrow-left"]?e.renderSlot(k.$slots,"arrow-left",{key:0}):e.createCommentVNode("",!0),k.$slots["arrow-left"]?e.createCommentVNode("",!0):(e.openBlock(),e.createBlock(e.unref(un),{key:1}))]),_:3},8,["aria-label","disabled","class"])):e.createCommentVNode("",!0),e.createElementVNode("div",{class:e.normalizeClass(["dp__month_year_wrap",{dp__year_disable_select:k.disableYearSelect}])},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(V.value,(B,me)=>(e.openBlock(),e.createElementBlock(e.Fragment,{key:B.type},[e.createElementVNode("button",{ref_for:!0,ref:de=>te(de,me+1),type:"button","data-dp-element":`overlay-${B.type}`,class:e.normalizeClass(["dp__btn dp__month_year_select",{"dp--hidden-el":Q.value}]),"aria-label":`${B.text}-${B.ariaLabel}`,"data-test-id":`${B.type}-toggle-overlay-${t.instance}`,onClick:B.toggle,onKeydown:de=>e.unref(ze)(de,()=>B.toggle(),!0)},[k.$slots[B.type]?e.renderSlot(k.$slots,B.type,{key:0,text:B.text,value:o[B.type]}):e.createCommentVNode("",!0),k.$slots[B.type]?e.createCommentVNode("",!0):(e.openBlock(),e.createElementBlock(e.Fragment,{key:1},[e.createTextVNode(e.toDisplayString(B.text),1)],64))],42,Ys),e.createVNode(e.Transition,{name:e.unref(M)(B.showSelectionGrid),css:e.unref(_)},{default:e.withCtx(()=>[B.showSelectionGrid?(e.openBlock(),e.createBlock(Ut,{key:0,items:B.items,"arrow-navigation":k.arrowNavigation,"hide-navigation":k.hideNavigation,"is-last":k.autoApply&&!e.unref(v).keepActionRow,"skip-button-ref":!1,config:k.config,type:B.type,"header-refs":[],"esc-close":k.escClose,"menu-wrap-ref":k.menuWrapRef,"text-input":k.textInput,"aria-labels":k.ariaLabels,"overlay-label":B.overlayLabel,onSelected:B.updateModelValue,onToggle:B.toggle},e.createSlots({"button-icon":e.withCtx(()=>[k.$slots["calendar-icon"]?e.renderSlot(k.$slots,"calendar-icon",{key:0}):e.createCommentVNode("",!0),k.$slots["calendar-icon"]?e.createCommentVNode("",!0):(e.openBlock(),e.createBlock(e.unref(We),{key:1}))]),_:2},[k.$slots[`${B.type}-overlay-value`]?{name:"item",fn:e.withCtx(({item:de})=>[e.renderSlot(k.$slots,`${B.type}-overlay-value`,{text:de.text,value:de.value})]),key:"0"}:void 0,k.$slots[`${B.type}-overlay`]?{name:"overlay",fn:e.withCtx(()=>[e.renderSlot(k.$slots,`${B.type}-overlay`,e.mergeProps({ref_for:!0},le.value(B.type)))]),key:"1"}:void 0,k.$slots[`${B.type}-overlay-header`]?{name:"header",fn:e.withCtx(()=>[e.renderSlot(k.$slots,`${B.type}-overlay-header`,{toggle:B.toggle})]),key:"2"}:void 0]),1032,["items","arrow-navigation","hide-navigation","is-last","config","type","esc-close","menu-wrap-ref","text-input","aria-labels","overlay-label","onSelected","onToggle"])):e.createCommentVNode("",!0)]),_:2},1032,["name","css"])],64))),128))],2),e.unref(p)(e.unref(d),t.instance)&&k.vertical?(e.openBlock(),e.createBlock(jt,{key:1,"aria-label":(X=e.unref(i))==null?void 0:X.prevMonth,"el-name":"action-prev",disabled:e.unref(h)(!1),class:e.normalizeClass((s=e.unref(m))==null?void 0:s.navBtnPrev),onActivate:K[2]||(K[2]=B=>e.unref(L)(!1,!0))},{default:e.withCtx(()=>[k.$slots["arrow-up"]?e.renderSlot(k.$slots,"arrow-up",{key:0}):e.createCommentVNode("",!0),k.$slots["arrow-up"]?e.createCommentVNode("",!0):(e.openBlock(),e.createBlock(e.unref(fn),{key:1}))]),_:3},8,["aria-label","disabled","class"])):e.createCommentVNode("",!0),e.unref(x)(e.unref(d),t.instance)?(e.openBlock(),e.createBlock(jt,{key:2,ref:"rightIcon","el-name":"action-next",disabled:e.unref(h)(!0),"aria-label":(C=e.unref(i))==null?void 0:C.nextMonth,class:e.normalizeClass((ae=e.unref(m))==null?void 0:ae.navBtnNext),onActivate:K[3]||(K[3]=B=>e.unref(L)(!0,!0)),onSetRef:K[4]||(K[4]=B=>te(B,k.disableYearSelect?2:3))},{default:e.withCtx(()=>[k.$slots[k.vertical?"arrow-down":"arrow-right"]?e.renderSlot(k.$slots,k.vertical?"arrow-down":"arrow-right",{key:0}):e.createCommentVNode("",!0),k.$slots[k.vertical?"arrow-down":"arrow-right"]?e.createCommentVNode("",!0):(e.openBlock(),e.createBlock(e.resolveDynamicComponent(k.vertical?e.unref(mn):e.unref(cn)),{key:1}))]),_:3},8,["disabled","aria-label","class"])):e.createCommentVNode("",!0)])],64))])}}}),xs={class:"dp__calendar_header",role:"row"},Vs={key:0,class:"dp__calendar_header_item",role:"gridcell"},Is=["aria-label"],Fs={key:0,class:"dp__calendar_item dp__week_num",role:"gridcell"},Ls={class:"dp__cell_inner"},zs=["id","aria-pressed","aria-disabled","aria-label","tabindex","data-test-id","onClick","onTouchend","onKeydown","onMouseenter","onMouseleave","onMousedown"],Hs=e.defineComponent({compatConfig:{MODE:3},__name:"DpCalendar",props:{mappedDates:{type:Array,default:()=>[]},instance:{type:Number,default:0},month:{type:Number,default:0},year:{type:Number,default:0},...et},emits:["select-date","set-hover-date","handle-scroll","mount","handle-swipe","handle-space","tooltip-open","tooltip-close"],setup(t,{expose:r,emit:n}){const a=n,o=t,{buildMultiLevelMatrix:l}=gt(),{defaultedTransitions:i,defaultedConfig:d,defaultedAriaLabels:c,defaultedMultiCalendars:v,defaultedWeekNumbers:f,defaultedMultiDates:T,defaultedUI:m}=Ce(o),M=e.ref(null),_=e.ref({bottom:"",left:"",transform:""}),E=e.ref([]),L=e.ref(null),h=e.ref(!0),A=e.ref(""),p=e.ref({startX:0,endX:0,startY:0,endY:0}),x=e.ref([]),U=e.ref({left:"50%"}),G=e.ref(!1),Q=e.computed(()=>o.calendar?o.calendar(o.mappedDates):o.mappedDates),g=e.computed(()=>o.dayNames?Array.isArray(o.dayNames)?o.dayNames:o.dayNames(o.locale,+o.weekStart):vl(o.formatLocale,o.locale,+o.weekStart));e.onMounted(()=>{a("mount",{cmp:"calendar",refs:E}),d.value.noSwipe||L.value&&(L.value.addEventListener("touchstart",te,{passive:!1}),L.value.addEventListener("touchend",S,{passive:!1}),L.value.addEventListener("touchmove",V,{passive:!1})),o.monthChangeOnScroll&&L.value&&L.value.addEventListener("wheel",ne,{passive:!1})});const R=B=>B?o.vertical?"vNext":"next":o.vertical?"vPrevious":"previous",O=(B,me)=>{if(o.transitions){const de=xe(it(q(),o.month,o.year));A.value=Ne(xe(it(q(),B,me)),de)?i.value[R(!0)]:i.value[R(!1)],h.value=!1,e.nextTick(()=>{h.value=!0})}},W=e.computed(()=>({...m.value.calendar??{}})),le=e.computed(()=>B=>{const me=Ml(B);return{dp__marker_dot:me.type==="dot",dp__marker_line:me.type==="line"}}),ue=e.computed(()=>B=>pe(B,M.value)),y=e.computed(()=>({dp__calendar:!0,dp__calendar_next:v.value.count>0&&o.instance!==0})),z=e.computed(()=>B=>o.hideOffsetDates?B.current:!0),J=async(B,me)=>{const{width:de,height:ee}=B.getBoundingClientRect();M.value=me.value;let u={left:`${de/2}px`},I=-50;if(await e.nextTick(),x.value[0]){const{left:se,width:Y}=x.value[0].getBoundingClientRect();se<0&&(u={left:"0"},I=0,U.value.left=`${de/2}px`),window.innerWidth<se+Y&&(u={right:"0"},I=0,U.value.left=`${Y-de/2}px`)}_.value={bottom:`${ee}px`,...u,transform:`translateX(${I}%)`}},w=async(B,me,de)=>{var u,I,se;const ee=Ye(E.value[me][de]);ee&&((u=B.marker)!=null&&u.customPosition&&((se=(I=B.marker)==null?void 0:I.tooltip)!=null&&se.length)?_.value=B.marker.customPosition(ee):await J(ee,B),a("tooltip-open",B.marker))},H=async(B,me,de)=>{var ee,u;if(G.value&&T.value.enabled&&T.value.dragSelect)return a("select-date",B);if(a("set-hover-date",B),(u=(ee=B.marker)==null?void 0:ee.tooltip)!=null&&u.length){if(o.hideOffsetDates&&!B.current)return;await w(B,me,de)}},$=B=>{M.value&&(M.value=null,_.value=JSON.parse(JSON.stringify({bottom:"",left:"",transform:""})),a("tooltip-close",B.marker))},te=B=>{p.value.startX=B.changedTouches[0].screenX,p.value.startY=B.changedTouches[0].screenY},S=B=>{p.value.endX=B.changedTouches[0].screenX,p.value.endY=B.changedTouches[0].screenY,k()},V=B=>{o.vertical&&!o.inline&&B.preventDefault()},k=()=>{const B=o.vertical?"Y":"X";Math.abs(p.value[`start${B}`]-p.value[`end${B}`])>10&&a("handle-swipe",p.value[`start${B}`]>p.value[`end${B}`]?"right":"left")},K=(B,me,de)=>{B&&(Array.isArray(E.value[me])?E.value[me][de]=B:E.value[me]=[B]),o.arrowNavigation&&l(E.value,"calendar")},ne=B=>{o.monthChangeOnScroll&&(B.preventDefault(),a("handle-scroll",B))},N=B=>f.value.type==="local"?wn(B.value,{weekStartsOn:+o.weekStart}):f.value.type==="iso"?yn(B.value):typeof f.value.type=="function"?f.value.type(B.value):"",X=B=>{const me=B[0];return f.value.hideOnOffsetDates?B.some(de=>de.current)?N(me):"":N(me)},s=(B,me,de=!0)=>{!de&&Sl()||(!T.value.enabled||d.value.allowPreventDefault)&&(ft(B,d.value),a("select-date",me))},C=B=>{ft(B,d.value)},ae=B=>{T.value.enabled&&T.value.dragSelect?(G.value=!0,a("select-date",B)):T.value.enabled&&a("select-date",B)};return r({triggerTransition:O}),(B,me)=>(e.openBlock(),e.createElementBlock("div",{class:e.normalizeClass(y.value)},[e.createElementVNode("div",{ref_key:"calendarWrapRef",ref:L,class:e.normalizeClass(W.value),role:"grid"},[e.createElementVNode("div",xs,[B.weekNumbers?(e.openBlock(),e.createElementBlock("div",Vs,e.toDisplayString(B.weekNumName),1)):e.createCommentVNode("",!0),(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(g.value,(de,ee)=>{var u,I;return e.openBlock(),e.createElementBlock("div",{key:ee,class:"dp__calendar_header_item",role:"gridcell","data-test-id":"calendar-header","aria-label":(I=(u=e.unref(c))==null?void 0:u.weekDay)==null?void 0:I.call(u,ee)},[B.$slots["calendar-header"]?e.renderSlot(B.$slots,"calendar-header",{key:0,day:de,index:ee}):e.createCommentVNode("",!0),B.$slots["calendar-header"]?e.createCommentVNode("",!0):(e.openBlock(),e.createElementBlock(e.Fragment,{key:1},[e.createTextVNode(e.toDisplayString(de),1)],64))],8,Is)}),128))]),me[2]||(me[2]=e.createElementVNode("div",{class:"dp__calendar_header_separator"},null,-1)),e.createVNode(e.Transition,{name:A.value,css:!!B.transitions},{default:e.withCtx(()=>[h.value?(e.openBlock(),e.createElementBlock("div",{key:0,class:"dp__calendar",role:"rowgroup",onMouseleave:me[1]||(me[1]=de=>G.value=!1)},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(Q.value,(de,ee)=>(e.openBlock(),e.createElementBlock("div",{key:ee,class:"dp__calendar_row",role:"row"},[B.weekNumbers?(e.openBlock(),e.createElementBlock("div",Fs,[e.createElementVNode("div",Ls,e.toDisplayString(X(de.days)),1)])):e.createCommentVNode("",!0),(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(de.days,(u,I)=>{var se,Y,ge;return e.openBlock(),e.createElementBlock("div",{id:e.unref(xn)(u.value),ref_for:!0,ref:he=>K(he,ee,I),key:I+ee,role:"gridcell",class:"dp__calendar_item","aria-pressed":(u.classData.dp__active_date||u.classData.dp__range_start||u.classData.dp__range_start)??void 0,"aria-disabled":u.classData.dp__cell_disabled||void 0,"aria-label":(Y=(se=e.unref(c))==null?void 0:se.day)==null?void 0:Y.call(se,u),tabindex:!u.current&&B.hideOffsetDates?void 0:0,"data-test-id":e.unref(xn)(u.value),onClick:e.withModifiers(he=>s(he,u),["prevent"]),onTouchend:he=>s(he,u,!1),onKeydown:he=>e.unref(ze)(he,()=>B.$emit("select-date",u)),onMouseenter:he=>H(u,ee,I),onMouseleave:he=>$(u),onMousedown:he=>ae(u),onMouseup:me[0]||(me[0]=he=>G.value=!1)},[e.createElementVNode("div",{class:e.normalizeClass(["dp__cell_inner",u.classData])},[B.$slots.day&&z.value(u)?e.renderSlot(B.$slots,"day",{key:0,day:+u.text,date:u.value}):e.createCommentVNode("",!0),B.$slots.day?e.createCommentVNode("",!0):(e.openBlock(),e.createElementBlock(e.Fragment,{key:1},[e.createTextVNode(e.toDisplayString(u.text),1)],64)),u.marker&&z.value(u)?(e.openBlock(),e.createElementBlock(e.Fragment,{key:2},[B.$slots.marker?e.renderSlot(B.$slots,"marker",{key:0,marker:u.marker,day:+u.text,date:u.value}):(e.openBlock(),e.createElementBlock("div",{key:1,class:e.normalizeClass(le.value(u.marker)),style:e.normalizeStyle(u.marker.color?{backgroundColor:u.marker.color}:{})},null,6))],64)):e.createCommentVNode("",!0),ue.value(u.value)?(e.openBlock(),e.createElementBlock("div",{key:3,ref_for:!0,ref_key:"activeTooltip",ref:x,class:"dp__marker_tooltip",style:e.normalizeStyle(_.value)},[(ge=u.marker)!=null&&ge.tooltip?(e.openBlock(),e.createElementBlock("div",{key:0,class:"dp__tooltip_content",onClick:C},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(u.marker.tooltip,(he,De)=>(e.openBlock(),e.createElementBlock("div",{key:De,class:"dp__tooltip_text"},[B.$slots["marker-tooltip"]?e.renderSlot(B.$slots,"marker-tooltip",{key:0,tooltip:he,day:u.value}):e.createCommentVNode("",!0),B.$slots["marker-tooltip"]?e.createCommentVNode("",!0):(e.openBlock(),e.createElementBlock(e.Fragment,{key:1},[e.createElementVNode("div",{class:"dp__tooltip_mark",style:e.normalizeStyle(he.color?{backgroundColor:he.color}:{})},null,4),e.createElementVNode("div",null,e.toDisplayString(he.text),1)],64))]))),128)),e.createElementVNode("div",{class:"dp__arrow_bottom_tp",style:e.normalizeStyle(U.value)},null,4)])):e.createCommentVNode("",!0)],4)):e.createCommentVNode("",!0)],2)],40,zs)}),128))]))),128))],32)):e.createCommentVNode("",!0)]),_:3},8,["name","css"])],2)],2))}}),ir=t=>Array.isArray(t),Ws=(t,r,n,a)=>{const o=e.ref([]),l=e.ref(new Date),i=e.ref(),d=()=>S(t.isTextInputDate),{modelValue:c,calendars:v,time:f,today:T}=Gt(t,r,d),{defaultedMultiCalendars:m,defaultedStartTime:M,defaultedRange:_,defaultedConfig:E,defaultedTz:L,propDates:h,defaultedMultiDates:A}=Ce(t),{validateMonthYearInRange:p,isDisabled:x,isDateRangeAllowed:U,checkMinMaxRange:G}=pt(t),{updateTimeValues:Q,getSetDateTime:g,setTime:R,assignStartTime:O,validateTime:W,disabledTimesConfig:le}=sr(t,f,c,a),ue=e.computed(()=>b=>v.value[b]?v.value[b].month:0),y=e.computed(()=>b=>v.value[b]?v.value[b].year:0),z=b=>!E.value.keepViewOnOffsetClick||b?!0:!i.value,J=(b,Z,P,j=!1)=>{var re,He;z(j)&&(v.value[b]||(v.value[b]={month:0,year:0}),v.value[b].month=Ia(Z)?(re=v.value[b])==null?void 0:re.month:Z,v.value[b].year=Ia(P)?(He=v.value[b])==null?void 0:He.year:P)},w=()=>{t.autoApply&&r("select-date")},H=()=>{M.value&&O(M.value)};e.onMounted(()=>{t.shadow||(c.value||(me(),H()),S(!0),t.focusStartDate&&t.startDate&&me())});const $=e.computed(()=>{var b;return(b=t.flow)!=null&&b.length&&!t.partialFlow?t.flowStep===t.flow.length:!0}),te=()=>{t.autoApply&&$.value&&r("auto-apply",t.partialFlow?t.flowStep!==t.flow.length:!1)},S=(b=!1)=>{if(c.value)return Array.isArray(c.value)?(o.value=c.value,s(b)):K(c.value,b);if(m.value.count&&b&&!t.startDate)return k(q(),b)},V=()=>Array.isArray(c.value)&&_.value.enabled?ye(c.value[0])===ye(c.value[1]??c.value[0]):!1,k=(b=new Date,Z=!1)=>{if((!m.value.count||!m.value.static||Z)&&J(0,ye(b),fe(b)),m.value.count&&(!c.value||V()||!m.value.solo)&&(!m.value.solo||Z))for(let P=1;P<m.value.count;P++){const j=ve(q(),{month:ue.value(P-1),year:y.value(P-1)}),re=oa(j,{months:1});v.value[P]={month:ye(re),year:fe(re)}}},K=(b,Z)=>{k(b),R("hours",lt(b)),R("minutes",dt(b)),R("seconds",St(b)),m.value.count&&Z&&B()},ne=b=>{if(m.value.count){if(m.value.solo)return 0;const Z=ye(b[0]),P=ye(b[1]);return Math.abs(P-Z)<m.value.count?0:1}return 1},N=(b,Z)=>{b[1]&&_.value.showLastInRange?k(b[ne(b)],Z):k(b[0],Z);const P=(j,re)=>[j(b[0]),b[1]?j(b[1]):f[re][1]];R("hours",P(lt,"hours")),R("minutes",P(dt,"minutes")),R("seconds",P(St,"seconds"))},X=(b,Z)=>{if((_.value.enabled||t.weekPicker)&&!A.value.enabled)return N(b,Z);if(A.value.enabled&&Z){const P=b[b.length-1];return K(P,Z)}},s=b=>{const Z=c.value;X(Z,b),m.value.count&&m.value.solo&&B()},C=(b,Z)=>{const P=ve(q(),{month:ue.value(Z),year:y.value(Z)}),j=b<0?Ge(P,1):Ot(P,1);p(ye(j),fe(j),b<0,t.preventMinMaxNavigation)&&(J(Z,ye(j),fe(j)),r("update-month-year",{instance:Z,month:ye(j),year:fe(j)}),m.value.count&&!m.value.solo&&ae(Z),n())},ae=b=>{for(let Z=b-1;Z>=0;Z--){const P=Ot(ve(q(),{month:ue.value(Z+1),year:y.value(Z+1)}),1);J(Z,ye(P),fe(P))}for(let Z=b+1;Z<=m.value.count-1;Z++){const P=Ge(ve(q(),{month:ue.value(Z-1),year:y.value(Z-1)}),1);J(Z,ye(P),fe(P))}},B=()=>{if(Array.isArray(c.value)&&c.value.length===2){const b=q(q(c.value[1]?c.value[1]:Ge(c.value[0],1))),[Z,P]=[ye(c.value[0]),fe(c.value[0])],[j,re]=[ye(c.value[1]),fe(c.value[1])];(Z!==j||Z===j&&P!==re)&&m.value.solo&&J(1,ye(b),fe(b))}else c.value&&!Array.isArray(c.value)&&(J(0,ye(c.value),fe(c.value)),k(q()))},me=()=>{t.startDate&&(J(0,ye(q(t.startDate)),fe(q(t.startDate))),m.value.count&&ae(0))},de=(b,Z)=>{if(t.monthChangeOnScroll){const P=new Date().getTime()-l.value.getTime(),j=Math.abs(b.deltaY);let re=500;j>1&&(re=100),j>100&&(re=0),P>re&&(l.value=new Date,C(t.monthChangeOnScroll!=="inverse"?-b.deltaY:b.deltaY,Z))}},ee=(b,Z,P=!1)=>{t.monthChangeOnArrows&&t.vertical===P&&u(b,Z)},u=(b,Z)=>{C(b==="right"?-1:1,Z)},I=b=>{if(h.value.markers)return Zt(b.value,h.value.markers)},se=(b,Z)=>{switch(t.sixWeeks===!0?"append":t.sixWeeks){case"prepend":return[!0,!1];case"center":return[b==0,!0];case"fair":return[b==0||Z>b,!0];case"append":return[!1,!1];default:return[!1,!1]}},Y=(b,Z,P,j)=>{if(t.sixWeeks&&b.length<6){const re=6-b.length,He=(Z.getDay()+7-j)%7,xt=6-(P.getDay()+7-j)%7,[Kt,ea]=se(He,xt);for(let Pt=1;Pt<=re;Pt++)if(ea?!!(Pt%2)==Kt:Kt){const sn=b[0].days[0],ta=ge(je(sn.value,-7),ye(Z));b.unshift({days:ta})}else{const sn=b[b.length-1],ta=sn.days[sn.days.length-1],Di=ge(je(ta.value,1),ye(Z));b.push({days:Di})}}return b},ge=(b,Z)=>{const P=q(b),j=[];for(let re=0;re<7;re++){const He=je(P,re),tt=ye(He)!==Z;j.push({text:t.hideOffsetDates&&tt?"":He.getDate(),value:He,current:!tt,classData:{}})}return j},he=(b,Z)=>{const P=[],j=new Date(Z,b),re=new Date(Z,b+1,0),He=t.weekStart,tt=Ke(j,{weekStartsOn:He}),xt=Kt=>{const ea=ge(Kt,b);if(P.push({days:ea}),!P[P.length-1].days.some(Pt=>pe(xe(Pt.value),xe(re)))){const Pt=je(Kt,7);xt(Pt)}};return xt(tt),Y(P,j,re,He)},De=b=>{const Z=mt(q(b.value),f.hours,f.minutes,Ve());r("date-update",Z),A.value.enabled?Hn(Z,c,A.value.limit):c.value=Z,a(),e.nextTick().then(()=>{te()})},D=b=>_.value.noDisabledRange?Ua(o.value[0],b).some(P=>x(P)):!1,F=()=>{o.value=c.value?c.value.slice():[],o.value.length===2&&!(_.value.fixedStart||_.value.fixedEnd)&&(o.value=[])},Me=(b,Z)=>{const P=[q(b.value),je(q(b.value),+_.value.autoRange)];U(P)?(Z&&_e(b.value),o.value=P):r("invalid-date",b.value)},_e=b=>{const Z=ye(q(b)),P=fe(q(b));if(J(0,Z,P),m.value.count>0)for(let j=1;j<m.value.count;j++){const re=Nl(ve(q(b),{year:y.value(j-1),month:ue.value(j-1)}));J(j,re.month,re.year)}},ut=b=>{if(D(b.value)||!G(b.value,c.value,_.value.fixedStart?0:1))return r("invalid-date",b.value);o.value=rr(q(b.value),c,r,_)},ie=(b,Z)=>{if(F(),_.value.autoRange)return Me(b,Z);if(_.value.fixedStart||_.value.fixedEnd)return ut(b);o.value[0]?G(q(b.value),c.value)&&!D(b.value)?Ae(q(b.value),q(o.value[0]))?(o.value.unshift(q(b.value)),r("range-end",o.value[0])):(o.value[1]=q(b.value),r("range-end",o.value[1])):(t.autoApply&&r("auto-apply-invalid",b.value),r("invalid-date",b.value)):(o.value[0]=q(b.value),r("range-start",o.value[0]))},Ve=(b=!0)=>t.enableSeconds?Array.isArray(f.seconds)?b?f.seconds[0]:f.seconds[1]:f.seconds:0,Qe=b=>{o.value[b]=mt(o.value[b],f.hours[b],f.minutes[b],Ve(b!==1))},jn=()=>{var b,Z;o.value[0]&&o.value[1]&&+((b=o.value)==null?void 0:b[0])>+((Z=o.value)==null?void 0:Z[1])&&(o.value.reverse(),r("range-start",o.value[0]),r("range-end",o.value[1]))},ln=()=>{o.value.length&&(o.value[0]&&!o.value[1]?Qe(0):(Qe(0),Qe(1),a()),jn(),c.value=o.value.slice(),rn(o.value,r,t.autoApply,t.modelAuto))},Qn=(b,Z=!1)=>{if(x(b.value)||!b.current&&t.hideOffsetDates)return r("invalid-date",b.value);if(i.value=JSON.parse(JSON.stringify(b)),!_.value.enabled)return De(b);ir(f.hours)&&ir(f.minutes)&&!A.value.enabled&&(ie(b,Z),ln())},Gn=(b,Z)=>{var j;J(b,Z.month,Z.year,!0),m.value.count&&!m.value.solo&&ae(b),r("update-month-year",{instance:b,month:Z.month,year:Z.year}),n(m.value.solo?b:void 0);const P=(j=t.flow)!=null&&j.length?t.flow[t.flowStep]:void 0;!Z.fromNav&&(P===Ie.month||P===Ie.year)&&a()},Kn=(b,Z)=>{ar({value:b,modelValue:c,range:_.value.enabled,timezone:Z?void 0:L.value.timezone}),w(),t.multiCalendars&&e.nextTick().then(()=>S(!0))},Xn=()=>{const b=Tn(q(),L.value);!_.value.enabled&&!A.value.enabled?c.value=b:c.value&&Array.isArray(c.value)&&c.value[0]?A.value.enabled?c.value=[...c.value,b]:c.value=Ae(b,c.value[0])?[b,c.value[0]]:[c.value[0],b]:c.value=[b],w()},Jn=()=>{if(Array.isArray(c.value))if(A.value.enabled){const b=Zn();c.value[c.value.length-1]=g(b)}else c.value=c.value.map((b,Z)=>b&&g(b,Z));else c.value=g(c.value);r("time-update")},Zn=()=>Array.isArray(c.value)&&c.value.length?c.value[c.value.length-1]:null;return{calendars:v,modelValue:c,month:ue,year:y,time:f,disabledTimesConfig:le,today:T,validateTime:W,getCalendarDays:he,getMarker:I,handleScroll:de,handleSwipe:u,handleArrow:ee,selectDate:Qn,updateMonthYear:Gn,presetDate:Kn,selectCurrentDate:Xn,updateTime:(b,Z=!0,P=!1)=>{Q(b,Z,P,Jn)},assignMonthAndYear:k,setStartTime:H}},qs={key:0},Us=e.defineComponent({__name:"DatePicker",props:{...et},emits:["tooltip-open","tooltip-close","mount","update:internal-model-value","update-flow-step","reset-flow","auto-apply","focus-menu","select-date","range-start","range-end","invalid-fixed-range","time-update","am-pm-change","time-picker-open","time-picker-close","recalculate-position","update-month-year","auto-apply-invalid","date-update","invalid-date","overlay-toggle"],setup(t,{expose:r,emit:n}){const a=n,o=t,{calendars:l,month:i,year:d,modelValue:c,time:v,disabledTimesConfig:f,today:T,validateTime:m,getCalendarDays:M,getMarker:_,handleArrow:E,handleScroll:L,handleSwipe:h,selectDate:A,updateMonthYear:p,presetDate:x,selectCurrentDate:U,updateTime:G,assignMonthAndYear:Q,setStartTime:g}=Ws(o,a,V,k),R=e.useSlots(),{setHoverDate:O,getDayClassData:W,clearHoverDate:le}=ui(c,o),{defaultedMultiCalendars:ue}=Ce(o),y=e.ref([]),z=e.ref([]),J=e.ref(null),w=Ue(R,"calendar"),H=Ue(R,"monthYear"),$=Ue(R,"timePicker"),te=ee=>{o.shadow||a("mount",ee)};e.watch(l,()=>{o.shadow||setTimeout(()=>{a("recalculate-position")},0)},{deep:!0}),e.watch(ue,(ee,u)=>{ee.count-u.count>0&&Q()},{deep:!0});const S=e.computed(()=>ee=>M(i.value(ee),d.value(ee)).map(u=>({...u,days:u.days.map(I=>(I.marker=_(I),I.classData=W(I),I))})));function V(ee){var u;ee||ee===0?(u=z.value[ee])==null||u.triggerTransition(i.value(ee),d.value(ee)):z.value.forEach((I,se)=>I.triggerTransition(i.value(se),d.value(se)))}function k(){a("update-flow-step")}const K=(ee,u=!1)=>{A(ee,u),o.spaceConfirm&&a("select-date")},ne=(ee,u,I=0)=>{var se;(se=y.value[I])==null||se.toggleMonthPicker(ee,u)},N=(ee,u,I=0)=>{var se;(se=y.value[I])==null||se.toggleYearPicker(ee,u)},X=(ee,u,I)=>{var se;(se=J.value)==null||se.toggleTimePicker(ee,u,I)},s=(ee,u)=>{var I;if(!o.range){const se=c.value?c.value:T,Y=u?new Date(u):se,ge=ee?Ke(Y,{weekStartsOn:1}):ga(Y,{weekStartsOn:1});A({value:ge,current:ye(Y)===i.value(0),text:"",classData:{}}),(I=document.getElementById(xn(ge)))==null||I.focus()}},C=ee=>{var u;(u=y.value[0])==null||u.handleMonthYearChange(ee,!0)},ae=ee=>{p(0,{month:i.value(0),year:d.value(0)+(ee?1:-1),fromNav:!0})},B=(ee,u)=>{ee===Ie.time&&a(`time-picker-${u?"open":"close"}`),a("overlay-toggle",{open:u,overlay:ee})},me=ee=>{a("overlay-toggle",{open:!1,overlay:ee}),a("focus-menu")};return r({clearHoverDate:le,presetDate:x,selectCurrentDate:U,toggleMonthPicker:ne,toggleYearPicker:N,toggleTimePicker:X,handleArrow:E,updateMonthYear:p,getSidebarProps:()=>({modelValue:c,month:i,year:d,time:v,updateTime:G,updateMonthYear:p,selectDate:A,presetDate:x}),changeMonth:C,changeYear:ae,selectWeekDate:s,setStartTime:g}),(ee,u)=>(e.openBlock(),e.createElementBlock(e.Fragment,null,[e.createVNode(an,{"multi-calendars":e.unref(ue).count,collapse:ee.collapse,"is-mobile":ee.isMobile},{default:e.withCtx(({instance:I,index:se})=>[ee.disableMonthYearSelect?e.createCommentVNode("",!0):(e.openBlock(),e.createBlock(_s,e.mergeProps({key:0,ref:Y=>{Y&&(y.value[se]=Y)},months:e.unref(_a)(ee.formatLocale,ee.locale,ee.monthNameFormat),years:e.unref(Cn)(ee.yearRange,ee.locale,ee.reverseYears),month:e.unref(i)(I),year:e.unref(d)(I),instance:I},ee.$props,{onMount:u[0]||(u[0]=Y=>te(e.unref(Mt).header)),onResetFlow:u[1]||(u[1]=Y=>ee.$emit("reset-flow")),onUpdateMonthYear:Y=>e.unref(p)(I,Y),onOverlayClosed:me,onOverlayOpened:u[2]||(u[2]=Y=>ee.$emit("overlay-toggle",{open:!0,overlay:Y}))}),e.createSlots({_:2},[e.renderList(e.unref(H),(Y,ge)=>({name:Y,fn:e.withCtx(he=>[e.renderSlot(ee.$slots,Y,e.normalizeProps(e.guardReactiveProps(he)))])}))]),1040,["months","years","month","year","instance","onUpdateMonthYear"])),e.createVNode(Hs,e.mergeProps({ref:Y=>{Y&&(z.value[se]=Y)},"mapped-dates":S.value(I),month:e.unref(i)(I),year:e.unref(d)(I),instance:I},ee.$props,{onSelectDate:Y=>e.unref(A)(Y,I!==1),onHandleSpace:Y=>K(Y,I!==1),onSetHoverDate:u[3]||(u[3]=Y=>e.unref(O)(Y)),onHandleScroll:Y=>e.unref(L)(Y,I),onHandleSwipe:Y=>e.unref(h)(Y,I),onMount:u[4]||(u[4]=Y=>te(e.unref(Mt).calendar)),onResetFlow:u[5]||(u[5]=Y=>ee.$emit("reset-flow")),onTooltipOpen:u[6]||(u[6]=Y=>ee.$emit("tooltip-open",Y)),onTooltipClose:u[7]||(u[7]=Y=>ee.$emit("tooltip-close",Y))}),e.createSlots({_:2},[e.renderList(e.unref(w),(Y,ge)=>({name:Y,fn:e.withCtx(he=>[e.renderSlot(ee.$slots,Y,e.normalizeProps(e.guardReactiveProps({...he})))])}))]),1040,["mapped-dates","month","year","instance","onSelectDate","onHandleSpace","onHandleScroll","onHandleSwipe"])]),_:3},8,["multi-calendars","collapse","is-mobile"]),ee.enableTimePicker?(e.openBlock(),e.createElementBlock("div",qs,[ee.$slots["time-picker"]?e.renderSlot(ee.$slots,"time-picker",e.normalizeProps(e.mergeProps({key:0},{time:e.unref(v),updateTime:e.unref(G)}))):(e.openBlock(),e.createBlock(lr,e.mergeProps({key:1,ref_key:"timePickerRef",ref:J},ee.$props,{hours:e.unref(v).hours,minutes:e.unref(v).minutes,seconds:e.unref(v).seconds,"internal-model-value":ee.internalModelValue,"disabled-times-config":e.unref(f),"validate-time":e.unref(m),onMount:u[8]||(u[8]=I=>te(e.unref(Mt).timePicker)),"onUpdate:hours":u[9]||(u[9]=I=>e.unref(G)(I)),"onUpdate:minutes":u[10]||(u[10]=I=>e.unref(G)(I,!1)),"onUpdate:seconds":u[11]||(u[11]=I=>e.unref(G)(I,!1,!0)),onResetFlow:u[12]||(u[12]=I=>ee.$emit("reset-flow")),onOverlayClosed:u[13]||(u[13]=I=>B(I,!1)),onOverlayOpened:u[14]||(u[14]=I=>B(I,!0)),onAmPmChange:u[15]||(u[15]=I=>ee.$emit("am-pm-change",I))}),e.createSlots({_:2},[e.renderList(e.unref($),(I,se)=>({name:I,fn:e.withCtx(Y=>[e.renderSlot(ee.$slots,I,e.normalizeProps(e.guardReactiveProps(Y)))])}))]),1040,["hours","minutes","seconds","internal-model-value","disabled-times-config","validate-time"]))])):e.createCommentVNode("",!0)],64))}}),js=(t,r)=>{const n=e.ref(),{defaultedMultiCalendars:a,defaultedConfig:o,defaultedHighlight:l,defaultedRange:i,propDates:d,defaultedFilters:c,defaultedMultiDates:v}=Ce(t),{modelValue:f,year:T,month:m,calendars:M}=Gt(t,r),{isDisabled:_}=pt(t),{selectYear:E,groupedYears:L,showYearPicker:h,isDisabled:A,toggleYearPicker:p,handleYearSelect:x,handleYear:U}=or({modelValue:f,multiCalendars:a,range:i,highlight:l,calendars:M,propDates:d,month:m,year:T,filters:c,props:t,emit:r}),G=(w,H)=>[w,H].map($=>nt($,"MMMM",{locale:t.formatLocale})).join("-"),Q=e.computed(()=>w=>f.value?Array.isArray(f.value)?f.value.some(H=>Aa(w,H)):Aa(f.value,w):!1),g=w=>{if(i.value.enabled){if(Array.isArray(f.value)){const H=pe(w,f.value[0])||pe(w,f.value[1]);return Wt(f.value,n.value,w)&&!H}return!1}return!1},R=(w,H)=>w.quarter===da(H)&&w.year===fe(H),O=w=>typeof l.value=="function"?l.value({quarter:da(w),year:fe(w)}):!!l.value.quarters.find(H=>R(H,w)),W=e.computed(()=>w=>{const H=ve(new Date,{year:T.value(w)});return Dr({start:Ft(H),end:ha(H)}).map($=>{const te=bt($),S=ya($),V=_($),k=g(te),K=O(te);return{text:G(te,S),value:te,active:Q.value(te),highlighted:K,disabled:V,isBetween:k}})}),le=w=>{Hn(w,f,v.value.limit),r("auto-apply",!0)},ue=w=>{f.value=Wn(f,w,r),rn(f.value,r,t.autoApply,t.modelAuto)},y=w=>{f.value=w,r("auto-apply")};return{defaultedConfig:o,defaultedMultiCalendars:a,groupedYears:L,year:T,isDisabled:A,quarters:W,showYearPicker:h,modelValue:f,setHoverDate:w=>{n.value=w},selectYear:E,selectQuarter:(w,H,$)=>{if(!$)return M.value[H].month=ye(ya(w)),v.value.enabled?le(w):i.value.enabled?ue(w):y(w)},toggleYearPicker:p,handleYearSelect:x,handleYear:U}},Qs={class:"dp--quarter-items"},Gs=["data-test-id","disabled","onClick","onMouseover"],Ks=e.defineComponent({compatConfig:{MODE:3},__name:"QuarterPicker",props:{...et},emits:["update:internal-model-value","reset-flow","overlay-closed","auto-apply","range-start","range-end","overlay-toggle","update-month-year"],setup(t,{expose:r,emit:n}){const a=n,o=t,l=e.useSlots(),i=Ue(l,"yearMode"),{defaultedMultiCalendars:d,defaultedConfig:c,groupedYears:v,year:f,isDisabled:T,quarters:m,modelValue:M,showYearPicker:_,setHoverDate:E,selectQuarter:L,toggleYearPicker:h,handleYearSelect:A,handleYear:p}=js(o,a);return r({getSidebarProps:()=>({modelValue:M,year:f,selectQuarter:L,handleYearSelect:A,handleYear:p})}),(U,G)=>(e.openBlock(),e.createBlock(an,{"multi-calendars":e.unref(d).count,collapse:U.collapse,stretch:"","is-mobile":U.isMobile},{default:e.withCtx(({instance:Q})=>[e.createElementVNode("div",{class:"dp-quarter-picker-wrap",style:e.normalizeStyle({minHeight:`${e.unref(c).modeHeight}px`})},[U.$slots["top-extra"]?e.renderSlot(U.$slots,"top-extra",{key:0,value:U.internalModelValue}):e.createCommentVNode("",!0),e.createElementVNode("div",null,[e.createVNode(nr,e.mergeProps(U.$props,{items:e.unref(v)(Q),instance:Q,"show-year-picker":e.unref(_)[Q],year:e.unref(f)(Q),"is-disabled":g=>e.unref(T)(Q,g),onHandleYear:g=>e.unref(p)(Q,g),onYearSelect:g=>e.unref(A)(g,Q),onToggleYearPicker:g=>e.unref(h)(Q,g==null?void 0:g.flow,g==null?void 0:g.show)}),e.createSlots({_:2},[e.renderList(e.unref(i),(g,R)=>({name:g,fn:e.withCtx(O=>[e.renderSlot(U.$slots,g,e.normalizeProps(e.guardReactiveProps(O)))])}))]),1040,["items","instance","show-year-picker","year","is-disabled","onHandleYear","onYearSelect","onToggleYearPicker"])]),e.createElementVNode("div",Qs,[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(e.unref(m)(Q),(g,R)=>(e.openBlock(),e.createElementBlock("div",{key:R},[e.createElementVNode("button",{type:"button",class:e.normalizeClass(["dp--qr-btn",{"dp--qr-btn-active":g.active,"dp--qr-btn-between":g.isBetween,"dp--qr-btn-disabled":g.disabled,"dp--highlighted":g.highlighted}]),"data-test-id":g.value,disabled:g.disabled,onClick:O=>e.unref(L)(g.value,Q,g.disabled),onMouseover:O=>e.unref(E)(g.value)},[U.$slots.quarter?e.renderSlot(U.$slots,"quarter",{key:0,value:g.value,text:g.text}):(e.openBlock(),e.createElementBlock(e.Fragment,{key:1},[e.createTextVNode(e.toDisplayString(g.text),1)],64))],42,Gs)]))),128))])],4)]),_:3},8,["multi-calendars","collapse","is-mobile"]))}}),ur=(t,r)=>{const n=e.ref(0);e.onMounted(()=>{a(),window.addEventListener("resize",a,{passive:!0})}),e.onUnmounted(()=>{window.removeEventListener("resize",a)});const a=()=>{n.value=window.document.documentElement.clientWidth};return{isMobile:e.computed(()=>n.value<=t.value.mobileBreakpoint&&!r?!0:void 0)}},Xs=["id","tabindex","role","aria-label"],Js={key:0,class:"dp--menu-load-container"},Zs={key:1,class:"dp--menu-header"},ei=["data-dp-mobile"],ti={key:0,class:"dp__sidebar_left"},ni=["data-dp-mobile"],ai=["data-test-id","data-dp-mobile","onClick","onKeydown"],ri={key:2,class:"dp__sidebar_right"},oi={key:3,class:"dp__action_extra"},cr=e.defineComponent({compatConfig:{MODE:3},__name:"DatepickerMenu",props:{...nn,shadow:{type:Boolean,default:!1},openOnTop:{type:Boolean,default:!1},internalModelValue:{type:[Date,Array],default:null},noOverlayFocus:{type:Boolean,default:!1},collapse:{type:Boolean,default:!1},getInputRect:{type:Function,default:()=>({})},isTextInputDate:{type:Boolean,default:!1}},emits:["close-picker","select-date","auto-apply","time-update","flow-step","update-month-year","invalid-select","update:internal-model-value","recalculate-position","invalid-fixed-range","tooltip-open","tooltip-close","time-picker-open","time-picker-close","am-pm-change","range-start","range-end","auto-apply-invalid","date-update","invalid-date","overlay-toggle","menu-blur"],setup(t,{expose:r,emit:n}){const a=n,o=t,l=e.ref(null),i=e.computed(()=>{const{openOnTop:D,...F}=o;return{...F,isMobile:E.value,flowStep:W.value,menuWrapRef:l.value}}),{setMenuFocused:d,setShiftKey:c,control:v}=Za(),f=e.useSlots(),{defaultedTextInput:T,defaultedInline:m,defaultedConfig:M,defaultedUI:_}=Ce(o),{isMobile:E}=ur(M,o.shadow),L=e.ref(null),h=e.ref(0),A=e.ref(null),p=e.ref(!1),x=e.ref(null),U=e.ref(!1);e.onMounted(()=>{if(!o.shadow){p.value=!0,G(),window.addEventListener("resize",G);const D=Ye(l);if(D&&!T.value.enabled&&!m.value.enabled&&(d(!0),H()),D){const F=Me=>{U.value=!0,M.value.allowPreventDefault&&Me.preventDefault(),ft(Me,M.value,!0)};D.addEventListener("pointerdown",F),D.addEventListener("mousedown",F)}}document.addEventListener("mousedown",he)}),e.onUnmounted(()=>{window.removeEventListener("resize",G),document.addEventListener("mousedown",he)});const G=()=>{const D=Ye(A);D&&(h.value=D.getBoundingClientRect().width)},{arrowRight:Q,arrowLeft:g,arrowDown:R,arrowUp:O}=gt(),{flowStep:W,updateFlowStep:le,childMount:ue,resetFlow:y,handleFlow:z}=ci(o,a,x),J=e.computed(()=>o.monthPicker?hs:o.yearPicker?ys:o.timePicker?$s:o.quarterPicker?Ks:Us),w=e.computed(()=>{var Me;if(M.value.arrowLeft)return M.value.arrowLeft;const D=(Me=l.value)==null?void 0:Me.getBoundingClientRect(),F=o.getInputRect();return(F==null?void 0:F.width)<(h==null?void 0:h.value)&&(F==null?void 0:F.left)<=((D==null?void 0:D.left)??0)?`${(F==null?void 0:F.width)/2}px`:(F==null?void 0:F.right)>=((D==null?void 0:D.right)??0)&&(F==null?void 0:F.width)<(h==null?void 0:h.value)?`${(h==null?void 0:h.value)-(F==null?void 0:F.width)/2}px`:"50%"}),H=()=>{const D=Ye(l);D&&D.focus({preventScroll:!0})},$=e.computed(()=>{var D;return((D=x.value)==null?void 0:D.getSidebarProps())||{}}),te=()=>{o.openOnTop&&a("recalculate-position")},S=Ue(f,"action"),V=e.computed(()=>o.monthPicker||o.yearPicker?Ue(f,"monthYear"):o.timePicker?Ue(f,"timePicker"):Ue(f,"shared")),k=e.computed(()=>o.openOnTop?"dp__arrow_bottom":"dp__arrow_top"),K=e.computed(()=>({dp__menu_disabled:o.disabled,dp__menu_readonly:o.readonly,"dp-menu-loading":o.loading})),ne=e.computed(()=>({dp__menu:!0,dp__menu_index:!m.value.enabled,dp__relative:m.value.enabled,..._.value.menu??{}})),N=D=>{ft(D,M.value,!0)},X=()=>{o.escClose&&a("close-picker")},s=D=>{if(o.arrowNavigation){if(D===Le.up)return O();if(D===Le.down)return R();if(D===Le.left)return g();if(D===Le.right)return Q()}else D===Le.left||D===Le.up?de("handleArrow",Le.left,0,D===Le.up):de("handleArrow",Le.right,0,D===Le.down)},C=D=>{c(D.shiftKey),!o.disableMonthYearSelect&&D.code===Te.tab&&D.target.classList.contains("dp__menu")&&v.value.shiftKeyInMenu&&(D.preventDefault(),ft(D,M.value,!0),a("close-picker"))},ae=()=>{H(),a("time-picker-close")},B=D=>{var F,Me,_e;(F=x.value)==null||F.toggleTimePicker(!1,!1),(Me=x.value)==null||Me.toggleMonthPicker(!1,!1,D),(_e=x.value)==null||_e.toggleYearPicker(!1,!1,D)},me=(D,F=0)=>{var Me,_e,ut;return D==="month"?(Me=x.value)==null?void 0:Me.toggleMonthPicker(!1,!0,F):D==="year"?(_e=x.value)==null?void 0:_e.toggleYearPicker(!1,!0,F):D==="time"?(ut=x.value)==null?void 0:ut.toggleTimePicker(!0,!1):B(F)},de=(D,...F)=>{var Me,_e;(Me=x.value)!=null&&Me[D]&&((_e=x.value)==null||_e[D](...F))},ee=()=>{de("selectCurrentDate")},u=(D,F)=>{de("presetDate",e.toValue(D),F)},I=()=>{de("clearHoverDate")},se=(D,F)=>{de("updateMonthYear",D,F)},Y=(D,F)=>{D.preventDefault(),s(F)},ge=D=>{var F,Me,_e;if(C(D),D.key===Te.home||D.key===Te.end)return de("selectWeekDate",D.key===Te.home,D.target.getAttribute("id"));switch((D.key===Te.pageUp||D.key===Te.pageDown)&&(D.shiftKey?(de("changeYear",D.key===Te.pageUp),(F=Sn(l.value,"overlay-year"))==null||F.focus()):(de("changeMonth",D.key===Te.pageUp),(Me=Sn(l.value,D.key===Te.pageUp?"action-prev":"action-next"))==null||Me.focus()),D.target.getAttribute("id")&&((_e=l.value)==null||_e.focus({preventScroll:!0}))),D.key){case Te.esc:return X();case Te.arrowLeft:return Y(D,Le.left);case Te.arrowRight:return Y(D,Le.right);case Te.arrowUp:return Y(D,Le.up);case Te.arrowDown:return Y(D,Le.down);default:return}},he=D=>{var F;m.value.enabled&&!m.value.input&&!((F=l.value)!=null&&F.contains(D.target))&&U.value&&(U.value=!1,a("menu-blur"))};return r({updateMonthYear:se,switchView:me,handleFlow:z,onValueCleared:()=>{var D,F;(F=(D=x.value)==null?void 0:D.setStartTime)==null||F.call(D)}}),(D,F)=>{var Me,_e,ut;return e.openBlock(),e.createElementBlock("div",{id:D.uid?`dp-menu-${D.uid}`:void 0,ref_key:"dpMenuRef",ref:l,tabindex:e.unref(m).enabled?void 0:"0",role:e.unref(m).enabled?void 0:"dialog","aria-label":(Me=D.ariaLabels)==null?void 0:Me.menu,class:e.normalizeClass(ne.value),style:e.normalizeStyle({"--dp-arrow-left":w.value}),onMouseleave:I,onClick:N,onKeydown:ge},[(D.disabled||D.readonly)&&e.unref(m).enabled||D.loading?(e.openBlock(),e.createElementBlock("div",{key:0,class:e.normalizeClass(K.value)},[D.loading?(e.openBlock(),e.createElementBlock("div",Js,F[19]||(F[19]=[e.createElementVNode("span",{class:"dp--menu-loader"},null,-1)]))):e.createCommentVNode("",!0)],2)):e.createCommentVNode("",!0),D.$slots["menu-header"]?(e.openBlock(),e.createElementBlock("div",Zs,[e.renderSlot(D.$slots,"menu-header")])):e.createCommentVNode("",!0),!e.unref(m).enabled&&!D.teleportCenter?(e.openBlock(),e.createElementBlock("div",{key:2,class:e.normalizeClass(k.value)},null,2)):e.createCommentVNode("",!0),e.createElementVNode("div",{ref_key:"innerMenuRef",ref:A,class:e.normalizeClass({dp__menu_content_wrapper:((_e=D.presetDates)==null?void 0:_e.length)||!!D.$slots["left-sidebar"]||!!D.$slots["right-sidebar"],"dp--menu-content-wrapper-collapsed":t.collapse&&(((ut=D.presetDates)==null?void 0:ut.length)||!!D.$slots["left-sidebar"]||!!D.$slots["right-sidebar"])}),"data-dp-mobile":e.unref(E),style:e.normalizeStyle({"--dp-menu-width":`${h.value}px`})},[D.$slots["left-sidebar"]?(e.openBlock(),e.createElementBlock("div",ti,[e.renderSlot(D.$slots,"left-sidebar",e.normalizeProps(e.guardReactiveProps($.value)))])):e.createCommentVNode("",!0),D.presetDates.length?(e.openBlock(),e.createElementBlock("div",{key:1,class:e.normalizeClass({"dp--preset-dates-collapsed":t.collapse,"dp--preset-dates":!0}),"data-dp-mobile":e.unref(E)},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(D.presetDates,(ie,Ve)=>(e.openBlock(),e.createElementBlock(e.Fragment,{key:Ve},[ie.slot?e.renderSlot(D.$slots,ie.slot,{key:0,presetDate:u,label:ie.label,value:ie.value}):(e.openBlock(),e.createElementBlock("button",{key:1,type:"button",style:e.normalizeStyle(ie.style||{}),class:e.normalizeClass(["dp__btn dp--preset-range",{"dp--preset-range-collapsed":t.collapse}]),"data-test-id":ie.testId??void 0,"data-dp-mobile":e.unref(E),onClick:e.withModifiers(Qe=>u(ie.value,ie.noTz),["prevent"]),onKeydown:Qe=>e.unref(ze)(Qe,()=>u(ie.value,ie.noTz),!0)},e.toDisplayString(ie.label),47,ai))],64))),128))],10,ni)):e.createCommentVNode("",!0),e.createElementVNode("div",{ref_key:"calendarWrapperRef",ref:L,class:"dp__instance_calendar",role:"document"},[(e.openBlock(),e.createBlock(e.resolveDynamicComponent(J.value),e.mergeProps({ref_key:"dynCmpRef",ref:x},i.value,{"flow-step":e.unref(W),onMount:e.unref(ue),onUpdateFlowStep:e.unref(le),onResetFlow:e.unref(y),onFocusMenu:H,onSelectDate:F[0]||(F[0]=ie=>D.$emit("select-date")),onDateUpdate:F[1]||(F[1]=ie=>D.$emit("date-update",ie)),onTooltipOpen:F[2]||(F[2]=ie=>D.$emit("tooltip-open",ie)),onTooltipClose:F[3]||(F[3]=ie=>D.$emit("tooltip-close",ie)),onAutoApply:F[4]||(F[4]=ie=>D.$emit("auto-apply",ie)),onRangeStart:F[5]||(F[5]=ie=>D.$emit("range-start",ie)),onRangeEnd:F[6]||(F[6]=ie=>D.$emit("range-end",ie)),onInvalidFixedRange:F[7]||(F[7]=ie=>D.$emit("invalid-fixed-range",ie)),onTimeUpdate:F[8]||(F[8]=ie=>D.$emit("time-update")),onAmPmChange:F[9]||(F[9]=ie=>D.$emit("am-pm-change",ie)),onTimePickerOpen:F[10]||(F[10]=ie=>D.$emit("time-picker-open",ie)),onTimePickerClose:ae,onRecalculatePosition:te,onUpdateMonthYear:F[11]||(F[11]=ie=>D.$emit("update-month-year",ie)),onAutoApplyInvalid:F[12]||(F[12]=ie=>D.$emit("auto-apply-invalid",ie)),onInvalidDate:F[13]||(F[13]=ie=>D.$emit("invalid-date",ie)),onOverlayToggle:F[14]||(F[14]=ie=>D.$emit("overlay-toggle",ie)),"onUpdate:internalModelValue":F[15]||(F[15]=ie=>D.$emit("update:internal-model-value",ie))}),e.createSlots({_:2},[e.renderList(V.value,(ie,Ve)=>({name:ie,fn:e.withCtx(Qe=>[e.renderSlot(D.$slots,ie,e.normalizeProps(e.guardReactiveProps({...Qe})))])}))]),1040,["flow-step","onMount","onUpdateFlowStep","onResetFlow"]))],512),D.$slots["right-sidebar"]?(e.openBlock(),e.createElementBlock("div",ri,[e.renderSlot(D.$slots,"right-sidebar",e.normalizeProps(e.guardReactiveProps($.value)))])):e.createCommentVNode("",!0),D.$slots["action-extra"]?(e.openBlock(),e.createElementBlock("div",oi,[D.$slots["action-extra"]?e.renderSlot(D.$slots,"action-extra",{key:0,selectCurrentDate:ee}):e.createCommentVNode("",!0)])):e.createCommentVNode("",!0)],14,ei),!D.autoApply||e.unref(M).keepActionRow?(e.openBlock(),e.createBlock(os,e.mergeProps({key:3,"menu-mount":p.value},i.value,{"calendar-width":h.value,onClosePicker:F[16]||(F[16]=ie=>D.$emit("close-picker")),onSelectDate:F[17]||(F[17]=ie=>D.$emit("select-date")),onInvalidSelect:F[18]||(F[18]=ie=>D.$emit("invalid-select")),onSelectNow:ee}),e.createSlots({_:2},[e.renderList(e.unref(S),(ie,Ve)=>({name:ie,fn:e.withCtx(Qe=>[e.renderSlot(D.$slots,ie,e.normalizeProps(e.guardReactiveProps({...Qe})))])}))]),1040,["menu-mount","calendar-width"])):e.createCommentVNode("",!0)],46,Xs)}}});var _t=(t=>(t.center="center",t.left="left",t.right="right",t))(_t||{});const li=({menuRef:t,menuRefInner:r,inputRef:n,pickerWrapperRef:a,inline:o,emit:l,props:i,slots:d})=>{const{defaultedConfig:c}=Ce(i),v=e.ref({}),f=e.ref(!1),T=e.ref({top:"0",left:"0"}),m=e.ref(!1),M=e.toRef(i,"teleportCenter");e.watch(M,()=>{T.value=JSON.parse(JSON.stringify({})),U()});const _=w=>{if(i.teleport){const H=w.getBoundingClientRect();return{left:H.left+window.scrollX,top:H.top+window.scrollY}}return{top:0,left:0}},E=(w,H)=>{T.value.left=`${w+H-v.value.width}px`},L=w=>{T.value.left=`${w}px`},h=(w,H)=>{i.position===_t.left&&L(w),i.position===_t.right&&E(w,H),i.position===_t.center&&(T.value.left=`${w+H/2-v.value.width/2}px`)},A=w=>{const{width:H,height:$}=w.getBoundingClientRect(),{top:te,left:S}=_(w);return{top:+te,left:+S,width:H,height:$}},p=()=>{T.value.left="50%",T.value.top="50%",T.value.transform="translate(-50%, -50%)",T.value.position="fixed",delete T.value.opacity},x=()=>{const w=Ye(n);T.value=i.altPosition(w)},U=(w=!0)=>{var H;if(!o.value.enabled){if(M.value)return p();if(i.altPosition!==null)return x();if(w){const $=i.teleport?(H=r.value)==null?void 0:H.$el:t.value;$&&(v.value=$.getBoundingClientRect()),l("recalculate-position")}return le()}},G=({inputEl:w,left:H,width:$})=>{window.screen.width>768&&!f.value&&h(H,$),R(w)},Q=w=>{const{top:H,left:$,height:te,width:S}=A(w);T.value.top=`${te+H+ +i.offset}px`,m.value=!1,f.value||(T.value.left=`${$+S/2-v.value.width/2}px`),G({inputEl:w,left:$,width:S})},g=w=>{const{top:H,left:$,width:te}=A(w);T.value.top=`${H-+i.offset-v.value.height}px`,m.value=!0,G({inputEl:w,left:$,width:te})},R=w=>{if(i.autoPosition){const{left:H,width:$}=A(w),{left:te,right:S}=v.value;if(!f.value){if(Math.abs(te)!==Math.abs(S)){if(te<=0)return f.value=!0,L(H);if(S>=document.documentElement.clientWidth)return f.value=!0,E(H,$)}return h(H,$)}}},O=()=>{const w=Ye(n);if(w){if(i.autoPosition===Je.top)return Je.top;if(i.autoPosition===Je.bottom)return Je.bottom;const{height:H}=v.value,{top:$,height:te}=w.getBoundingClientRect(),V=window.innerHeight-$-te,k=$;return H<=V?Je.bottom:H>V&&H<=k?Je.top:V>=k?Je.bottom:Je.top}return Je.bottom},W=w=>O()===Je.bottom?Q(w):g(w),le=()=>{const w=Ye(n);if(w)return i.autoPosition?W(w):Q(w)},ue=function(w){if(w){const H=w.scrollHeight>w.clientHeight,te=window.getComputedStyle(w).overflowY.indexOf("hidden")!==-1;return H&&!te}return!0},y=function(w){return!w||w===document.body||w.nodeType===Node.DOCUMENT_FRAGMENT_NODE?window:ue(w)?w:y(w.assignedSlot&&c.value.shadowDom?w.assignedSlot.parentNode:w.parentNode)},z=w=>{if(w)switch(i.position){case _t.left:return{left:0,transform:"translateX(0)"};case _t.right:return{left:`${w.width}px`,transform:"translateX(-100%)"};default:return{left:`${w.width/2}px`,transform:"translateX(-50%)"}}return{}};return{openOnTop:m,menuStyle:T,xCorrect:f,setMenuPosition:U,getScrollableParent:y,shadowRender:(w,H)=>{var ne,N,X;const $=document.createElement("div"),te=(ne=Ye(n))==null?void 0:ne.getBoundingClientRect();$.setAttribute("id","dp--temp-container");const S=(N=a.value)!=null&&N.clientWidth?a.value:document.body;S.append($);const V=z(te),k=c.value.shadowDom?Object.keys(d).filter(s=>["right-sidebar","left-sidebar","top-extra","action-extra"].includes(s)):Object.keys(d),K=e.h(w,{...H,shadow:!0,style:{opacity:0,position:"absolute",...V}},Object.fromEntries(k.map(s=>[s,d[s]])));e.render(K,$),v.value=(X=K.el)==null?void 0:X.getBoundingClientRect(),e.render(null,$),S.removeChild($)}}},yt=[{name:"clock-icon",use:["time","calendar","shared"]},{name:"arrow-left",use:["month-year","calendar","shared","year-mode"]},{name:"arrow-right",use:["month-year","calendar","shared","year-mode"]},{name:"arrow-up",use:["time","calendar","month-year","shared"]},{name:"arrow-down",use:["time","calendar","month-year","shared"]},{name:"calendar-icon",use:["month-year","time","calendar","shared","year-mode"]},{name:"day",use:["calendar","shared"]},{name:"month-overlay-value",use:["calendar","month-year","shared"]},{name:"year-overlay-value",use:["calendar","month-year","shared","year-mode"]},{name:"year-overlay",use:["month-year","shared"]},{name:"month-overlay",use:["month-year","shared"]},{name:"month-overlay-header",use:["month-year","shared"]},{name:"year-overlay-header",use:["month-year","shared"]},{name:"hours-overlay-value",use:["calendar","time","shared"]},{name:"hours-overlay-header",use:["calendar","time","shared"]},{name:"minutes-overlay-value",use:["calendar","time","shared"]},{name:"minutes-overlay-header",use:["calendar","time","shared"]},{name:"seconds-overlay-value",use:["calendar","time","shared"]},{name:"seconds-overlay-header",use:["calendar","time","shared"]},{name:"hours",use:["calendar","time","shared"]},{name:"minutes",use:["calendar","time","shared"]},{name:"month",use:["calendar","month-year","shared"]},{name:"year",use:["calendar","month-year","shared","year-mode"]},{name:"action-buttons",use:["action"]},{name:"action-preview",use:["action"]},{name:"calendar-header",use:["calendar","shared"]},{name:"marker-tooltip",use:["calendar","shared"]},{name:"action-extra",use:["menu"]},{name:"time-picker-overlay",use:["calendar","time","shared"]},{name:"am-pm-button",use:["calendar","time","shared"]},{name:"left-sidebar",use:["menu"]},{name:"right-sidebar",use:["menu"]},{name:"month-year",use:["month-year","shared"]},{name:"time-picker",use:["menu","shared"]},{name:"action-row",use:["action"]},{name:"marker",use:["calendar","shared"]},{name:"quarter",use:["shared"]},{name:"top-extra",use:["shared","month-year"]},{name:"tp-inline-arrow-up",use:["shared","time"]},{name:"tp-inline-arrow-down",use:["shared","time"]},{name:"menu-header",use:["menu"]}],si=[{name:"trigger"},{name:"input-icon"},{name:"clear-icon"},{name:"dp-input"}],ii={all:()=>yt,monthYear:()=>yt.filter(t=>t.use.includes("month-year")),input:()=>si,timePicker:()=>yt.filter(t=>t.use.includes("time")),action:()=>yt.filter(t=>t.use.includes("action")),calendar:()=>yt.filter(t=>t.use.includes("calendar")),menu:()=>yt.filter(t=>t.use.includes("menu")),shared:()=>yt.filter(t=>t.use.includes("shared")),yearMode:()=>yt.filter(t=>t.use.includes("year-mode"))},Ue=(t,r,n)=>{const a=[];return ii[r]().forEach(o=>{t[o.name]&&a.push(o.name)}),n!=null&&n.length&&n.forEach(o=>{o.slot&&a.push(o.slot)}),a},Qt=t=>{const r=e.computed(()=>a=>t.value?a?t.value.open:t.value.close:""),n=e.computed(()=>a=>t.value?a?t.value.menuAppearTop:t.value.menuAppearBottom:"");return{transitionName:r,showTransition:!!t.value,menuTransition:n}},Gt=(t,r,n)=>{const{defaultedRange:a,defaultedTz:o}=Ce(t),l=q(qe(q(),o.value.timezone)),i=e.ref([{month:ye(l),year:fe(l)}]),d=m=>{const M={hours:lt(l),minutes:dt(l),seconds:0};return a.value.enabled?[M[m],M[m]]:M[m]},c=e.reactive({hours:d("hours"),minutes:d("minutes"),seconds:d("seconds")});e.watch(a,(m,M)=>{m.enabled!==M.enabled&&(c.hours=d("hours"),c.minutes=d("minutes"),c.seconds=d("seconds"))},{deep:!0});const v=e.computed({get:()=>t.internalModelValue,set:m=>{!t.readonly&&!t.disabled&&r("update:internal-model-value",m)}}),f=e.computed(()=>m=>i.value[m]?i.value[m].month:0),T=e.computed(()=>m=>i.value[m]?i.value[m].year:0);return e.watch(v,(m,M)=>{n&&JSON.stringify(m??{})!==JSON.stringify(M??{})&&n()},{deep:!0}),{calendars:i,time:c,modelValue:v,month:f,year:T,today:l}},ui=(t,r)=>{const{defaultedMultiCalendars:n,defaultedMultiDates:a,defaultedUI:o,defaultedHighlight:l,defaultedTz:i,propDates:d,defaultedRange:c}=Ce(r),{isDisabled:v}=pt(r),f=e.ref(null),T=e.ref(qe(new Date,i.value.timezone)),m=s=>{!s.current&&r.hideOffsetDates||(f.value=s.value)},M=()=>{f.value=null},_=s=>Array.isArray(t.value)&&c.value.enabled&&t.value[0]&&f.value?s?Ne(f.value,t.value[0]):Ae(f.value,t.value[0]):!0,E=(s,C)=>{const ae=()=>t.value?C?t.value[0]||null:t.value[1]:null,B=t.value&&Array.isArray(t.value)?ae():null;return pe(q(s.value),B)},L=s=>{const C=Array.isArray(t.value)?t.value[0]:null;return s?!Ae(f.value??null,C):!0},h=(s,C=!0)=>(c.value.enabled||r.weekPicker)&&Array.isArray(t.value)&&t.value.length===2?r.hideOffsetDates&&!s.current?!1:pe(q(s.value),t.value[C?0:1]):c.value.enabled?E(s,C)&&L(C)||pe(s.value,Array.isArray(t.value)?t.value[0]:null)&&_(C):!1,A=(s,C)=>{if(Array.isArray(t.value)&&t.value[0]&&t.value.length===1){const ae=pe(s.value,f.value);return C?Ne(t.value[0],s.value)&&ae:Ae(t.value[0],s.value)&&ae}return!1},p=s=>!t.value||r.hideOffsetDates&&!s.current?!1:c.value.enabled?r.modelAuto&&Array.isArray(t.value)?pe(s.value,t.value[0]?t.value[0]:T.value):!1:a.value.enabled&&Array.isArray(t.value)?t.value.some(C=>pe(C,s.value)):pe(s.value,t.value?t.value:T.value),x=s=>{if(c.value.autoRange||r.weekPicker){if(f.value){if(r.hideOffsetDates&&!s.current)return!1;const C=je(f.value,+c.value.autoRange),ae=st(q(f.value),r.weekStart);return r.weekPicker?pe(ae[1],q(s.value)):pe(C,q(s.value))}return!1}return!1},U=s=>{if(c.value.autoRange||r.weekPicker){if(f.value){const C=je(f.value,+c.value.autoRange);if(r.hideOffsetDates&&!s.current)return!1;const ae=st(q(f.value),r.weekStart);return r.weekPicker?Ne(s.value,ae[0])&&Ae(s.value,ae[1]):Ne(s.value,f.value)&&Ae(s.value,C)}return!1}return!1},G=s=>{if(c.value.autoRange||r.weekPicker){if(f.value){if(r.hideOffsetDates&&!s.current)return!1;const C=st(q(f.value),r.weekStart);return r.weekPicker?pe(C[0],s.value):pe(f.value,s.value)}return!1}return!1},Q=s=>Wt(t.value,f.value,s.value),g=()=>r.modelAuto&&Array.isArray(r.internalModelValue)?!!r.internalModelValue[0]:!1,R=()=>r.modelAuto?xa(r.internalModelValue):!0,O=s=>{if(r.weekPicker)return!1;const C=c.value.enabled?!h(s)&&!h(s,!1):!0;return!v(s.value)&&!p(s)&&!(!s.current&&r.hideOffsetDates)&&C},W=s=>c.value.enabled?r.modelAuto?g()&&p(s):!1:p(s),le=s=>l.value?Bl(s.value,d.value.highlight):!1,ue=s=>{const C=v(s.value);return C&&(typeof l.value=="function"?!l.value(s.value,C):!l.value.options.highlightDisabled)},y=s=>{var C;return typeof l.value=="function"?l.value(s.value):(C=l.value.weekdays)==null?void 0:C.includes(s.value.getDay())},z=s=>(c.value.enabled||r.weekPicker)&&(!(n.value.count>0)||s.current)&&R()&&!(!s.current&&r.hideOffsetDates)&&!p(s)?Q(s):!1,J=s=>{if(Array.isArray(t.value)&&t.value.length===1){const{before:C,after:ae}=Ja(+c.value.maxRange,t.value[0]);return At(s.value,C)||Dt(s.value,ae)}return!1},w=s=>{if(Array.isArray(t.value)&&t.value.length===1){const{before:C,after:ae}=Ja(+c.value.minRange,t.value[0]);return Wt([C,ae],t.value[0],s.value)}return!1},H=s=>c.value.enabled&&(c.value.maxRange||c.value.minRange)?c.value.maxRange&&c.value.minRange?J(s)||w(s):c.value.maxRange?J(s):w(s):!1,$=s=>{const{isRangeStart:C,isRangeEnd:ae}=k(s),B=c.value.enabled?C||ae:!1;return{dp__cell_offset:!s.current,dp__pointer:!r.disabled&&!(!s.current&&r.hideOffsetDates)&&!v(s.value)&&!H(s),dp__cell_disabled:v(s.value)||H(s),dp__cell_highlight:!ue(s)&&(le(s)||y(s))&&!W(s)&&!B&&!G(s)&&!(z(s)&&r.weekPicker)&&!ae,dp__cell_highlight_active:!ue(s)&&(le(s)||y(s))&&W(s),dp__today:!r.noToday&&pe(s.value,T.value)&&s.current,"dp--past":Ae(s.value,T.value),"dp--future":Ne(s.value,T.value)}},te=s=>({dp__active_date:W(s),dp__date_hover:O(s)}),S=s=>{if(t.value&&!Array.isArray(t.value)){const C=st(t.value,r.weekStart);return{...ne(s),dp__range_start:pe(C[0],s.value),dp__range_end:pe(C[1],s.value),dp__range_between_week:Ne(s.value,C[0])&&Ae(s.value,C[1])}}return{...ne(s)}},V=s=>{if(t.value&&Array.isArray(t.value)){const C=st(t.value[0],r.weekStart),ae=t.value[1]?st(t.value[1],r.weekStart):[];return{...ne(s),dp__range_start:pe(C[0],s.value)||pe(ae[0],s.value),dp__range_end:pe(C[1],s.value)||pe(ae[1],s.value),dp__range_between_week:Ne(s.value,C[0])&&Ae(s.value,C[1])||Ne(s.value,ae[0])&&Ae(s.value,ae[1]),dp__range_between:Ne(s.value,C[1])&&Ae(s.value,ae[0])}}return{...ne(s)}},k=s=>{const C=n.value.count>0?s.current&&h(s)&&R():h(s)&&R(),ae=n.value.count>0?s.current&&h(s,!1)&&R():h(s,!1)&&R();return{isRangeStart:C,isRangeEnd:ae}},K=s=>{const{isRangeStart:C,isRangeEnd:ae}=k(s);return{dp__range_start:C,dp__range_end:ae,dp__range_between:z(s),dp__date_hover:pe(s.value,f.value)&&!C&&!ae&&!r.weekPicker,dp__date_hover_start:A(s,!0),dp__date_hover_end:A(s,!1)}},ne=s=>({...K(s),dp__cell_auto_range:U(s),dp__cell_auto_range_start:G(s),dp__cell_auto_range_end:x(s)}),N=s=>c.value.enabled?c.value.autoRange?ne(s):r.modelAuto?{...te(s),...K(s)}:r.weekPicker?V(s):K(s):r.weekPicker?S(s):te(s);return{setHoverDate:m,clearHoverDate:M,getDayClassData:s=>r.hideOffsetDates&&!s.current?{}:{...$(s),...N(s),[r.dayClass?r.dayClass(s.value,r.internalModelValue):""]:!0,...o.value.calendarCell??{}}}},pt=t=>{const{defaultedFilters:r,defaultedRange:n,propDates:a,defaultedMultiDates:o}=Ce(t),l=y=>a.value.disabledDates?typeof a.value.disabledDates=="function"?a.value.disabledDates(q(y)):!!Zt(y,a.value.disabledDates):!1,i=y=>a.value.maxDate?t.yearPicker?fe(y)>fe(a.value.maxDate):Ne(y,a.value.maxDate):!1,d=y=>a.value.minDate?t.yearPicker?fe(y)<fe(a.value.minDate):Ae(y,a.value.minDate):!1,c=y=>{const z=i(y),J=d(y),w=l(y),$=r.value.months.map(K=>+K).includes(ye(y)),te=t.disabledWeekDays.length?t.disabledWeekDays.some(K=>+K===ho(y)):!1,S=M(y),V=fe(y),k=V<+t.yearRange[0]||V>+t.yearRange[1];return!(z||J||w||$||k||te||S)},v=(y,z)=>Ae(...ht(a.value.minDate,y,z))||pe(...ht(a.value.minDate,y,z)),f=(y,z)=>Ne(...ht(a.value.maxDate,y,z))||pe(...ht(a.value.maxDate,y,z)),T=(y,z,J)=>{let w=!1;return a.value.maxDate&&J&&f(y,z)&&(w=!0),a.value.minDate&&!J&&v(y,z)&&(w=!0),w},m=(y,z,J,w)=>{let H=!1;return w&&(a.value.minDate||a.value.maxDate)?a.value.minDate&&a.value.maxDate?H=T(y,z,J):(a.value.minDate&&v(y,z)||a.value.maxDate&&f(y,z))&&(H=!0):H=!0,H},M=y=>Array.isArray(a.value.allowedDates)&&!a.value.allowedDates.length?!0:a.value.allowedDates?!Zt(y,a.value.allowedDates):!1,_=y=>!c(y),E=y=>n.value.noDisabledRange?!ma({start:y[0],end:y[1]}).some(J=>_(J)):!0,L=y=>{if(y){const z=fe(y);return z>=+t.yearRange[0]&&z<=t.yearRange[1]}return!0},h=(y,z)=>!!(Array.isArray(y)&&y[z]&&(n.value.maxRange||n.value.minRange)&&L(y[z])),A=(y,z,J=0)=>{if(h(z,J)&&L(y)){const w=ia(y,z[J]),H=Ua(z[J],y),$=H.length===1?0:H.filter(S=>_(S)).length,te=Math.abs(w)-(n.value.minMaxRawRange?0:$);if(n.value.minRange&&n.value.maxRange)return te>=+n.value.minRange&&te<=+n.value.maxRange;if(n.value.minRange)return te>=+n.value.minRange;if(n.value.maxRange)return te<=+n.value.maxRange}return!0},p=()=>!t.enableTimePicker||t.monthPicker||t.yearPicker||t.ignoreTimeValidation,x=y=>Array.isArray(y)?[y[0]?Yn(y[0]):null,y[1]?Yn(y[1]):null]:Yn(y),U=(y,z,J)=>y.find(w=>+w.hours===lt(z)&&w.minutes==="*"?!0:+w.minutes===dt(z)&&+w.hours===lt(z))&&J,G=(y,z,J)=>{const[w,H]=y,[$,te]=z;return!U(w,$,J)&&!U(H,te,J)&&J},Q=(y,z)=>{const J=Array.isArray(z)?z:[z];return Array.isArray(t.disabledTimes)?Array.isArray(t.disabledTimes[0])?G(t.disabledTimes,J,y):!J.some(w=>U(t.disabledTimes,w,y)):y},g=(y,z)=>{const J=Array.isArray(z)?[Tt(z[0]),z[1]?Tt(z[1]):void 0]:Tt(z),w=!t.disabledTimes(J);return y&&w},R=(y,z)=>t.disabledTimes?Array.isArray(t.disabledTimes)?Q(z,y):g(z,y):z,O=y=>{let z=!0;if(!y||p())return!0;const J=!a.value.minDate&&!a.value.maxDate?x(y):y;return(t.maxTime||a.value.maxDate)&&(z=Ka(t.maxTime,a.value.maxDate,"max",Re(J),z)),(t.minTime||a.value.minDate)&&(z=Ka(t.minTime,a.value.minDate,"min",Re(J),z)),R(y,z)},W=y=>{if(!t.monthPicker)return!0;let z=!0;const J=q(Ze(y));if(a.value.minDate&&a.value.maxDate){const w=q(Ze(a.value.minDate)),H=q(Ze(a.value.maxDate));return Ne(J,w)&&Ae(J,H)||pe(J,w)||pe(J,H)}if(a.value.minDate){const w=q(Ze(a.value.minDate));z=Ne(J,w)||pe(J,w)}if(a.value.maxDate){const w=q(Ze(a.value.maxDate));z=Ae(J,w)||pe(J,w)}return z},le=e.computed(()=>y=>!t.enableTimePicker||t.ignoreTimeValidation?!0:O(y)),ue=e.computed(()=>y=>t.monthPicker?Array.isArray(y)&&(n.value.enabled||o.value.enabled)?!y.filter(J=>!W(J)).length:W(y):!0);return{isDisabled:_,validateDate:c,validateMonthYearInRange:m,isDateRangeAllowed:E,checkMinMaxRange:A,isValidTime:O,isTimeValid:le,isMonthValid:ue}},on=()=>{const t=e.computed(()=>(a,o)=>a==null?void 0:a.includes(o)),r=e.computed(()=>(a,o)=>a.count?a.solo?!0:o===0:!0),n=e.computed(()=>(a,o)=>a.count?a.solo?!0:o===a.count-1:!0);return{hideNavigationButtons:t,showLeftIcon:r,showRightIcon:n}},ci=(t,r,n)=>{const a=e.ref(0),o=e.reactive({[Mt.timePicker]:!t.enableTimePicker||t.timePicker||t.monthPicker,[Mt.calendar]:!1,[Mt.header]:!1}),l=e.computed(()=>t.monthPicker||t.timePicker),i=T=>{var m;if((m=t.flow)!=null&&m.length){if(!T&&l.value)return f();o[T]=!0,Object.keys(o).filter(M=>!o[M]).length||f()}},d=()=>{var T,m;(T=t.flow)!=null&&T.length&&a.value!==-1&&(a.value+=1,r("flow-step",a.value),f()),((m=t.flow)==null?void 0:m.length)===a.value&&e.nextTick().then(()=>c())},c=()=>{a.value=-1},v=(T,m,...M)=>{var _,E;t.flow[a.value]===T&&n.value&&((E=(_=n.value)[m])==null||E.call(_,...M))},f=(T=0)=>{T&&(a.value+=T),v(Ie.month,"toggleMonthPicker",!0),v(Ie.year,"toggleYearPicker",!0),v(Ie.calendar,"toggleTimePicker",!1,!0),v(Ie.time,"toggleTimePicker",!0,!0);const m=t.flow[a.value];(m===Ie.hours||m===Ie.minutes||m===Ie.seconds)&&v(m,"toggleTimePicker",!0,!0,m)};return{childMount:i,updateFlowStep:d,resetFlow:c,handleFlow:f,flowStep:a}},di={key:1,class:"dp__input_wrap"},fi=["id","name","inputmode","placeholder","disabled","readonly","required","value","autocomplete","aria-label","aria-disabled","aria-invalid"],mi={key:2,class:"dp--clear-btn"},hi=["aria-label"],gi=e.defineComponent({compatConfig:{MODE:3},__name:"DatepickerInput",props:{isMenuOpen:{type:Boolean,default:!1},inputValue:{type:String,default:""},...nn},emits:["clear","open","update:input-value","set-input-date","close","select-date","set-empty-date","toggle","focus-prev","focus","blur","real-blur","text-input"],setup(t,{expose:r,emit:n}){const a=n,o=t,{defaultedTextInput:l,defaultedAriaLabels:i,defaultedInline:d,defaultedConfig:c,defaultedRange:v,defaultedMultiDates:f,defaultedUI:T,getDefaultPattern:m,getDefaultStartTime:M}=Ce(o),{checkMinMaxRange:_}=pt(o),E=e.ref(),L=e.ref(null),h=e.ref(!1),A=e.ref(!1),p=e.computed(()=>({dp__pointer:!o.disabled&&!o.readonly&&!l.value.enabled,dp__disabled:o.disabled,dp__input_readonly:!l.value.enabled,dp__input:!0,dp__input_icon_pad:!o.hideInputIcon,dp__input_valid:typeof o.state=="boolean"?o.state:!1,dp__input_invalid:typeof o.state=="boolean"?!o.state:!1,dp__input_focus:h.value||o.isMenuOpen,dp__input_reg:!l.value.enabled,...T.value.input??{}})),x=()=>{a("set-input-date",null),o.clearable&&o.autoApply&&(a("set-empty-date"),E.value=null)},U=S=>{const V=M();return $l(S,l.value.format??m(),V??ja({},o.enableSeconds),o.inputValue,A.value,o.formatLocale)},G=S=>{const{rangeSeparator:V}=l.value,[k,K]=S.split(`${V}`);if(k){const ne=U(k.trim()),N=K?U(K.trim()):null;if(Dt(ne,N))return;const X=ne&&N?[ne,N]:[ne];_(N,X,0)&&(E.value=ne?X:null)}},Q=()=>{A.value=!0},g=S=>{if(v.value.enabled)G(S);else if(f.value.enabled){const V=S.split(";");E.value=V.map(k=>U(k.trim())).filter(k=>k)}else E.value=U(S)},R=S=>{var k;const V=typeof S=="string"?S:(k=S.target)==null?void 0:k.value;V!==""?(l.value.openMenu&&!o.isMenuOpen&&a("open"),g(V),a("set-input-date",E.value)):x(),A.value=!1,a("update:input-value",V),a("text-input",S,E.value)},O=S=>{l.value.enabled?(g(S.target.value),l.value.enterSubmit&&Nn(E.value)&&o.inputValue!==""?(a("set-input-date",E.value,!0),E.value=null):l.value.enterSubmit&&o.inputValue===""&&(E.value=null,a("clear"))):ue(S)},W=(S,V)=>{l.value.enabled&&l.value.tabSubmit&&!V&&g(S.target.value),l.value.tabSubmit&&Nn(E.value)&&o.inputValue!==""?(a("set-input-date",E.value,!0,!0),E.value=null):l.value.tabSubmit&&o.inputValue===""&&(E.value=null,a("clear",!0))},le=()=>{h.value=!0,a("focus"),e.nextTick().then(()=>{var S;l.value.enabled&&l.value.selectOnFocus&&((S=L.value)==null||S.select())})},ue=S=>{if(ft(S,c.value,!0),l.value.enabled&&l.value.openMenu&&!d.value.input){if(l.value.openMenu==="open"&&!o.isMenuOpen)return a("open");if(l.value.openMenu==="toggle")return a("toggle")}else l.value.enabled||a("toggle")},y=()=>{a("real-blur"),h.value=!1,(!o.isMenuOpen||d.value.enabled&&d.value.input)&&a("blur"),o.autoApply&&l.value.enabled&&E.value&&!o.isMenuOpen&&(a("set-input-date",E.value),a("select-date"),E.value=null)},z=S=>{ft(S,c.value,!0),a("clear")},J=()=>{a("close")},w=S=>{if(S.key==="Tab"&&W(S),S.key==="Enter"&&O(S),S.key==="Escape"&&l.value.escClose&&J(),!l.value.enabled){if(S.code==="Tab")return;S.preventDefault()}},H=()=>{var S;(S=L.value)==null||S.focus({preventScroll:!0})},$=S=>{E.value=S},te=S=>{S.key===Te.tab&&W(S,!0)};return r({focusInput:H,setParsedDate:$}),(S,V)=>{var k,K,ne;return e.openBlock(),e.createElementBlock("div",{onClick:ue},[S.$slots.trigger&&!S.$slots["dp-input"]&&!e.unref(d).enabled?e.renderSlot(S.$slots,"trigger",{key:0}):e.createCommentVNode("",!0),!S.$slots.trigger&&(!e.unref(d).enabled||e.unref(d).input)?(e.openBlock(),e.createElementBlock("div",di,[S.$slots["dp-input"]&&!S.$slots.trigger&&(!e.unref(d).enabled||e.unref(d).enabled&&e.unref(d).input)?e.renderSlot(S.$slots,"dp-input",{key:0,value:t.inputValue,isMenuOpen:t.isMenuOpen,onInput:R,onEnter:O,onTab:W,onClear:z,onBlur:y,onKeypress:w,onPaste:Q,onFocus:le,openMenu:()=>S.$emit("open"),closeMenu:()=>S.$emit("close"),toggleMenu:()=>S.$emit("toggle")}):e.createCommentVNode("",!0),S.$slots["dp-input"]?e.createCommentVNode("",!0):(e.openBlock(),e.createElementBlock("input",{key:1,id:S.uid?`dp-input-${S.uid}`:void 0,ref_key:"inputRef",ref:L,"data-test-id":"dp-input",name:S.name,class:e.normalizeClass(p.value),inputmode:e.unref(l).enabled?"text":"none",placeholder:S.placeholder,disabled:S.disabled,readonly:S.readonly,required:S.required,value:t.inputValue,autocomplete:S.autocomplete,"aria-label":(k=e.unref(i))==null?void 0:k.input,"aria-disabled":S.disabled||void 0,"aria-invalid":S.state===!1?!0:void 0,onInput:R,onBlur:y,onFocus:le,onKeypress:w,onKeydown:V[0]||(V[0]=N=>w(N)),onPaste:Q},null,42,fi)),e.createElementVNode("div",{onClick:V[3]||(V[3]=N=>a("toggle"))},[S.$slots["input-icon"]&&!S.hideInputIcon?(e.openBlock(),e.createElementBlock("span",{key:0,class:"dp__input_icon",onClick:V[1]||(V[1]=N=>a("toggle"))},[e.renderSlot(S.$slots,"input-icon")])):e.createCommentVNode("",!0),!S.$slots["input-icon"]&&!S.hideInputIcon&&!S.$slots["dp-input"]?(e.openBlock(),e.createBlock(e.unref(We),{key:1,"aria-label":(K=e.unref(i))==null?void 0:K.calendarIcon,class:"dp__input_icon dp__input_icons",onClick:V[2]||(V[2]=N=>a("toggle"))},null,8,["aria-label"])):e.createCommentVNode("",!0)]),S.$slots["clear-icon"]&&(S.alwaysClearable||t.inputValue&&S.clearable&&!S.disabled&&!S.readonly)?(e.openBlock(),e.createElementBlock("span",mi,[e.renderSlot(S.$slots,"clear-icon",{clear:z})])):e.createCommentVNode("",!0),!S.$slots["clear-icon"]&&(S.alwaysClearable||S.clearable&&t.inputValue&&!S.disabled&&!S.readonly)?(e.openBlock(),e.createElementBlock("button",{key:3,"aria-label":(ne=e.unref(i))==null?void 0:ne.clearInput,class:"dp--clear-btn",type:"button",onKeydown:V[4]||(V[4]=N=>e.unref(ze)(N,()=>z(N),!0,te)),onClick:V[5]||(V[5]=e.withModifiers(N=>z(N),["prevent"]))},[e.createVNode(e.unref(wt),{class:"dp__input_icons","data-test-id":"clear-icon"})],40,hi)):e.createCommentVNode("",!0)])):e.createCommentVNode("",!0)])}}}),yi=typeof window<"u"?window:void 0,qn=()=>{},pi=t=>e.getCurrentScope()?(e.onScopeDispose(t),!0):!1,wi=(t,r,n,a)=>{if(!t)return qn;let o=qn;const l=e.watch(()=>e.unref(t),d=>{o(),d&&(d.addEventListener(r,n,a),o=()=>{d.removeEventListener(r,n,a),o=qn})},{immediate:!0,flush:"post"}),i=()=>{l(),o()};return pi(i),i},ki=(t,r,n,a={})=>{const{window:o=yi,event:l="pointerdown"}=a;return o?wi(o,l,d=>{const c=Ye(t),v=Ye(r);!c||!v||c===d.target||d.composedPath().includes(c)||d.composedPath().includes(v)||n(d)},{passive:!0}):void 0},bi=["data-dp-mobile"],vi=e.defineComponent({compatConfig:{MODE:3},__name:"VueDatePicker",props:{...nn},emits:["update:model-value","update:model-timezone-value","text-submit","closed","cleared","open","focus","blur","internal-model-change","recalculate-position","flow-step","update-month-year","invalid-select","invalid-fixed-range","tooltip-open","tooltip-close","time-picker-open","time-picker-close","am-pm-change","range-start","range-end","date-update","invalid-date","overlay-toggle","text-input"],setup(t,{expose:r,emit:n}){const a=n,o=t,l=e.useSlots(),i=e.ref(!1),d=e.toRef(o,"modelValue"),c=e.toRef(o,"timezone"),v=e.ref(null),f=e.ref(null),T=e.ref(null),m=e.ref(!1),M=e.ref(null),_=e.ref(!1),E=e.ref(!1),L=e.ref(!1),h=e.ref(!1),{setMenuFocused:A,setShiftKey:p}=Za(),{clearArrowNav:x}=gt(),{validateDate:U,isValidTime:G}=pt(o),{defaultedTransitions:Q,defaultedTextInput:g,defaultedInline:R,defaultedConfig:O,defaultedRange:W,defaultedMultiDates:le}=Ce(o),{menuTransition:ue,showTransition:y}=Qt(Q),{isMobile:z}=ur(O);e.onMounted(()=>{N(o.modelValue),e.nextTick().then(()=>{if(!R.value.enabled){const P=V(M.value);P==null||P.addEventListener("scroll",I),window==null||window.addEventListener("resize",se)}}),R.value.enabled&&(i.value=!0),window==null||window.addEventListener("keyup",Y),window==null||window.addEventListener("keydown",ge)}),e.onUnmounted(()=>{if(!R.value.enabled){const P=V(M.value);P==null||P.removeEventListener("scroll",I),window==null||window.removeEventListener("resize",se)}window==null||window.removeEventListener("keyup",Y),window==null||window.removeEventListener("keydown",ge)});const J=Ue(l,"all",o.presetDates),w=Ue(l,"input");e.watch([d,c],()=>{N(d.value)},{deep:!0});const{openOnTop:H,menuStyle:$,xCorrect:te,setMenuPosition:S,getScrollableParent:V,shadowRender:k}=li({menuRef:v,menuRefInner:f,inputRef:T,pickerWrapperRef:M,inline:R,emit:a,props:o,slots:l}),{inputValue:K,internalModelValue:ne,parseExternalModelValue:N,emitModelValue:X,formatInputValue:s,checkBeforeEmit:C}=ts(a,o,m),ae=e.computed(()=>({dp__main:!0,dp__theme_dark:o.dark,dp__theme_light:!o.dark,dp__flex_display:R.value.enabled,"dp--flex-display-collapsed":L.value,dp__flex_display_with_input:R.value.input})),B=e.computed(()=>o.dark?"dp__theme_dark":"dp__theme_light"),me=e.computed(()=>o.teleport?{to:typeof o.teleport=="boolean"?"body":o.teleport,disabled:!o.teleport||R.value.enabled}:{}),de=e.computed(()=>({class:"dp__outer_menu_wrap"})),ee=e.computed(()=>R.value.enabled&&(o.timePicker||o.monthPicker||o.yearPicker||o.quarterPicker)),u=()=>{var P,j;return((j=(P=T.value)==null?void 0:P.$el)==null?void 0:j.getBoundingClientRect())??{width:0,left:0,right:0}},I=()=>{i.value&&(O.value.closeOnScroll?Ve():S())},se=()=>{var j;i.value&&S();const P=((j=f.value)==null?void 0:j.$el.getBoundingClientRect().width)??0;L.value=document.body.offsetWidth<=P},Y=P=>{P.key==="Tab"&&!R.value.enabled&&!o.teleport&&O.value.tabOutClosesMenu&&(M.value.contains(document.activeElement)||Ve()),E.value=P.shiftKey},ge=P=>{E.value=P.shiftKey},he=()=>{!o.disabled&&!o.readonly&&(k(cr,o),S(!1),i.value=!0,i.value&&a("open"),i.value||ie(),N(o.modelValue))},De=()=>{var P,j;K.value="",ie(),(P=f.value)==null||P.onValueCleared(),(j=T.value)==null||j.setParsedDate(null),a("update:model-value",null),a("update:model-timezone-value",null),a("cleared"),O.value.closeOnClearValue&&Ve()},D=()=>{const P=ne.value;return!P||!Array.isArray(P)&&U(P)?!0:Array.isArray(P)?le.value.enabled||P.length===2&&U(P[0])&&U(P[1])?!0:W.value.partialRange&&!o.timePicker?U(P[0]):!1:!1},F=()=>{C()&&D()?(X(),Ve()):a("invalid-select",ne.value)},Me=P=>{_e(),X(),O.value.closeOnAutoApply&&!P&&Ve()},_e=()=>{T.value&&g.value.enabled&&T.value.setParsedDate(ne.value)},ut=(P=!1)=>{o.autoApply&&G(ne.value)&&D()&&(W.value.enabled&&Array.isArray(ne.value)?(W.value.partialRange||ne.value.length===2)&&Me(P):Me(P))},ie=()=>{g.value.enabled||(ne.value=null)},Ve=(P=!1)=>{P&&ne.value&&O.value.setDateOnMenuClose&&F(),R.value.enabled||(i.value&&(i.value=!1,te.value=!1,A(!1),p(!1),x(),a("closed"),K.value&&N(d.value)),ie(),a("blur"))},Qe=(P,j,re=!1)=>{if(!P){ne.value=null;return}const He=Array.isArray(P)?!P.some(xt=>!U(xt)):U(P),tt=G(P);He&&tt?(h.value=!0,ne.value=P,j&&(_.value=re,F(),a("text-submit")),e.nextTick().then(()=>{h.value=!1})):a("invalid-date",P)},jn=()=>{o.autoApply&&G(ne.value)&&X(),_e()},ln=()=>i.value?Ve():he(),Qn=P=>{ne.value=P},Gn=()=>{g.value.enabled&&(m.value=!0,s()),a("focus")},Kn=()=>{if(g.value.enabled&&(m.value=!1,N(o.modelValue),_.value)){const P=Cl(M.value,E.value);P==null||P.focus()}a("blur")},Xn=P=>{f.value&&f.value.updateMonthYear(0,{month:Va(P.month),year:Va(P.year)})},Jn=P=>{N(P??o.modelValue)},Zn=(P,j)=>{var re;(re=f.value)==null||re.switchView(P,j)},dr=(P,j)=>O.value.onClickOutside?O.value.onClickOutside(P,j):Ve(!0),b=(P=0)=>{var j;(j=f.value)==null||j.handleFlow(P)},Z=()=>v;return ki(v,T,P=>dr(D,P)),r({closeMenu:Ve,selectDate:F,clearValue:De,openMenu:he,onScroll:I,formatInputValue:s,updateInternalModelValue:Qn,setMonthYear:Xn,parseModel:Jn,switchView:Zn,toggleMenu:ln,handleFlow:b,getDpWrapMenuRef:Z}),(P,j)=>(e.openBlock(),e.createElementBlock("div",{ref_key:"pickerWrapperRef",ref:M,class:e.normalizeClass(ae.value),"data-datepicker-instance":"","data-dp-mobile":e.unref(z)},[e.createVNode(gi,e.mergeProps({ref_key:"inputRef",ref:T,"input-value":e.unref(K),"onUpdate:inputValue":j[0]||(j[0]=re=>e.isRef(K)?K.value=re:null),"is-menu-open":i.value},P.$props,{onClear:De,onOpen:he,onSetInputDate:Qe,onSetEmptyDate:e.unref(X),onSelectDate:F,onToggle:ln,onClose:Ve,onFocus:Gn,onBlur:Kn,onRealBlur:j[1]||(j[1]=re=>m.value=!1),onTextInput:j[2]||(j[2]=re=>P.$emit("text-input",re))}),e.createSlots({_:2},[e.renderList(e.unref(w),(re,He)=>({name:re,fn:e.withCtx(tt=>[e.renderSlot(P.$slots,re,e.normalizeProps(e.guardReactiveProps(tt)))])}))]),1040,["input-value","is-menu-open","onSetEmptyDate"]),(e.openBlock(),e.createBlock(e.resolveDynamicComponent(P.teleport?e.Teleport:"div"),e.normalizeProps(e.guardReactiveProps(me.value)),{default:e.withCtx(()=>[e.createVNode(e.Transition,{name:e.unref(ue)(e.unref(H)),css:e.unref(y)&&!e.unref(R).enabled},{default:e.withCtx(()=>[i.value?(e.openBlock(),e.createElementBlock("div",e.mergeProps({key:0,ref_key:"dpWrapMenuRef",ref:v},de.value,{class:{"dp--menu-wrapper":!e.unref(R).enabled},style:e.unref(R).enabled?void 0:e.unref($)}),[e.createVNode(cr,e.mergeProps({ref_key:"dpMenuRef",ref:f},P.$props,{"internal-model-value":e.unref(ne),"onUpdate:internalModelValue":j[3]||(j[3]=re=>e.isRef(ne)?ne.value=re:null),class:{[B.value]:!0,"dp--menu-wrapper":P.teleport},"open-on-top":e.unref(H),"no-overlay-focus":ee.value,collapse:L.value,"get-input-rect":u,"is-text-input-date":h.value,onClosePicker:Ve,onSelectDate:F,onAutoApply:ut,onTimeUpdate:jn,onFlowStep:j[4]||(j[4]=re=>P.$emit("flow-step",re)),onUpdateMonthYear:j[5]||(j[5]=re=>P.$emit("update-month-year",re)),onInvalidSelect:j[6]||(j[6]=re=>P.$emit("invalid-select",e.unref(ne))),onAutoApplyInvalid:j[7]||(j[7]=re=>P.$emit("invalid-select",re)),onInvalidFixedRange:j[8]||(j[8]=re=>P.$emit("invalid-fixed-range",re)),onRecalculatePosition:e.unref(S),onTooltipOpen:j[9]||(j[9]=re=>P.$emit("tooltip-open",re)),onTooltipClose:j[10]||(j[10]=re=>P.$emit("tooltip-close",re)),onTimePickerOpen:j[11]||(j[11]=re=>P.$emit("time-picker-open",re)),onTimePickerClose:j[12]||(j[12]=re=>P.$emit("time-picker-close",re)),onAmPmChange:j[13]||(j[13]=re=>P.$emit("am-pm-change",re)),onRangeStart:j[14]||(j[14]=re=>P.$emit("range-start",re)),onRangeEnd:j[15]||(j[15]=re=>P.$emit("range-end",re)),onDateUpdate:j[16]||(j[16]=re=>P.$emit("date-update",re)),onInvalidDate:j[17]||(j[17]=re=>P.$emit("invalid-date",re)),onOverlayToggle:j[18]||(j[18]=re=>P.$emit("overlay-toggle",re)),onMenuBlur:j[19]||(j[19]=re=>P.$emit("blur"))}),e.createSlots({_:2},[e.renderList(e.unref(J),(re,He)=>({name:re,fn:e.withCtx(tt=>[e.renderSlot(P.$slots,re,e.normalizeProps(e.guardReactiveProps({...tt})))])}))]),1040,["internal-model-value","class","open-on-top","no-overlay-focus","collapse","is-text-input-date","onRecalculatePosition"])],16)):e.createCommentVNode("",!0)]),_:3},8,["name","css"])]),_:3},16))],10,bi))}}),Un=(()=>{const t=vi;return t.install=r=>{r.component("Vue3DatePicker",t)},t})();return Object.entries(Object.freeze(Object.defineProperty({__proto__:null,default:Un},Symbol.toStringTag,{value:"Module"}))).forEach(([t,r])=>{t!=="default"&&(Un[t]=r)}),Un}(Vue);
