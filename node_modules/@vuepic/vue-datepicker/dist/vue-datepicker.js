import { useAttrs as qn, openBlock as A, createElementBlock as V, createElementVNode as ge, unref as s, reactive as Zt, computed as Q, ref as te, toRef as Gt, watch as ot, defineComponent as Ve, onMounted as je, onUnmounted as xt, renderSlot as de, normalizeProps as ze, mergeProps as He, Fragment as ke, normalizeStyle as rt, createCommentVNode as J, createTextVNode as gt, toDisplayString as Qe, onBeforeUpdate as Xn, nextTick as nt, normalizeClass as De, withModifiers as Qt, renderList as Be, withDirectives as sa, vShow as ua, createBlock as Te, withCtx as we, with<PERSON>eys as Jn, createVNode as at, Transition as Ut, createSlots as qe, useSlots as Bt, guardReactiveProps as xe, resolveDynamicComponent as fa, toValue as Zn, h as xn, render as an, getCurrentScope as el, onScopeDispose as tl, isRef as nn, Teleport as al } from "vue";
import { format as vt, isEqual as Et, set as Pe, startOfMonth as nl, isAfter as Ot, getYear as ye, getMonth as $e, setMonth as ll, setYear as ft, addMonths as Pt, subMonths as qt, isValid as ia, isBefore as Ft, eachDayOfInterval as hn, setHours as rl, setMinutes as ol, setSeconds as bn, setMilliseconds as kn, getHours as ht, getMinutes as $t, getSeconds as Lt, startOfWeek as Ua, endOfWeek as wn, parse as Na, isDate as sl, subDays as ul, addDays as kt, addHours as il, addYears as Dn, subYears as Mn, endOfYear as $n, startOfYear as da, differenceInYears as dl, add as An, sub as cl, getWeek as fl, getISOWeek as vl, isSameQuarter as ln, eachQuarterOfInterval as ml, startOfQuarter as pl, endOfQuarter as rn, getQuarter as on, getDay as yl, differenceInCalendarDays as gl } from "date-fns";
function Wt() {
  const e = qn();
  return A(), V(
    "svg",
    {
      xmlns: "http://www.w3.org/2000/svg",
      viewBox: "0 0 32 32",
      fill: "currentColor",
      "aria-hidden": "true",
      class: "dp__icon",
      role: "img",
      ...e
    },
    [
      ge("path", {
        d: "M29.333 8c0-2.208-1.792-4-4-4h-18.667c-2.208 0-4 1.792-4 4v18.667c0 2.208 1.792 4 4 4h18.667c2.208 0 4-1.792 4-4v-18.667zM26.667 8v18.667c0 0.736-0.597 1.333-1.333 1.333 0 0-18.667 0-18.667 0-0.736 0-1.333-0.597-1.333-1.333 0 0 0-18.667 0-18.667 0-0.736 0.597-1.333 1.333-1.333 0 0 18.667 0 18.667 0 0.736 0 1.333 0.597 1.333 1.333z"
      }),
      ge("path", {
        d: "M20 2.667v5.333c0 0.736 0.597 1.333 1.333 1.333s1.333-0.597 1.333-1.333v-5.333c0-0.736-0.597-1.333-1.333-1.333s-1.333 0.597-1.333 1.333z"
      }),
      ge("path", {
        d: "M9.333 2.667v5.333c0 0.736 0.597 1.333 1.333 1.333s1.333-0.597 1.333-1.333v-5.333c0-0.736-0.597-1.333-1.333-1.333s-1.333 0.597-1.333 1.333z"
      }),
      ge("path", {
        d: "M4 14.667h24c0.736 0 1.333-0.597 1.333-1.333s-0.597-1.333-1.333-1.333h-24c-0.736 0-1.333 0.597-1.333 1.333s0.597 1.333 1.333 1.333z"
      })
    ]
  );
}
Wt.compatConfig = {
  MODE: 3
};
function Tn() {
  return A(), V(
    "svg",
    {
      xmlns: "http://www.w3.org/2000/svg",
      viewBox: "0 0 32 32",
      fill: "currentColor",
      "aria-hidden": "true",
      class: "dp__icon",
      role: "img"
    },
    [
      ge("path", {
        d: "M23.057 7.057l-16 16c-0.52 0.52-0.52 1.365 0 1.885s1.365 0.52 1.885 0l16-16c0.52-0.52 0.52-1.365 0-1.885s-1.365-0.52-1.885 0z"
      }),
      ge("path", {
        d: "M7.057 8.943l16 16c0.52 0.52 1.365 0.52 1.885 0s0.52-1.365 0-1.885l-16-16c-0.52-0.52-1.365-0.52-1.885 0s-0.52 1.365 0 1.885z"
      })
    ]
  );
}
Tn.compatConfig = {
  MODE: 3
};
function Wa() {
  return A(), V(
    "svg",
    {
      xmlns: "http://www.w3.org/2000/svg",
      viewBox: "0 0 32 32",
      fill: "currentColor",
      "aria-hidden": "true",
      class: "dp__icon",
      role: "img"
    },
    [
      ge("path", {
        d: "M20.943 23.057l-7.057-7.057c0 0 7.057-7.057 7.057-7.057 0.52-0.52 0.52-1.365 0-1.885s-1.365-0.52-1.885 0l-8 8c-0.521 0.521-0.521 1.365 0 1.885l8 8c0.52 0.52 1.365 0.52 1.885 0s0.52-1.365 0-1.885z"
      })
    ]
  );
}
Wa.compatConfig = {
  MODE: 3
};
function Va() {
  return A(), V(
    "svg",
    {
      xmlns: "http://www.w3.org/2000/svg",
      viewBox: "0 0 32 32",
      fill: "currentColor",
      "aria-hidden": "true",
      class: "dp__icon",
      role: "img"
    },
    [
      ge("path", {
        d: "M12.943 24.943l8-8c0.521-0.521 0.521-1.365 0-1.885l-8-8c-0.52-0.52-1.365-0.52-1.885 0s-0.52 1.365 0 1.885l7.057 7.057c0 0-7.057 7.057-7.057 7.057-0.52 0.52-0.52 1.365 0 1.885s1.365 0.52 1.885 0z"
      })
    ]
  );
}
Va.compatConfig = {
  MODE: 3
};
function ja() {
  return A(), V(
    "svg",
    {
      xmlns: "http://www.w3.org/2000/svg",
      viewBox: "0 0 32 32",
      fill: "currentColor",
      "aria-hidden": "true",
      class: "dp__icon",
      role: "img"
    },
    [
      ge("path", {
        d: "M16 1.333c-8.095 0-14.667 6.572-14.667 14.667s6.572 14.667 14.667 14.667c8.095 0 14.667-6.572 14.667-14.667s-6.572-14.667-14.667-14.667zM16 4c6.623 0 12 5.377 12 12s-5.377 12-12 12c-6.623 0-12-5.377-12-12s5.377-12 12-12z"
      }),
      ge("path", {
        d: "M14.667 8v8c0 0.505 0.285 0.967 0.737 1.193l5.333 2.667c0.658 0.329 1.46 0.062 1.789-0.596s0.062-1.46-0.596-1.789l-4.596-2.298c0 0 0-7.176 0-7.176 0-0.736-0.597-1.333-1.333-1.333s-1.333 0.597-1.333 1.333z"
      })
    ]
  );
}
ja.compatConfig = {
  MODE: 3
};
function Ka() {
  return A(), V(
    "svg",
    {
      xmlns: "http://www.w3.org/2000/svg",
      viewBox: "0 0 32 32",
      fill: "currentColor",
      "aria-hidden": "true",
      class: "dp__icon",
      role: "img"
    },
    [
      ge("path", {
        d: "M24.943 19.057l-8-8c-0.521-0.521-1.365-0.521-1.885 0l-8 8c-0.52 0.52-0.52 1.365 0 1.885s1.365 0.52 1.885 0l7.057-7.057c0 0 7.057 7.057 7.057 7.057 0.52 0.52 1.365 0.52 1.885 0s0.52-1.365 0-1.885z"
      })
    ]
  );
}
Ka.compatConfig = {
  MODE: 3
};
function Ga() {
  return A(), V(
    "svg",
    {
      xmlns: "http://www.w3.org/2000/svg",
      viewBox: "0 0 32 32",
      fill: "currentColor",
      "aria-hidden": "true",
      class: "dp__icon",
      role: "img"
    },
    [
      ge("path", {
        d: "M7.057 12.943l8 8c0.521 0.521 1.365 0.521 1.885 0l8-8c0.52-0.52 0.52-1.365 0-1.885s-1.365-0.52-1.885 0l-7.057 7.057c0 0-7.057-7.057-7.057-7.057-0.52-0.52-1.365-0.52-1.885 0s-0.52 1.365 0 1.885z"
      })
    ]
  );
}
Ga.compatConfig = {
  MODE: 3
};
const et = (e, t) => t ? new Date(e.toLocaleString("en-US", { timeZone: t })) : new Date(e), Qa = (e, t, r) => {
  const a = Fa(e, t, r);
  return a || j();
}, hl = (e, t, r) => {
  const a = t.dateInTz ? et(new Date(e), t.dateInTz) : j(e);
  return r ? We(a, !0) : a;
}, Fa = (e, t, r) => {
  if (!e) return null;
  const a = r ? We(j(e), !0) : j(e);
  return t ? t.exactMatch ? hl(e, t, r) : et(a, t.timezone) : a;
}, bl = (e) => {
  const r = new Date(e.getFullYear(), 0, 1).getTimezoneOffset();
  return e.getTimezoneOffset() < r;
}, kl = (e, t) => {
  if (!e) return 0;
  const r = /* @__PURE__ */ new Date(), a = new Date(r.toLocaleString("en-US", { timeZone: "UTC" })), n = new Date(r.toLocaleString("en-US", { timeZone: e })), d = (bl(t ?? n) ? n : t ?? n).getTimezoneOffset() / 60;
  return (+a - +n) / (1e3 * 60 * 60) - d;
};
var ut = /* @__PURE__ */ ((e) => (e.month = "month", e.year = "year", e))(ut || {}), it = /* @__PURE__ */ ((e) => (e.top = "top", e.bottom = "bottom", e))(it || {}), Rt = /* @__PURE__ */ ((e) => (e.header = "header", e.calendar = "calendar", e.timePicker = "timePicker", e))(Rt || {}), Ge = /* @__PURE__ */ ((e) => (e.month = "month", e.year = "year", e.calendar = "calendar", e.time = "time", e.minutes = "minutes", e.hours = "hours", e.seconds = "seconds", e))(Ge || {});
const wl = ["timestamp", "date", "iso"];
var Je = /* @__PURE__ */ ((e) => (e.up = "up", e.down = "down", e.left = "left", e.right = "right", e))(Je || {}), Oe = /* @__PURE__ */ ((e) => (e.arrowUp = "ArrowUp", e.arrowDown = "ArrowDown", e.arrowLeft = "ArrowLeft", e.arrowRight = "ArrowRight", e.enter = "Enter", e.space = " ", e.esc = "Escape", e.tab = "Tab", e.home = "Home", e.end = "End", e.pageUp = "PageUp", e.pageDown = "PageDown", e))(Oe || {}), Nt = /* @__PURE__ */ ((e) => (e.MONTH_AND_YEAR = "MM-yyyy", e.YEAR = "yyyy", e.DATE = "dd-MM-yyyy", e))(Nt || {});
function sn(e) {
  return (t) => new Intl.DateTimeFormat(e, { weekday: "short", timeZone: "UTC" }).format(/* @__PURE__ */ new Date(`2017-01-0${t}T00:00:00+00:00`)).slice(0, 2);
}
function Dl(e) {
  return (t) => vt(et(/* @__PURE__ */ new Date(`2017-01-0${t}T00:00:00+00:00`), "UTC"), "EEEEEE", { locale: e });
}
const Ml = (e, t, r) => {
  const a = [1, 2, 3, 4, 5, 6, 7];
  let n;
  if (e !== null)
    try {
      n = a.map(Dl(e));
    } catch {
      n = a.map(sn(t));
    }
  else
    n = a.map(sn(t));
  const u = n.slice(0, r), d = n.slice(r + 1, n.length);
  return [n[r]].concat(...d).concat(...u);
}, qa = (e, t, r) => {
  const a = [];
  for (let n = +e[0]; n <= +e[1]; n++)
    a.push({ value: +n, text: Cn(n, t) });
  return r ? a.reverse() : a;
}, Sn = (e, t, r) => {
  const a = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12].map((u) => {
    const d = u < 10 ? `0${u}` : u;
    return /* @__PURE__ */ new Date(`2017-${d}-01T00:00:00+00:00`);
  });
  if (e !== null)
    try {
      const u = r === "long" ? "LLLL" : "LLL";
      return a.map((d, y) => {
        const i = vt(et(d, "UTC"), u, { locale: e });
        return {
          text: i.charAt(0).toUpperCase() + i.substring(1),
          value: y
        };
      });
    } catch {
    }
  const n = new Intl.DateTimeFormat(t, { month: r, timeZone: "UTC" });
  return a.map((u, d) => {
    const y = n.format(u);
    return {
      text: y.charAt(0).toUpperCase() + y.substring(1),
      value: d
    };
  });
}, $l = (e) => [12, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11][e], Le = (e) => {
  const t = s(e);
  return t != null && t.$el ? t == null ? void 0 : t.$el : t;
}, Al = (e) => ({ type: "dot", ...e ?? {} }), Pn = (e) => Array.isArray(e) ? !!e[0] && !!e[1] : !1, Xa = {
  prop: (e) => `"${e}" prop must be enabled!`,
  dateArr: (e) => `You need to use array as "model-value" binding in order to support "${e}"`
}, Ne = (e) => e, un = (e) => e === 0 ? e : !e || isNaN(+e) ? null : +e, dn = (e) => e === null, Rn = (e) => {
  if (e)
    return [...e.querySelectorAll("input, button, select, textarea, a[href]")][0];
}, Tl = (e) => {
  const t = [], r = (a) => a.filter((n) => n);
  for (let a = 0; a < e.length; a += 3) {
    const n = [e[a], e[a + 1], e[a + 2]];
    t.push(r(n));
  }
  return t;
}, Xt = (e, t, r) => {
  const a = r != null, n = t != null;
  if (!a && !n) return !1;
  const u = +r, d = +t;
  return a && n ? +e > u || +e < d : a ? +e > u : n ? +e < d : !1;
}, zt = (e, t) => Tl(e).map((r) => r.map((a) => {
  const { active: n, disabled: u, isBetween: d, highlighted: y } = t(a);
  return {
    ...a,
    active: n,
    disabled: u,
    className: {
      dp__overlay_cell_active: n,
      dp__overlay_cell: !n,
      dp__overlay_cell_disabled: u,
      dp__overlay_cell_pad: !0,
      dp__overlay_cell_active_disabled: u && n,
      dp__cell_in_between: d,
      "dp--highlighted": y
    }
  };
})), Dt = (e, t, r = !1) => {
  e && t.allowStopPropagation && (r && e.stopImmediatePropagation(), e.stopPropagation());
}, Sl = () => [
  "a[href]",
  "area[href]",
  "input:not([disabled]):not([type='hidden'])",
  "select:not([disabled])",
  "textarea:not([disabled])",
  "button:not([disabled])",
  "[tabindex]:not([tabindex='-1'])",
  "[data-datepicker-instance]"
].join(", ");
function Pl(e, t) {
  let r = [...document.querySelectorAll(Sl())];
  r = r.filter((n) => !e.contains(n) || n.hasAttribute("data-datepicker-instance"));
  const a = r.indexOf(e);
  if (a >= 0 && (t ? a - 1 >= 0 : a + 1 <= r.length))
    return r[a + (t ? -1 : 1)];
}
const La = (e, t) => e == null ? void 0 : e.querySelector(`[data-dp-element="${t}"]`), Cn = (e, t) => new Intl.NumberFormat(t, { useGrouping: !1, style: "decimal" }).format(e), Ja = (e, t) => vt(e, t ?? Nt.DATE), Ta = (e) => Array.isArray(e), ca = (e, t, r) => t.get(Ja(e, r)), Rl = (e, t) => e ? t ? t instanceof Map ? !!ca(e, t) : t(j(e)) : !1 : !0, Ze = (e, t, r = !1, a) => {
  if (e.key === Oe.enter || e.key === Oe.space)
    return r && e.preventDefault(), t();
  if (a) return a(e);
}, Cl = () => "ontouchstart" in window || navigator.maxTouchPoints > 0, Ol = (e, t) => e ? Nt.MONTH_AND_YEAR : t ? Nt.YEAR : Nt.DATE, On = (e) => e < 10 ? `0${e}` : e, cn = (e, t, r, a, n, u) => {
  const d = Na(e, t.slice(0, e.length), /* @__PURE__ */ new Date(), { locale: u });
  return ia(d) && sl(d) ? a || n ? d : Pe(d, {
    hours: +r.hours,
    minutes: +(r == null ? void 0 : r.minutes),
    seconds: +(r == null ? void 0 : r.seconds),
    milliseconds: 0
  }) : null;
}, Bl = (e, t, r, a, n, u) => {
  const d = Array.isArray(r) ? r[0] : r;
  if (typeof t == "string")
    return cn(e, t, d, a, n, u);
  if (Array.isArray(t)) {
    let y = null;
    for (const i of t)
      if (y = cn(e, i, d, a, n, u), y)
        break;
    return y;
  }
  return typeof t == "function" ? t(e) : null;
}, j = (e) => e ? new Date(e) : /* @__PURE__ */ new Date(), _l = (e, t, r) => {
  if (t) {
    const n = (e.getMonth() + 1).toString().padStart(2, "0"), u = e.getDate().toString().padStart(2, "0"), d = e.getHours().toString().padStart(2, "0"), y = e.getMinutes().toString().padStart(2, "0"), i = r ? e.getSeconds().toString().padStart(2, "0") : "00";
    return `${e.getFullYear()}-${n}-${u}T${d}:${y}:${i}.000Z`;
  }
  const a = Date.UTC(
    e.getUTCFullYear(),
    e.getUTCMonth(),
    e.getUTCDate(),
    e.getUTCHours(),
    e.getUTCMinutes(),
    e.getUTCSeconds()
  );
  return new Date(a).toISOString();
}, We = (e, t) => {
  const r = j(JSON.parse(JSON.stringify(e))), a = Pe(r, { hours: 0, minutes: 0, seconds: 0, milliseconds: 0 });
  return t ? nl(a) : a;
}, Mt = (e, t, r, a) => {
  let n = e ? j(e) : j();
  return (t || t === 0) && (n = rl(n, +t)), (r || r === 0) && (n = ol(n, +r)), (a || a === 0) && (n = bn(n, +a)), kn(n, 0);
}, Ye = (e, t) => !e || !t ? !1 : Ft(We(e), We(t)), Ae = (e, t) => !e || !t ? !1 : Et(We(e), We(t)), Ee = (e, t) => !e || !t ? !1 : Ot(We(e), We(t)), Jt = (e, t, r) => e != null && e[0] && (e != null && e[1]) ? Ee(r, e[0]) && Ye(r, e[1]) : e != null && e[0] && t ? Ee(r, e[0]) && Ye(r, t) || Ye(r, e[0]) && Ee(r, t) : !1, dt = (e) => {
  const t = Pe(new Date(e), { date: 1 });
  return We(t);
}, Sa = (e, t, r) => t && (r || r === 0) ? Object.fromEntries(
  ["hours", "minutes", "seconds"].map((a) => a === t ? [a, r] : [a, isNaN(+e[a]) ? void 0 : +e[a]])
) : {
  hours: isNaN(+e.hours) ? void 0 : +e.hours,
  minutes: isNaN(+e.minutes) ? void 0 : +e.minutes,
  seconds: isNaN(+e.seconds) ? void 0 : +e.seconds
}, Ct = (e) => ({
  hours: ht(e),
  minutes: $t(e),
  seconds: Lt(e)
}), Bn = (e, t) => {
  if (t) {
    const r = ye(j(t));
    if (r > e) return 12;
    if (r === e) return $e(j(t));
  }
}, _n = (e, t) => {
  if (t) {
    const r = ye(j(t));
    return r < e ? -1 : r === e ? $e(j(t)) : void 0;
  }
}, Ht = (e) => {
  if (e) return ye(j(e));
}, Yn = (e, t) => {
  const r = Ee(e, t) ? t : e, a = Ee(t, e) ? t : e;
  return hn({ start: r, end: a });
}, Yl = (e) => {
  const t = Pt(e, 1);
  return { month: $e(t), year: ye(t) };
}, pt = (e, t) => {
  const r = Ua(e, { weekStartsOn: +t }), a = wn(e, { weekStartsOn: +t });
  return [r, a];
}, In = (e, t) => {
  const r = {
    hours: ht(j()),
    minutes: $t(j()),
    seconds: t ? Lt(j()) : 0
  };
  return Object.assign(r, e);
}, wt = (e, t, r) => [Pe(j(e), { date: 1 }), Pe(j(), { month: t, year: r, date: 1 })], yt = (e, t, r) => {
  let a = e ? j(e) : j();
  return (t || t === 0) && (a = ll(a, t)), r && (a = ft(a, r)), a;
}, En = (e, t, r, a, n) => {
  if (!a || n && !t || !n && !r) return !1;
  const u = n ? Pt(e, 1) : qt(e, 1), d = [$e(u), ye(u)];
  return n ? !El(...d, t) : !Il(...d, r);
}, Il = (e, t, r) => Ye(...wt(r, e, t)) || Ae(...wt(r, e, t)), El = (e, t, r) => Ee(...wt(r, e, t)) || Ae(...wt(r, e, t)), Nn = (e, t, r, a, n, u, d) => {
  if (typeof t == "function" && !d) return t(e);
  const y = r ? { locale: r } : void 0;
  return Array.isArray(e) ? `${vt(e[0], u, y)}${n && !e[1] ? "" : a}${e[1] ? vt(e[1], u, y) : ""}` : vt(e, u, y);
}, Yt = (e) => {
  if (e) return null;
  throw new Error(Xa.prop("partial-range"));
}, ra = (e, t) => {
  if (t) return e();
  throw new Error(Xa.prop("range"));
}, za = (e) => Array.isArray(e) ? ia(e[0]) && (e[1] ? ia(e[1]) : !0) : e ? ia(e) : !1, Nl = (e, t) => Pe(t ?? j(), {
  hours: +e.hours || 0,
  minutes: +e.minutes || 0,
  seconds: +e.seconds || 0
}), Pa = (e, t, r, a) => {
  if (!e) return !0;
  if (a) {
    const n = r === "max" ? Ft(e, t) : Ot(e, t), u = { seconds: 0, milliseconds: 0 };
    return n || Et(Pe(e, u), Pe(t, u));
  }
  return r === "max" ? e.getTime() <= t.getTime() : e.getTime() >= t.getTime();
}, Ra = (e, t, r) => e ? Nl(e, t) : j(r ?? t), fn = (e, t, r, a, n) => {
  if (Array.isArray(a)) {
    const d = Ra(e, a[0], t), y = Ra(e, a[1], t);
    return Pa(a[0], d, r, !!t) && Pa(a[1], y, r, !!t) && n;
  }
  const u = Ra(e, a, t);
  return Pa(a, u, r, !!t) && n;
}, Ca = (e) => Pe(j(), Ct(e)), Fl = (e, t, r) => {
  if (e instanceof Map) {
    const a = `${On(r + 1)}-${t}`;
    return e.size ? e.has(a) : !1;
  }
  return !1;
}, Ll = (e, t, r) => {
  if (e instanceof Map) {
    const a = `${On(r + 1)}-${t}`;
    return e.size ? e.has(a) : !0;
  }
  return !0;
}, Fn = (e, t, r) => typeof e == "function" ? e({ month: t, year: r }) : !!e.months.find((a) => a.month === t && a.year === r), Za = (e, t) => typeof e == "function" ? e(t) : e.years.includes(t), Ha = (e) => `dp-${vt(e, "yyyy-MM-dd")}`, vn = (e, t) => {
  const r = ul(We(t), e), a = kt(We(t), e);
  return { before: r, after: a };
}, jt = Zt({
  menuFocused: !1,
  shiftKeyInMenu: !1
}), Ln = () => {
  const e = (a) => {
    jt.menuFocused = a;
  }, t = (a) => {
    jt.shiftKeyInMenu !== a && (jt.shiftKeyInMenu = a);
  };
  return {
    control: Q(() => ({ shiftKeyInMenu: jt.shiftKeyInMenu, menuFocused: jt.menuFocused })),
    setMenuFocused: e,
    setShiftKey: t
  };
}, Ce = Zt({
  monthYear: [],
  calendar: [],
  time: [],
  actionRow: [],
  selectionGrid: [],
  timePicker: {
    0: [],
    1: []
  },
  monthPicker: []
}), Oa = te(null), oa = te(!1), Ba = te(!1), _a = te(!1), Ya = te(!1), Ke = te(0), Ie = te(0), At = () => {
  const e = Q(() => oa.value ? [...Ce.selectionGrid, Ce.actionRow].filter((f) => f.length) : Ba.value ? [
    ...Ce.timePicker[0],
    ...Ce.timePicker[1],
    Ya.value ? [] : [Oa.value],
    Ce.actionRow
  ].filter((f) => f.length) : _a.value ? [...Ce.monthPicker, Ce.actionRow] : [Ce.monthYear, ...Ce.calendar, Ce.time, Ce.actionRow].filter((f) => f.length)), t = (f) => {
    Ke.value = f ? Ke.value + 1 : Ke.value - 1;
    let I = null;
    e.value[Ie.value] && (I = e.value[Ie.value][Ke.value]), !I && e.value[Ie.value + (f ? 1 : -1)] ? (Ie.value = Ie.value + (f ? 1 : -1), Ke.value = f ? 0 : e.value[Ie.value].length - 1) : I || (Ke.value = f ? Ke.value - 1 : Ke.value + 1);
  }, r = (f) => {
    if (Ie.value === 0 && !f || Ie.value === e.value.length && f) return;
    Ie.value = f ? Ie.value + 1 : Ie.value - 1, e.value[Ie.value] ? e.value[Ie.value] && !e.value[Ie.value][Ke.value] && Ke.value !== 0 && (Ke.value = e.value[Ie.value].length - 1) : Ie.value = f ? Ie.value - 1 : Ie.value + 1;
  }, a = (f) => {
    let I = null;
    e.value[Ie.value] && (I = e.value[Ie.value][Ke.value]), I ? I.focus({ preventScroll: !oa.value }) : Ke.value = f ? Ke.value - 1 : Ke.value + 1;
  }, n = () => {
    t(!0), a(!0);
  }, u = () => {
    t(!1), a(!1);
  }, d = () => {
    r(!1), a(!0);
  }, y = () => {
    r(!0), a(!0);
  }, i = (f, I) => {
    Ce[I] = f;
  }, _ = (f, I) => {
    Ce[I] = f;
  }, c = () => {
    Ke.value = 0, Ie.value = 0;
  };
  return {
    buildMatrix: i,
    buildMultiLevelMatrix: _,
    setTimePickerBackRef: (f) => {
      Oa.value = f;
    },
    setSelectionGrid: (f) => {
      oa.value = f, c(), f || (Ce.selectionGrid = []);
    },
    setTimePicker: (f, I = !1) => {
      Ba.value = f, Ya.value = I, c(), f || (Ce.timePicker[0] = [], Ce.timePicker[1] = []);
    },
    setTimePickerElements: (f, I = 0) => {
      Ce.timePicker[I] = f;
    },
    arrowRight: n,
    arrowLeft: u,
    arrowUp: d,
    arrowDown: y,
    clearArrowNav: () => {
      Ce.monthYear = [], Ce.calendar = [], Ce.time = [], Ce.actionRow = [], Ce.selectionGrid = [], Ce.timePicker[0] = [], Ce.timePicker[1] = [], oa.value = !1, Ba.value = !1, Ya.value = !1, _a.value = !1, c(), Oa.value = null;
    },
    setMonthPicker: (f) => {
      _a.value = f, c();
    },
    refSets: Ce
    // exposed for testing
  };
}, mn = (e) => ({
  menuAppearTop: "dp-menu-appear-top",
  menuAppearBottom: "dp-menu-appear-bottom",
  open: "dp-slide-down",
  close: "dp-slide-up",
  next: "calendar-next",
  previous: "calendar-prev",
  vNext: "dp-slide-up",
  vPrevious: "dp-slide-down",
  ...e ?? {}
}), zl = (e) => ({
  toggleOverlay: "Toggle overlay",
  menu: "Datepicker menu",
  input: "Datepicker input",
  openTimePicker: "Open time picker",
  closeTimePicker: "Close time Picker",
  incrementValue: (t) => `Increment ${t}`,
  decrementValue: (t) => `Decrement ${t}`,
  openTpOverlay: (t) => `Open ${t} overlay`,
  amPmButton: "Switch AM/PM mode",
  openYearsOverlay: "Open years overlay",
  openMonthsOverlay: "Open months overlay",
  nextMonth: "Next month",
  prevMonth: "Previous month",
  nextYear: "Next year",
  prevYear: "Previous year",
  day: void 0,
  weekDay: void 0,
  clearInput: "Clear value",
  calendarIcon: "Calendar icon",
  timePicker: "Time picker",
  monthPicker: (t) => `Month picker${t ? " overlay" : ""}`,
  yearPicker: (t) => `Year picker${t ? " overlay" : ""}`,
  timeOverlay: (t) => `${t} overlay`,
  ...e ?? {}
}), pn = (e) => e ? typeof e == "boolean" ? e ? 2 : 0 : +e >= 2 ? +e : 2 : 0, Hl = (e) => {
  const t = typeof e == "object" && e, r = {
    static: !0,
    solo: !1
  };
  if (!e) return { ...r, count: pn(!1) };
  const a = t ? e : {}, n = t ? a.count ?? !0 : e, u = pn(n);
  return Object.assign(r, a, { count: u });
}, Ul = (e, t, r) => e || (typeof r == "string" ? r : t), Wl = (e) => typeof e == "boolean" ? e ? mn({}) : !1 : mn(e), Vl = (e) => {
  const t = {
    enterSubmit: !0,
    tabSubmit: !0,
    openMenu: "open",
    selectOnFocus: !1,
    rangeSeparator: " - ",
    escClose: !0
  };
  return typeof e == "object" ? { ...t, ...e ?? {}, enabled: !0 } : { ...t, enabled: e };
}, jl = (e) => ({
  months: [],
  years: [],
  times: { hours: [], minutes: [], seconds: [] },
  ...e ?? {}
}), Kl = (e) => ({
  showSelect: !0,
  showCancel: !0,
  showNow: !1,
  showPreview: !0,
  ...e ?? {}
}), Gl = (e) => {
  const t = { input: !1 };
  return typeof e == "object" ? { ...t, ...e ?? {}, enabled: !0 } : {
    enabled: e,
    ...t
  };
}, Ql = (e) => ({ ...{
  allowStopPropagation: !0,
  closeOnScroll: !1,
  modeHeight: 255,
  allowPreventDefault: !1,
  closeOnClearValue: !0,
  closeOnAutoApply: !0,
  noSwipe: !1,
  keepActionRow: !1,
  onClickOutside: void 0,
  tabOutClosesMenu: !0,
  arrowLeft: void 0,
  keepViewOnOffsetClick: !1,
  timeArrowHoldThreshold: 0,
  shadowDom: !1,
  mobileBreakpoint: 600,
  setDateOnMenuClose: !1
}, ...e ?? {} }), ql = (e) => {
  const t = {
    dates: Array.isArray(e) ? e.map((r) => j(r)) : [],
    years: [],
    months: [],
    quarters: [],
    weeks: [],
    weekdays: [],
    options: { highlightDisabled: !1 }
  };
  return typeof e == "function" ? e : { ...t, ...e ?? {} };
}, Xl = (e) => typeof e == "object" ? {
  type: (e == null ? void 0 : e.type) ?? "local",
  hideOnOffsetDates: (e == null ? void 0 : e.hideOnOffsetDates) ?? !1
} : {
  type: e,
  hideOnOffsetDates: !1
}, Jl = (e) => {
  const t = {
    noDisabledRange: !1,
    showLastInRange: !0,
    minMaxRawRange: !1,
    partialRange: !0,
    disableTimeRangeValidation: !1,
    maxRange: void 0,
    minRange: void 0,
    autoRange: void 0,
    fixedStart: !1,
    fixedEnd: !1
  };
  return typeof e == "object" ? { enabled: !0, ...t, ...e } : {
    enabled: e,
    ...t
  };
}, Zl = (e) => e ? typeof e == "string" ? {
  timezone: e,
  exactMatch: !1,
  dateInTz: void 0,
  emitTimezone: void 0,
  convertModel: !0
} : {
  timezone: e.timezone,
  exactMatch: e.exactMatch ?? !1,
  dateInTz: e.dateInTz ?? void 0,
  emitTimezone: e.emitTimezone ?? void 0,
  convertModel: e.convertModel ?? !0
} : { timezone: void 0, exactMatch: !1, emitTimezone: void 0 }, Ia = (e, t, r, a) => new Map(
  e.map((n) => {
    const u = Qa(n, t, a);
    return [Ja(u, r), u];
  })
), xl = (e, t) => e.length ? new Map(
  e.map((r) => {
    const a = Qa(r.date, t);
    return [Ja(a, Nt.DATE), r];
  })
) : null, er = (e) => {
  var r;
  const t = Ol(e.isMonthPicker, e.isYearPicker);
  return {
    minDate: Fa(e.minDate, e.timezone, e.isSpecific),
    maxDate: Fa(e.maxDate, e.timezone, e.isSpecific),
    disabledDates: Ta(e.disabledDates) ? Ia(e.disabledDates, e.timezone, t, e.isSpecific) : e.disabledDates,
    allowedDates: Ta(e.allowedDates) ? Ia(e.allowedDates, e.timezone, t, e.isSpecific) : null,
    highlight: typeof e.highlight == "object" && Ta((r = e.highlight) == null ? void 0 : r.dates) ? Ia(e.highlight.dates, e.timezone, t) : e.highlight,
    markers: xl(e.markers, e.timezone)
  };
}, tr = (e) => typeof e == "boolean" ? { enabled: e, dragSelect: !0, limit: null } : {
  enabled: !!e,
  limit: e.limit ? +e.limit : null,
  dragSelect: e.dragSelect ?? !0
}, ar = (e) => ({
  ...Object.fromEntries(
    Object.keys(e).map((r) => {
      const a = r, n = e[a], u = typeof e[a] == "string" ? { [n]: !0 } : Object.fromEntries(n.map((d) => [d, !0]));
      return [r, u];
    })
  )
}), _e = (e) => {
  const t = () => {
    const ee = e.enableSeconds ? ":ss" : "", x = e.enableMinutes ? ":mm" : "";
    return e.is24 ? `HH${x}${ee}` : `hh${x}${ee} aa`;
  }, r = () => {
    var ee;
    return e.format ? e.format : e.monthPicker ? "MM/yyyy" : e.timePicker ? t() : e.weekPicker ? `${((ee = H.value) == null ? void 0 : ee.type) === "iso" ? "II" : "ww"}-RR` : e.yearPicker ? "yyyy" : e.quarterPicker ? "QQQ/yyyy" : e.enableTimePicker ? `MM/dd/yyyy, ${t()}` : "MM/dd/yyyy";
  }, a = (ee) => In(ee, e.enableSeconds), n = () => z.value.enabled ? e.startTime && Array.isArray(e.startTime) ? [a(e.startTime[0]), a(e.startTime[1])] : null : e.startTime && !Array.isArray(e.startTime) ? a(e.startTime) : null, u = Q(() => Hl(e.multiCalendars)), d = Q(() => n()), y = Q(() => zl(e.ariaLabels)), i = Q(() => jl(e.filters)), _ = Q(() => Wl(e.transitions)), c = Q(() => Kl(e.actionRow)), C = Q(
    () => Ul(e.previewFormat, e.format, r())
  ), m = Q(() => Vl(e.textInput)), P = Q(() => Gl(e.inline)), U = Q(() => Ql(e.config)), N = Q(() => ql(e.highlight)), H = Q(() => Xl(e.weekNumbers)), f = Q(() => Zl(e.timezone)), I = Q(() => tr(e.multiDates)), k = Q(
    () => er({
      minDate: e.minDate,
      maxDate: e.maxDate,
      disabledDates: e.disabledDates,
      allowedDates: e.allowedDates,
      highlight: N.value,
      markers: e.markers,
      timezone: f.value,
      isSpecific: e.monthPicker || e.yearPicker || e.quarterPicker,
      isMonthPicker: e.monthPicker,
      isYearPicker: e.yearPicker
    })
  ), z = Q(() => Jl(e.range)), q = Q(() => ar(e.ui));
  return {
    defaultedTransitions: _,
    defaultedMultiCalendars: u,
    defaultedStartTime: d,
    defaultedAriaLabels: y,
    defaultedFilters: i,
    defaultedActionRow: c,
    defaultedPreviewFormat: C,
    defaultedTextInput: m,
    defaultedInline: P,
    defaultedConfig: U,
    defaultedHighlight: N,
    defaultedWeekNumbers: H,
    defaultedRange: z,
    propDates: k,
    defaultedTz: f,
    defaultedMultiDates: I,
    defaultedUI: q,
    getDefaultPattern: r,
    getDefaultStartTime: n
  };
}, nr = (e, t, r) => {
  const a = te(), { defaultedTextInput: n, defaultedRange: u, defaultedTz: d, defaultedMultiDates: y, getDefaultPattern: i } = _e(t), _ = te(""), c = Gt(t, "format"), C = Gt(t, "formatLocale");
  ot(
    a,
    () => {
      typeof t.onInternalModelChange == "function" && e("internal-model-change", a.value, R(!0));
    },
    { deep: !0 }
  ), ot(u, (l, D) => {
    l.enabled !== D.enabled && (a.value = null);
  }), ot(c, () => {
    W();
  });
  const m = (l) => d.value.timezone && d.value.convertModel ? et(l, d.value.timezone) : l, P = (l) => {
    if (d.value.timezone && d.value.convertModel) {
      const D = kl(d.value.timezone, l);
      return il(l, D);
    }
    return l;
  }, U = (l, D, ue = !1) => Nn(
    l,
    t.format,
    t.formatLocale,
    n.value.rangeSeparator,
    t.modelAuto,
    D ?? i(),
    ue
  ), N = (l) => l ? t.modelType ? oe(l) : {
    hours: ht(l),
    minutes: $t(l),
    seconds: t.enableSeconds ? Lt(l) : 0
  } : null, H = (l) => t.modelType ? oe(l) : { month: $e(l), year: ye(l) }, f = (l) => Array.isArray(l) ? y.value.enabled ? l.map((D) => I(D, ft(j(), D))) : ra(
    () => [
      ft(j(), l[0]),
      l[1] ? ft(j(), l[1]) : Yt(u.value.partialRange)
    ],
    u.value.enabled
  ) : ft(j(), +l), I = (l, D) => (typeof l == "string" || typeof l == "number") && t.modelType ? T(l) : D, k = (l) => Array.isArray(l) ? [
    I(
      l[0],
      Mt(null, +l[0].hours, +l[0].minutes, l[0].seconds)
    ),
    I(
      l[1],
      Mt(null, +l[1].hours, +l[1].minutes, l[1].seconds)
    )
  ] : I(l, Mt(null, l.hours, l.minutes, l.seconds)), z = (l) => {
    const D = Pe(j(), { date: 1 });
    return Array.isArray(l) ? y.value.enabled ? l.map((ue) => I(ue, yt(D, +ue.month, +ue.year))) : ra(
      () => [
        I(l[0], yt(D, +l[0].month, +l[0].year)),
        I(
          l[1],
          l[1] ? yt(D, +l[1].month, +l[1].year) : Yt(u.value.partialRange)
        )
      ],
      u.value.enabled
    ) : I(l, yt(D, +l.month, +l.year));
  }, q = (l) => {
    if (Array.isArray(l))
      return l.map((D) => T(D));
    throw new Error(Xa.dateArr("multi-dates"));
  }, ee = (l) => {
    if (Array.isArray(l) && u.value.enabled) {
      const D = l[0], ue = l[1];
      return [
        j(Array.isArray(D) ? D[0] : null),
        Array.isArray(ue) && ue.length ? j(ue[0]) : null
      ];
    }
    return j(l[0]);
  }, x = (l) => t.modelAuto ? Array.isArray(l) ? [T(l[0]), T(l[1])] : t.autoApply ? [T(l)] : [T(l), null] : Array.isArray(l) ? ra(
    () => l[1] ? [
      T(l[0]),
      l[1] ? T(l[1]) : Yt(u.value.partialRange)
    ] : [T(l[0])],
    u.value.enabled
  ) : T(l), S = () => {
    Array.isArray(a.value) && u.value.enabled && a.value.length === 1 && a.value.push(Yt(u.value.partialRange));
  }, X = () => {
    const l = a.value;
    return [
      oe(l[0]),
      l[1] ? oe(l[1]) : Yt(u.value.partialRange)
    ];
  }, O = () => a.value[1] ? X() : oe(Ne(a.value[0])), K = () => (a.value || []).map((l) => oe(l)), fe = (l = !1) => (l || S(), t.modelAuto ? O() : y.value.enabled ? K() : Array.isArray(a.value) ? ra(() => X(), u.value.enabled) : oe(Ne(a.value))), me = (l) => !l || Array.isArray(l) && !l.length ? null : t.timePicker ? k(Ne(l)) : t.monthPicker ? z(Ne(l)) : t.yearPicker ? f(Ne(l)) : y.value.enabled ? q(Ne(l)) : t.weekPicker ? ee(Ne(l)) : x(Ne(l)), v = (l) => {
    const D = me(l);
    za(Ne(D)) ? (a.value = Ne(D), W()) : (a.value = null, _.value = "");
  }, L = () => {
    const l = (D) => vt(D, n.value.format);
    return `${l(a.value[0])} ${n.value.rangeSeparator} ${a.value[1] ? l(a.value[1]) : ""}`;
  }, ne = () => r.value && a.value ? Array.isArray(a.value) ? L() : vt(a.value, n.value.format) : U(a.value), p = () => a.value ? y.value.enabled ? a.value.map((l) => U(l)).join("; ") : n.value.enabled && typeof n.value.format == "string" ? ne() : U(a.value) : "", W = () => {
    !t.format || typeof t.format == "string" || n.value.enabled && typeof n.value.format == "string" ? _.value = p() : _.value = t.format(a.value);
  }, T = (l) => {
    if (t.utc) {
      const D = new Date(l);
      return t.utc === "preserve" ? new Date(D.getTime() + D.getTimezoneOffset() * 6e4) : D;
    }
    return t.modelType ? wl.includes(t.modelType) ? m(new Date(l)) : t.modelType === "format" && (typeof t.format == "string" || !t.format) ? m(
      Na(l, i(), /* @__PURE__ */ new Date(), { locale: C.value })
    ) : m(
      Na(l, t.modelType, /* @__PURE__ */ new Date(), { locale: C.value })
    ) : m(new Date(l));
  }, oe = (l) => l ? t.utc ? _l(l, t.utc === "preserve", t.enableSeconds) : t.modelType ? t.modelType === "timestamp" ? +P(l) : t.modelType === "iso" ? P(l).toISOString() : t.modelType === "format" && (typeof t.format == "string" || !t.format) ? U(P(l)) : U(P(l), t.modelType, !0) : P(l) : "", $ = (l, D = !1, ue = !1) => {
    if (ue) return l;
    if (e("update:model-value", l), d.value.emitTimezone && D) {
      const M = Array.isArray(l) ? l.map((he) => et(Ne(he), d.value.emitTimezone)) : et(Ne(l), d.value.emitTimezone);
      e("update:model-timezone-value", M);
    }
  }, Y = (l) => Array.isArray(a.value) ? y.value.enabled ? a.value.map((D) => l(D)) : [
    l(a.value[0]),
    a.value[1] ? l(a.value[1]) : Yt(u.value.partialRange)
  ] : l(Ne(a.value)), g = () => {
    if (Array.isArray(a.value)) {
      const l = pt(a.value[0], t.weekStart), D = a.value[1] ? pt(a.value[1], t.weekStart) : [];
      return [l.map((ue) => j(ue)), D.map((ue) => j(ue))];
    }
    return pt(a.value, t.weekStart).map((l) => j(l));
  }, Z = (l, D) => $(Ne(Y(l)), !1, D), se = (l) => {
    const D = g();
    return l ? D : e("update:model-value", g());
  }, R = (l = !1) => (l || W(), t.monthPicker ? Z(H, l) : t.timePicker ? Z(N, l) : t.yearPicker ? Z(ye, l) : t.weekPicker ? se(l) : $(fe(l), !0, l));
  return {
    inputValue: _,
    internalModelValue: a,
    checkBeforeEmit: () => a.value ? u.value.enabled ? u.value.partialRange ? a.value.length >= 1 : a.value.length === 2 : !!a.value : !1,
    parseExternalModelValue: v,
    formatInputValue: W,
    emitModelValue: R
  };
}, lr = (e, t) => {
  const { defaultedFilters: r, propDates: a } = _e(e), { validateMonthYearInRange: n } = Tt(e), u = (c, C) => {
    let m = c;
    return r.value.months.includes($e(m)) ? (m = C ? Pt(c, 1) : qt(c, 1), u(m, C)) : m;
  }, d = (c, C) => {
    let m = c;
    return r.value.years.includes(ye(m)) ? (m = C ? Dn(c, 1) : Mn(c, 1), d(m, C)) : m;
  }, y = (c, C = !1) => {
    const m = Pe(j(), { month: e.month, year: e.year });
    let P = c ? Pt(m, 1) : qt(m, 1);
    e.disableYearSelect && (P = ft(P, e.year));
    let U = $e(P), N = ye(P);
    r.value.months.includes(U) && (P = u(P, c), U = $e(P), N = ye(P)), r.value.years.includes(N) && (P = d(P, c), N = ye(P)), n(U, N, c, e.preventMinMaxNavigation) && i(U, N, C);
  }, i = (c, C, m) => {
    t("update-month-year", { month: c, year: C, fromNav: m });
  }, _ = Q(() => (c) => En(
    Pe(j(), { month: e.month, year: e.year }),
    a.value.maxDate,
    a.value.minDate,
    e.preventMinMaxNavigation,
    c
  ));
  return { handleMonthYearChange: y, isDisabled: _, updateMonthYear: i };
}, va = {
  multiCalendars: { type: [Boolean, Number, String, Object], default: void 0 },
  modelValue: { type: [String, Date, Array, Object, Number], default: null },
  modelType: { type: String, default: null },
  position: { type: String, default: "center" },
  dark: { type: Boolean, default: !1 },
  format: {
    type: [String, Function],
    default: () => null
  },
  autoPosition: { type: [Boolean, String], default: !0 },
  altPosition: { type: Function, default: null },
  transitions: { type: [Boolean, Object], default: !0 },
  formatLocale: { type: Object, default: null },
  utc: { type: [Boolean, String], default: !1 },
  ariaLabels: { type: Object, default: () => ({}) },
  offset: { type: [Number, String], default: 10 },
  hideNavigation: { type: Array, default: () => [] },
  timezone: { type: [String, Object], default: null },
  vertical: { type: Boolean, default: !1 },
  disableMonthYearSelect: { type: Boolean, default: !1 },
  disableYearSelect: { type: Boolean, default: !1 },
  dayClass: {
    type: Function,
    default: null
  },
  yearRange: { type: Array, default: () => [1900, 2100] },
  enableTimePicker: { type: Boolean, default: !0 },
  autoApply: { type: Boolean, default: !1 },
  disabledDates: { type: [Array, Function], default: () => [] },
  monthNameFormat: { type: String, default: "short" },
  startDate: { type: [Date, String], default: null },
  startTime: { type: [Object, Array], default: null },
  hideOffsetDates: { type: Boolean, default: !1 },
  noToday: { type: Boolean, default: !1 },
  disabledWeekDays: { type: Array, default: () => [] },
  allowedDates: { type: Array, default: null },
  nowButtonLabel: { type: String, default: "Now" },
  markers: { type: Array, default: () => [] },
  escClose: { type: Boolean, default: !0 },
  spaceConfirm: { type: Boolean, default: !0 },
  monthChangeOnArrows: { type: Boolean, default: !0 },
  presetDates: { type: Array, default: () => [] },
  flow: { type: Array, default: () => [] },
  partialFlow: { type: Boolean, default: !1 },
  preventMinMaxNavigation: { type: Boolean, default: !1 },
  reverseYears: { type: Boolean, default: !1 },
  weekPicker: { type: Boolean, default: !1 },
  filters: { type: Object, default: () => ({}) },
  arrowNavigation: { type: Boolean, default: !1 },
  highlight: {
    type: [Function, Object],
    default: null
  },
  teleport: { type: [Boolean, String, Object], default: null },
  teleportCenter: { type: Boolean, default: !1 },
  locale: { type: String, default: "en-Us" },
  weekNumName: { type: String, default: "W" },
  weekStart: { type: [Number, String], default: 1 },
  weekNumbers: {
    type: [String, Function, Object],
    default: null
  },
  monthChangeOnScroll: { type: [Boolean, String], default: !0 },
  dayNames: {
    type: [Function, Array],
    default: null
  },
  monthPicker: { type: Boolean, default: !1 },
  customProps: { type: Object, default: null },
  yearPicker: { type: Boolean, default: !1 },
  modelAuto: { type: Boolean, default: !1 },
  selectText: { type: String, default: "Select" },
  cancelText: { type: String, default: "Cancel" },
  previewFormat: {
    type: [String, Function],
    default: () => ""
  },
  multiDates: { type: [Object, Boolean], default: !1 },
  ignoreTimeValidation: { type: Boolean, default: !1 },
  minDate: { type: [Date, String], default: null },
  maxDate: { type: [Date, String], default: null },
  minTime: { type: Object, default: null },
  maxTime: { type: Object, default: null },
  name: { type: String, default: null },
  placeholder: { type: String, default: "" },
  hideInputIcon: { type: Boolean, default: !1 },
  clearable: { type: Boolean, default: !0 },
  alwaysClearable: { type: Boolean, default: !1 },
  state: { type: Boolean, default: null },
  required: { type: Boolean, default: !1 },
  autocomplete: { type: String, default: "off" },
  timePicker: { type: Boolean, default: !1 },
  enableSeconds: { type: Boolean, default: !1 },
  is24: { type: Boolean, default: !0 },
  noHoursOverlay: { type: Boolean, default: !1 },
  noMinutesOverlay: { type: Boolean, default: !1 },
  noSecondsOverlay: { type: Boolean, default: !1 },
  hoursGridIncrement: { type: [String, Number], default: 1 },
  minutesGridIncrement: { type: [String, Number], default: 5 },
  secondsGridIncrement: { type: [String, Number], default: 5 },
  hoursIncrement: { type: [Number, String], default: 1 },
  minutesIncrement: { type: [Number, String], default: 1 },
  secondsIncrement: { type: [Number, String], default: 1 },
  range: { type: [Boolean, Object], default: !1 },
  uid: { type: String, default: null },
  disabled: { type: Boolean, default: !1 },
  readonly: { type: Boolean, default: !1 },
  inline: { type: [Boolean, Object], default: !1 },
  textInput: { type: [Boolean, Object], default: !1 },
  sixWeeks: { type: [Boolean, String], default: !1 },
  actionRow: { type: Object, default: () => ({}) },
  focusStartDate: { type: Boolean, default: !1 },
  disabledTimes: { type: [Function, Array], default: void 0 },
  timePickerInline: { type: Boolean, default: !1 },
  calendar: { type: Function, default: null },
  config: { type: Object, default: void 0 },
  quarterPicker: { type: Boolean, default: !1 },
  yearFirst: { type: Boolean, default: !1 },
  loading: { type: Boolean, default: !1 },
  onInternalModelChange: { type: [Function, Object], default: null },
  enableMinutes: { type: Boolean, default: !0 },
  ui: { type: Object, default: () => ({}) }
}, ct = {
  ...va,
  shadow: { type: Boolean, default: !1 },
  flowStep: { type: Number, default: 0 },
  internalModelValue: { type: [Date, Array], default: null },
  noOverlayFocus: { type: Boolean, default: !1 },
  collapse: { type: Boolean, default: !1 },
  menuWrapRef: { type: Object, default: null },
  getInputRect: { type: Function, default: () => ({}) },
  isTextInputDate: { type: Boolean, default: !1 },
  isMobile: { type: Boolean, default: void 0 }
}, rr = ["title"], or = ["disabled"], sr = /* @__PURE__ */ Ve({
  compatConfig: {
    MODE: 3
  },
  __name: "ActionRow",
  props: {
    menuMount: { type: Boolean, default: !1 },
    calendarWidth: { type: Number, default: 0 },
    ...ct
  },
  emits: ["close-picker", "select-date", "select-now", "invalid-select"],
  setup(e, { emit: t }) {
    const r = t, a = e, {
      defaultedActionRow: n,
      defaultedPreviewFormat: u,
      defaultedMultiCalendars: d,
      defaultedTextInput: y,
      defaultedInline: i,
      defaultedRange: _,
      defaultedMultiDates: c
    } = _e(a), { isTimeValid: C, isMonthValid: m } = Tt(a), { buildMatrix: P } = At(), U = te(null), N = te(null), H = te(!1), f = te({}), I = te(null), k = te(null);
    je(() => {
      a.arrowNavigation && P([Le(U), Le(N)], "actionRow"), z(), window.addEventListener("resize", z);
    }), xt(() => {
      window.removeEventListener("resize", z);
    });
    const z = () => {
      H.value = !1, setTimeout(() => {
        var ne, p;
        const v = (ne = I.value) == null ? void 0 : ne.getBoundingClientRect(), L = (p = k.value) == null ? void 0 : p.getBoundingClientRect();
        v && L && (f.value.maxWidth = `${L.width - v.width - 20}px`), H.value = !0;
      }, 0);
    }, q = Q(() => _.value.enabled && !_.value.partialRange && a.internalModelValue ? a.internalModelValue.length === 2 : !0), ee = Q(
      () => !C.value(a.internalModelValue) || !m.value(a.internalModelValue) || !q.value
    ), x = () => {
      const v = u.value;
      return a.timePicker || a.monthPicker, v(Ne(a.internalModelValue));
    }, S = () => {
      const v = a.internalModelValue;
      return d.value.count > 0 ? `${X(v[0])} - ${X(v[1])}` : [X(v[0]), X(v[1])];
    }, X = (v) => Nn(
      v,
      u.value,
      a.formatLocale,
      y.value.rangeSeparator,
      a.modelAuto,
      u.value
    ), O = Q(() => !a.internalModelValue || !a.menuMount ? "" : typeof u.value == "string" ? Array.isArray(a.internalModelValue) ? a.internalModelValue.length === 2 && a.internalModelValue[1] ? S() : c.value.enabled ? a.internalModelValue.map((v) => `${X(v)}`) : a.modelAuto ? `${X(a.internalModelValue[0])}` : `${X(a.internalModelValue[0])} -` : X(a.internalModelValue) : x()), K = () => c.value.enabled ? "; " : " - ", fe = Q(
      () => Array.isArray(O.value) ? O.value.join(K()) : O.value
    ), me = () => {
      C.value(a.internalModelValue) && m.value(a.internalModelValue) && q.value ? r("select-date") : r("invalid-select");
    };
    return (v, L) => (A(), V("div", {
      ref_key: "actionRowRef",
      ref: k,
      class: "dp__action_row"
    }, [
      v.$slots["action-row"] ? de(v.$slots, "action-row", ze(He({ key: 0 }, {
        internalModelValue: v.internalModelValue,
        disabled: ee.value,
        selectDate: () => v.$emit("select-date"),
        closePicker: () => v.$emit("close-picker")
      }))) : (A(), V(ke, { key: 1 }, [
        s(n).showPreview ? (A(), V("div", {
          key: 0,
          class: "dp__selection_preview",
          title: fe.value,
          style: rt(f.value)
        }, [
          v.$slots["action-preview"] && H.value ? de(v.$slots, "action-preview", {
            key: 0,
            value: v.internalModelValue
          }) : J("", !0),
          !v.$slots["action-preview"] && H.value ? (A(), V(ke, { key: 1 }, [
            gt(Qe(fe.value), 1)
          ], 64)) : J("", !0)
        ], 12, rr)) : J("", !0),
        ge("div", {
          ref_key: "actionBtnContainer",
          ref: I,
          class: "dp__action_buttons",
          "data-dp-element": "action-row"
        }, [
          v.$slots["action-buttons"] ? de(v.$slots, "action-buttons", {
            key: 0,
            value: v.internalModelValue
          }) : J("", !0),
          v.$slots["action-buttons"] ? J("", !0) : (A(), V(ke, { key: 1 }, [
            !s(i).enabled && s(n).showCancel ? (A(), V("button", {
              key: 0,
              ref_key: "cancelButtonRef",
              ref: U,
              type: "button",
              class: "dp__action_button dp__action_cancel",
              onClick: L[0] || (L[0] = (ne) => v.$emit("close-picker")),
              onKeydown: L[1] || (L[1] = (ne) => s(Ze)(ne, () => v.$emit("close-picker")))
            }, Qe(v.cancelText), 545)) : J("", !0),
            s(n).showNow ? (A(), V("button", {
              key: 1,
              type: "button",
              class: "dp__action_button dp__action_cancel",
              onClick: L[2] || (L[2] = (ne) => v.$emit("select-now")),
              onKeydown: L[3] || (L[3] = (ne) => s(Ze)(ne, () => v.$emit("select-now")))
            }, Qe(v.nowButtonLabel), 33)) : J("", !0),
            s(n).showSelect ? (A(), V("button", {
              key: 2,
              ref_key: "selectButtonRef",
              ref: N,
              type: "button",
              class: "dp__action_button dp__action_select",
              disabled: ee.value,
              "data-test-id": "select-button",
              onKeydown: L[4] || (L[4] = (ne) => s(Ze)(ne, () => me())),
              onClick: me
            }, Qe(v.selectText), 41, or)) : J("", !0)
          ], 64))
        ], 512)
      ], 64))
    ], 512));
  }
}), ur = ["role", "aria-label", "tabindex"], ir = { class: "dp__selection_grid_header" }, dr = ["aria-selected", "aria-disabled", "data-test-id", "onClick", "onKeydown", "onMouseover"], cr = ["aria-label"], ea = /* @__PURE__ */ Ve({
  __name: "SelectionOverlay",
  props: {
    items: {},
    type: {},
    isLast: { type: Boolean },
    arrowNavigation: { type: Boolean },
    skipButtonRef: { type: Boolean },
    headerRefs: {},
    hideNavigation: {},
    escClose: { type: Boolean },
    useRelative: { type: Boolean },
    height: {},
    textInput: { type: [Boolean, Object] },
    config: {},
    noOverlayFocus: { type: Boolean },
    focusValue: {},
    menuWrapRef: {},
    ariaLabels: {},
    overlayLabel: {}
  },
  emits: ["selected", "toggle", "reset-flow", "hover-value"],
  setup(e, { expose: t, emit: r }) {
    const { setSelectionGrid: a, buildMultiLevelMatrix: n, setMonthPicker: u } = At(), d = r, y = e, { defaultedAriaLabels: i, defaultedTextInput: _, defaultedConfig: c } = _e(
      y
    ), { hideNavigationButtons: C } = ya(), m = te(!1), P = te(null), U = te(null), N = te([]), H = te(), f = te(null), I = te(0), k = te(null);
    Xn(() => {
      P.value = null;
    }), je(() => {
      nt().then(() => K()), y.noOverlayFocus || q(), z(!0);
    }), xt(() => z(!1));
    const z = (Y) => {
      var g;
      y.arrowNavigation && ((g = y.headerRefs) != null && g.length ? u(Y) : a(Y));
    }, q = () => {
      var g;
      const Y = Le(U);
      Y && (_.value.enabled || (P.value ? (g = P.value) == null || g.focus({ preventScroll: !0 }) : Y.focus({ preventScroll: !0 })), m.value = Y.clientHeight < Y.scrollHeight);
    }, ee = Q(
      () => ({
        dp__overlay: !0,
        "dp--overlay-absolute": !y.useRelative,
        "dp--overlay-relative": y.useRelative
      })
    ), x = Q(
      () => y.useRelative ? { height: `${y.height}px`, width: "var(--dp-menu-min-width)" } : void 0
    ), S = Q(() => ({
      dp__overlay_col: !0
    })), X = Q(
      () => ({
        dp__btn: !0,
        dp__button: !0,
        dp__overlay_action: !0,
        dp__over_action_scroll: m.value,
        dp__button_bottom: y.isLast
      })
    ), O = Q(() => {
      var Y, g;
      return {
        dp__overlay_container: !0,
        dp__container_flex: ((Y = y.items) == null ? void 0 : Y.length) <= 6,
        dp__container_block: ((g = y.items) == null ? void 0 : g.length) > 6
      };
    });
    ot(
      () => y.items,
      () => K(!1),
      { deep: !0 }
    );
    const K = (Y = !0) => {
      nt().then(() => {
        const g = Le(P), Z = Le(U), se = Le(f), R = Le(k), ae = se ? se.getBoundingClientRect().height : 0;
        Z && (Z.getBoundingClientRect().height ? I.value = Z.getBoundingClientRect().height - ae : I.value = c.value.modeHeight - ae), g && R && Y && (R.scrollTop = g.offsetTop - R.offsetTop - (I.value / 2 - g.getBoundingClientRect().height) - ae);
      });
    }, fe = (Y) => {
      Y.disabled || d("selected", Y.value);
    }, me = () => {
      d("toggle"), d("reset-flow");
    }, v = () => {
      y.escClose && me();
    }, L = (Y, g, Z, se) => {
      Y && ((g.active || g.value === y.focusValue) && (P.value = Y), y.arrowNavigation && (Array.isArray(N.value[Z]) ? N.value[Z][se] = Y : N.value[Z] = [Y], ne()));
    }, ne = () => {
      var g, Z;
      const Y = (g = y.headerRefs) != null && g.length ? [y.headerRefs].concat(N.value) : N.value.concat([y.skipButtonRef ? [] : [f.value]]);
      n(Ne(Y), (Z = y.headerRefs) != null && Z.length ? "monthPicker" : "selectionGrid");
    }, p = (Y) => {
      y.arrowNavigation || Dt(Y, c.value, !0);
    }, W = (Y) => {
      H.value = Y, d("hover-value", Y);
    }, T = () => {
      if (me(), !y.isLast) {
        const Y = La(y.menuWrapRef ?? null, "action-row");
        if (Y) {
          const g = Rn(Y);
          g == null || g.focus();
        }
      }
    }, oe = (Y) => {
      switch (Y.key) {
        case Oe.esc:
          return v();
        case Oe.arrowLeft:
          return p(Y);
        case Oe.arrowRight:
          return p(Y);
        case Oe.arrowUp:
          return p(Y);
        case Oe.arrowDown:
          return p(Y);
        default:
          return;
      }
    }, $ = (Y) => {
      if (Y.key === Oe.enter) return me();
      if (Y.key === Oe.tab) return T();
    };
    return t({ focusGrid: q }), (Y, g) => {
      var Z;
      return A(), V("div", {
        ref_key: "gridWrapRef",
        ref: U,
        class: De(ee.value),
        style: rt(x.value),
        role: Y.useRelative ? void 0 : "dialog",
        "aria-label": Y.overlayLabel,
        tabindex: Y.useRelative ? void 0 : "0",
        onKeydown: oe,
        onClick: g[0] || (g[0] = Qt(() => {
        }, ["prevent"]))
      }, [
        ge("div", {
          ref_key: "containerRef",
          ref: k,
          class: De(O.value),
          style: rt({ "--dp-overlay-height": `${I.value}px` }),
          role: "grid"
        }, [
          ge("div", ir, [
            de(Y.$slots, "header")
          ]),
          Y.$slots.overlay ? de(Y.$slots, "overlay", { key: 0 }) : (A(!0), V(ke, { key: 1 }, Be(Y.items, (se, R) => (A(), V("div", {
            key: R,
            class: De(["dp__overlay_row", { dp__flex_row: Y.items.length >= 3 }]),
            role: "row"
          }, [
            (A(!0), V(ke, null, Be(se, (ae, l) => (A(), V("div", {
              key: ae.value,
              ref_for: !0,
              ref: (D) => L(D, ae, R, l),
              role: "gridcell",
              class: De(S.value),
              "aria-selected": ae.active || void 0,
              "aria-disabled": ae.disabled || void 0,
              tabindex: "0",
              "data-test-id": ae.text,
              onClick: Qt((D) => fe(ae), ["prevent"]),
              onKeydown: (D) => s(Ze)(D, () => fe(ae), !0),
              onMouseover: (D) => W(ae.value)
            }, [
              ge("div", {
                class: De(ae.className)
              }, [
                Y.$slots.item ? de(Y.$slots, "item", {
                  key: 0,
                  item: ae
                }) : J("", !0),
                Y.$slots.item ? J("", !0) : (A(), V(ke, { key: 1 }, [
                  gt(Qe(ae.text), 1)
                ], 64))
              ], 2)
            ], 42, dr))), 128))
          ], 2))), 128))
        ], 6),
        Y.$slots["button-icon"] ? sa((A(), V("button", {
          key: 0,
          ref_key: "toggleButton",
          ref: f,
          type: "button",
          "aria-label": (Z = s(i)) == null ? void 0 : Z.toggleOverlay,
          class: De(X.value),
          tabindex: "0",
          onClick: me,
          onKeydown: $
        }, [
          de(Y.$slots, "button-icon")
        ], 42, cr)), [
          [ua, !s(C)(Y.hideNavigation, Y.type)]
        ]) : J("", !0)
      ], 46, ur);
    };
  }
}), fr = ["data-dp-mobile"], ma = /* @__PURE__ */ Ve({
  __name: "InstanceWrap",
  props: {
    multiCalendars: {},
    stretch: { type: Boolean },
    collapse: { type: Boolean },
    isMobile: { type: Boolean }
  },
  setup(e) {
    const t = e, r = Q(
      () => t.multiCalendars > 0 ? [...Array(t.multiCalendars).keys()] : [0]
    ), a = Q(() => ({
      dp__instance_calendar: t.multiCalendars > 0
    }));
    return (n, u) => (A(), V("div", {
      class: De({
        dp__menu_inner: !n.stretch,
        "dp--menu--inner-stretched": n.stretch,
        dp__flex_display: n.multiCalendars > 0,
        "dp--flex-display-collapsed": n.collapse
      }),
      "data-dp-mobile": n.isMobile
    }, [
      (A(!0), V(ke, null, Be(r.value, (d, y) => (A(), V("div", {
        key: d,
        class: De(a.value)
      }, [
        de(n.$slots, "default", {
          instance: d,
          index: y
        })
      ], 2))), 128))
    ], 10, fr));
  }
}), vr = ["data-dp-element", "aria-label", "aria-disabled"], Kt = /* @__PURE__ */ Ve({
  compatConfig: {
    MODE: 3
  },
  __name: "ArrowBtn",
  props: {
    ariaLabel: {},
    elName: {},
    disabled: { type: Boolean }
  },
  emits: ["activate", "set-ref"],
  setup(e, { emit: t }) {
    const r = t, a = te(null);
    return je(() => r("set-ref", a)), (n, u) => (A(), V("button", {
      ref_key: "elRef",
      ref: a,
      type: "button",
      "data-dp-element": n.elName,
      class: "dp__btn dp--arrow-btn-nav",
      tabindex: "0",
      "aria-label": n.ariaLabel,
      "aria-disabled": n.disabled || void 0,
      onClick: u[0] || (u[0] = (d) => n.$emit("activate")),
      onKeydown: u[1] || (u[1] = (d) => s(Ze)(d, () => n.$emit("activate"), !0))
    }, [
      ge("span", {
        class: De(["dp__inner_nav", { dp__inner_nav_disabled: n.disabled }])
      }, [
        de(n.$slots, "default")
      ], 2)
    ], 40, vr));
  }
}), mr = ["aria-label", "data-test-id"], zn = /* @__PURE__ */ Ve({
  __name: "YearModePicker",
  props: {
    ...ct,
    showYearPicker: { type: Boolean, default: !1 },
    items: { type: Array, default: () => [] },
    instance: { type: Number, default: 0 },
    year: { type: Number, default: 0 },
    isDisabled: { type: Function, default: () => !1 }
  },
  emits: ["toggle-year-picker", "year-select", "handle-year"],
  setup(e, { emit: t }) {
    const r = t, a = e, { showRightIcon: n, showLeftIcon: u } = ya(), { defaultedConfig: d, defaultedMultiCalendars: y, defaultedAriaLabels: i, defaultedTransitions: _, defaultedUI: c } = _e(a), { showTransition: C, transitionName: m } = ta(_), P = te(!1), U = (f = !1, I) => {
      P.value = !P.value, r("toggle-year-picker", { flow: f, show: I });
    }, N = (f) => {
      P.value = !1, r("year-select", f);
    }, H = (f = !1) => {
      r("handle-year", f);
    };
    return (f, I) => {
      var k, z, q, ee, x;
      return A(), V(ke, null, [
        ge("div", {
          class: De(["dp--year-mode-picker", { "dp--hidden-el": P.value }])
        }, [
          s(u)(s(y), e.instance) ? (A(), Te(Kt, {
            key: 0,
            ref: "mpPrevIconRef",
            "aria-label": (k = s(i)) == null ? void 0 : k.prevYear,
            disabled: e.isDisabled(!1),
            class: De((z = s(c)) == null ? void 0 : z.navBtnPrev),
            onActivate: I[0] || (I[0] = (S) => H(!1))
          }, {
            default: we(() => [
              f.$slots["arrow-left"] ? de(f.$slots, "arrow-left", { key: 0 }) : J("", !0),
              f.$slots["arrow-left"] ? J("", !0) : (A(), Te(s(Wa), { key: 1 }))
            ]),
            _: 3
          }, 8, ["aria-label", "disabled", "class"])) : J("", !0),
          ge("button", {
            ref: "mpYearButtonRef",
            class: "dp__btn dp--year-select",
            type: "button",
            "aria-label": `${e.year}-${(q = s(i)) == null ? void 0 : q.openYearsOverlay}`,
            "data-test-id": `year-mode-btn-${e.instance}`,
            onClick: I[1] || (I[1] = () => U(!1)),
            onKeydown: I[2] || (I[2] = Jn(() => U(!1), ["enter"]))
          }, [
            f.$slots.year ? de(f.$slots, "year", {
              key: 0,
              year: e.year
            }) : J("", !0),
            f.$slots.year ? J("", !0) : (A(), V(ke, { key: 1 }, [
              gt(Qe(e.year), 1)
            ], 64))
          ], 40, mr),
          s(n)(s(y), e.instance) ? (A(), Te(Kt, {
            key: 1,
            ref: "mpNextIconRef",
            "aria-label": (ee = s(i)) == null ? void 0 : ee.nextYear,
            disabled: e.isDisabled(!0),
            class: De((x = s(c)) == null ? void 0 : x.navBtnNext),
            onActivate: I[3] || (I[3] = (S) => H(!0))
          }, {
            default: we(() => [
              f.$slots["arrow-right"] ? de(f.$slots, "arrow-right", { key: 0 }) : J("", !0),
              f.$slots["arrow-right"] ? J("", !0) : (A(), Te(s(Va), { key: 1 }))
            ]),
            _: 3
          }, 8, ["aria-label", "disabled", "class"])) : J("", !0)
        ], 2),
        at(Ut, {
          name: s(m)(e.showYearPicker),
          css: s(C)
        }, {
          default: we(() => {
            var S, X;
            return [
              e.showYearPicker ? (A(), Te(ea, {
                key: 0,
                items: e.items,
                "text-input": f.textInput,
                "esc-close": f.escClose,
                config: f.config,
                "is-last": f.autoApply && !s(d).keepActionRow,
                "hide-navigation": f.hideNavigation,
                "aria-labels": f.ariaLabels,
                "overlay-label": (X = (S = s(i)) == null ? void 0 : S.yearPicker) == null ? void 0 : X.call(S, !0),
                type: "year",
                onToggle: U,
                onSelected: I[4] || (I[4] = (O) => N(O))
              }, qe({
                "button-icon": we(() => [
                  f.$slots["calendar-icon"] ? de(f.$slots, "calendar-icon", { key: 0 }) : J("", !0),
                  f.$slots["calendar-icon"] ? J("", !0) : (A(), Te(s(Wt), { key: 1 }))
                ]),
                _: 2
              }, [
                f.$slots["year-overlay-value"] ? {
                  name: "item",
                  fn: we(({ item: O }) => [
                    de(f.$slots, "year-overlay-value", {
                      text: O.text,
                      value: O.value
                    })
                  ]),
                  key: "0"
                } : void 0
              ]), 1032, ["items", "text-input", "esc-close", "config", "is-last", "hide-navigation", "aria-labels", "overlay-label"])) : J("", !0)
            ];
          }),
          _: 3
        }, 8, ["name", "css"])
      ], 64);
    };
  }
}), xa = (e, t, r) => {
  if (t.value && Array.isArray(t.value))
    if (t.value.some((a) => Ae(e, a))) {
      const a = t.value.filter((n) => !Ae(n, e));
      t.value = a.length ? a : null;
    } else (r && +r > t.value.length || !r) && t.value.push(e);
  else
    t.value = [e];
}, en = (e, t, r) => {
  let a = e.value ? e.value.slice() : [];
  return a.length === 2 && a[1] !== null && (a = []), a.length ? (Ye(t, a[0]) ? a.unshift(t) : a[1] = t, r("range-end", t)) : (a = [t], r("range-start", t)), a;
}, pa = (e, t, r, a) => {
  e && (e[0] && e[1] && r && t("auto-apply"), e[0] && !e[1] && a && r && t("auto-apply"));
}, Hn = (e) => {
  Array.isArray(e.value) && e.value.length <= 2 && e.range ? e.modelValue.value = e.value.map((t) => et(j(t), e.timezone)) : Array.isArray(e.value) || (e.modelValue.value = et(j(e.value), e.timezone));
}, Un = (e, t, r, a) => Array.isArray(t.value) && (t.value.length === 2 || t.value.length === 1 && a.value.partialRange) ? a.value.fixedStart && (Ee(e, t.value[0]) || Ae(e, t.value[0])) ? [t.value[0], e] : a.value.fixedEnd && (Ye(e, t.value[1]) || Ae(e, t.value[1])) ? [e, t.value[1]] : (r("invalid-fixed-range", e), t.value) : [], Wn = ({
  multiCalendars: e,
  range: t,
  highlight: r,
  propDates: a,
  calendars: n,
  modelValue: u,
  props: d,
  filters: y,
  year: i,
  month: _,
  emit: c
}) => {
  const C = Q(() => qa(d.yearRange, d.locale, d.reverseYears)), m = te([!1]), P = Q(() => (O, K) => {
    const fe = Pe(dt(/* @__PURE__ */ new Date()), {
      month: _.value(O),
      year: i.value(O)
    }), me = K ? $n(fe) : da(fe);
    return En(
      me,
      a.value.maxDate,
      a.value.minDate,
      d.preventMinMaxNavigation,
      K
    );
  }), U = () => Array.isArray(u.value) && e.value.solo && u.value[1], N = () => {
    for (let O = 0; O < e.value.count; O++)
      if (O === 0)
        n.value[O] = n.value[0];
      else if (O === e.value.count - 1 && U())
        n.value[O] = {
          month: $e(u.value[1]),
          year: ye(u.value[1])
        };
      else {
        const K = Pe(j(), n.value[O - 1]);
        n.value[O] = { month: $e(K), year: ye(Dn(K, 1)) };
      }
  }, H = (O) => {
    if (!O) return N();
    const K = Pe(j(), n.value[O]);
    return n.value[0].year = ye(Mn(K, e.value.count - 1)), N();
  }, f = (O, K) => {
    const fe = dl(K, O);
    return t.value.showLastInRange && fe > 1 ? K : O;
  }, I = (O) => d.focusStartDate || e.value.solo ? O[0] : O[1] ? f(O[0], O[1]) : O[0], k = () => {
    if (u.value) {
      const O = Array.isArray(u.value) ? I(u.value) : u.value;
      n.value[0] = { month: $e(O), year: ye(O) };
    }
  }, z = () => {
    k(), e.value.count && N();
  };
  ot(u, (O, K) => {
    d.isTextInputDate && JSON.stringify(O ?? {}) !== JSON.stringify(K ?? {}) && z();
  }), je(() => {
    z();
  });
  const q = (O, K) => {
    n.value[K].year = O, c("update-month-year", { instance: K, year: O, month: n.value[K].month }), e.value.count && !e.value.solo && H(K);
  }, ee = Q(() => (O) => zt(C.value, (K) => {
    var L;
    const fe = i.value(O) === K.value, me = Xt(
      K.value,
      Ht(a.value.minDate),
      Ht(a.value.maxDate)
    ) || ((L = y.value.years) == null ? void 0 : L.includes(i.value(O))), v = Za(r.value, K.value);
    return { active: fe, disabled: me, highlighted: v };
  })), x = (O, K) => {
    q(O, K), X(K);
  }, S = (O, K = !1) => {
    if (!P.value(O, K)) {
      const fe = K ? i.value(O) + 1 : i.value(O) - 1;
      q(fe, O);
    }
  }, X = (O, K = !1, fe) => {
    K || c("reset-flow"), fe !== void 0 ? m.value[O] = fe : m.value[O] = !m.value[O], m.value[O] ? c("overlay-toggle", { open: !0, overlay: Ge.year }) : (c("overlay-closed"), c("overlay-toggle", { open: !1, overlay: Ge.year }));
  };
  return {
    isDisabled: P,
    groupedYears: ee,
    showYearPicker: m,
    selectYear: q,
    toggleYearPicker: X,
    handleYearSelect: x,
    handleYear: S
  };
}, pr = (e, t) => {
  const {
    defaultedMultiCalendars: r,
    defaultedAriaLabels: a,
    defaultedTransitions: n,
    defaultedConfig: u,
    defaultedRange: d,
    defaultedHighlight: y,
    propDates: i,
    defaultedTz: _,
    defaultedFilters: c,
    defaultedMultiDates: C
  } = _e(e), m = () => {
    e.isTextInputDate && z(ye(j(e.startDate)), 0);
  }, { modelValue: P, year: U, month: N, calendars: H } = aa(e, t, m), f = Q(() => Sn(e.formatLocale, e.locale, e.monthNameFormat)), I = te(null), { checkMinMaxRange: k } = Tt(e), {
    selectYear: z,
    groupedYears: q,
    showYearPicker: ee,
    toggleYearPicker: x,
    handleYearSelect: S,
    handleYear: X,
    isDisabled: O
  } = Wn({
    modelValue: P,
    multiCalendars: r,
    range: d,
    highlight: y,
    calendars: H,
    year: U,
    propDates: i,
    month: N,
    filters: c,
    props: e,
    emit: t
  });
  je(() => {
    e.startDate && (P.value && e.focusStartDate || !P.value) && z(ye(j(e.startDate)), 0);
  });
  const K = (R) => R ? { month: $e(R), year: ye(R) } : { month: null, year: null }, fe = () => P.value ? Array.isArray(P.value) ? P.value.map((R) => K(R)) : K(P.value) : K(), me = (R, ae) => {
    const l = H.value[R], D = fe();
    return Array.isArray(D) ? D.some((ue) => ue.year === (l == null ? void 0 : l.year) && ue.month === ae) : (l == null ? void 0 : l.year) === D.year && ae === D.month;
  }, v = (R, ae, l) => {
    var ue, M;
    const D = fe();
    return Array.isArray(D) ? U.value(ae) === ((ue = D[l]) == null ? void 0 : ue.year) && R === ((M = D[l]) == null ? void 0 : M.month) : !1;
  }, L = (R, ae) => {
    if (d.value.enabled) {
      const l = fe();
      if (Array.isArray(P.value) && Array.isArray(l)) {
        const D = v(R, ae, 0) || v(R, ae, 1), ue = yt(dt(j()), R, U.value(ae));
        return Jt(P.value, I.value, ue) && !D;
      }
      return !1;
    }
    return !1;
  }, ne = Q(() => (R) => zt(f.value, (ae) => {
    var he;
    const l = me(R, ae.value), D = Xt(
      ae.value,
      Bn(U.value(R), i.value.minDate),
      _n(U.value(R), i.value.maxDate)
    ) || Fl(i.value.disabledDates, U.value(R), ae.value) || ((he = c.value.months) == null ? void 0 : he.includes(ae.value)) || !Ll(i.value.allowedDates, U.value(R), ae.value), ue = L(ae.value, R), M = Fn(y.value, ae.value, U.value(R));
    return { active: l, disabled: D, isBetween: ue, highlighted: M };
  })), p = (R, ae) => yt(dt(j()), R, U.value(ae)), W = (R, ae) => {
    const l = P.value ? P.value : dt(/* @__PURE__ */ new Date());
    P.value = yt(l, R, U.value(ae)), t("auto-apply"), t("update-flow-step");
  }, T = (R, ae) => {
    const l = p(R, ae);
    d.value.fixedEnd || d.value.fixedStart ? P.value = Un(l, P, t, d) : P.value ? k(l, P.value) && (P.value = en(P, p(R, ae), t)) : P.value = [p(R, ae)], nt().then(() => {
      pa(P.value, t, e.autoApply, e.modelAuto);
    });
  }, oe = (R, ae) => {
    xa(p(R, ae), P, C.value.limit), t("auto-apply", !0);
  }, $ = (R, ae) => (H.value[ae].month = R, g(ae, H.value[ae].year, R), C.value.enabled ? oe(R, ae) : d.value.enabled ? T(R, ae) : W(R, ae)), Y = (R, ae) => {
    z(R, ae), g(ae, R, null);
  }, g = (R, ae, l) => {
    let D = l;
    if (!D && D !== 0) {
      const ue = fe();
      D = Array.isArray(ue) ? ue[R].month : ue.month;
    }
    t("update-month-year", { instance: R, year: ae, month: D });
  };
  return {
    groupedMonths: ne,
    groupedYears: q,
    year: U,
    isDisabled: O,
    defaultedMultiCalendars: r,
    defaultedAriaLabels: a,
    defaultedTransitions: n,
    defaultedConfig: u,
    showYearPicker: ee,
    modelValue: P,
    presetDate: (R, ae) => {
      Hn({
        value: R,
        modelValue: P,
        range: d.value.enabled,
        timezone: ae ? void 0 : _.value.timezone
      }), t("auto-apply");
    },
    setHoverDate: (R, ae) => {
      I.value = p(R, ae);
    },
    selectMonth: $,
    selectYear: Y,
    toggleYearPicker: x,
    handleYearSelect: S,
    handleYear: X,
    getModelMonthYear: fe
  };
}, yr = /* @__PURE__ */ Ve({
  compatConfig: {
    MODE: 3
  },
  __name: "MonthPicker",
  props: {
    ...ct
  },
  emits: [
    "update:internal-model-value",
    "overlay-closed",
    "reset-flow",
    "range-start",
    "range-end",
    "auto-apply",
    "update-month-year",
    "update-flow-step",
    "mount",
    "invalid-fixed-range",
    "overlay-toggle"
  ],
  setup(e, { expose: t, emit: r }) {
    const a = r, n = Bt(), u = tt(n, "yearMode"), d = e;
    je(() => {
      d.shadow || a("mount", null);
    });
    const {
      groupedMonths: y,
      groupedYears: i,
      year: _,
      isDisabled: c,
      defaultedMultiCalendars: C,
      defaultedConfig: m,
      showYearPicker: P,
      modelValue: U,
      presetDate: N,
      setHoverDate: H,
      selectMonth: f,
      selectYear: I,
      toggleYearPicker: k,
      handleYearSelect: z,
      handleYear: q,
      getModelMonthYear: ee
    } = pr(d, a);
    return t({ getSidebarProps: () => ({
      modelValue: U,
      year: _,
      getModelMonthYear: ee,
      selectMonth: f,
      selectYear: I,
      handleYear: q
    }), presetDate: N, toggleYearPicker: (S) => k(0, S) }), (S, X) => (A(), Te(ma, {
      "multi-calendars": s(C).count,
      collapse: S.collapse,
      stretch: "",
      "is-mobile": S.isMobile
    }, {
      default: we(({ instance: O }) => [
        S.$slots["top-extra"] ? de(S.$slots, "top-extra", {
          key: 0,
          value: S.internalModelValue
        }) : J("", !0),
        S.$slots["month-year"] ? de(S.$slots, "month-year", ze(He({ key: 1 }, {
          year: s(_),
          months: s(y)(O),
          years: s(i)(O),
          selectMonth: s(f),
          selectYear: s(I),
          instance: O
        }))) : (A(), Te(ea, {
          key: 2,
          items: s(y)(O),
          "arrow-navigation": S.arrowNavigation,
          "is-last": S.autoApply && !s(m).keepActionRow,
          "esc-close": S.escClose,
          height: s(m).modeHeight,
          config: S.config,
          "no-overlay-focus": !!(S.noOverlayFocus || S.textInput),
          "use-relative": "",
          type: "month",
          onSelected: (K) => s(f)(K, O),
          onHoverValue: (K) => s(H)(K, O)
        }, qe({
          header: we(() => [
            at(zn, He(S.$props, {
              items: s(i)(O),
              instance: O,
              "show-year-picker": s(P)[O],
              year: s(_)(O),
              "is-disabled": (K) => s(c)(O, K),
              onHandleYear: (K) => s(q)(O, K),
              onYearSelect: (K) => s(z)(K, O),
              onToggleYearPicker: (K) => s(k)(O, K == null ? void 0 : K.flow, K == null ? void 0 : K.show)
            }), qe({ _: 2 }, [
              Be(s(u), (K, fe) => ({
                name: K,
                fn: we((me) => [
                  de(S.$slots, K, ze(xe(me)))
                ])
              }))
            ]), 1040, ["items", "instance", "show-year-picker", "year", "is-disabled", "onHandleYear", "onYearSelect", "onToggleYearPicker"])
          ]),
          _: 2
        }, [
          S.$slots["month-overlay-value"] ? {
            name: "item",
            fn: we(({ item: K }) => [
              de(S.$slots, "month-overlay-value", {
                text: K.text,
                value: K.value
              })
            ]),
            key: "0"
          } : void 0
        ]), 1032, ["items", "arrow-navigation", "is-last", "esc-close", "height", "config", "no-overlay-focus", "onSelected", "onHoverValue"]))
      ]),
      _: 3
    }, 8, ["multi-calendars", "collapse", "is-mobile"]));
  }
}), gr = (e, t) => {
  const r = () => {
    e.isTextInputDate && (c.value = ye(j(e.startDate)));
  }, { modelValue: a } = aa(e, t, r), n = te(null), { defaultedHighlight: u, defaultedMultiDates: d, defaultedFilters: y, defaultedRange: i, propDates: _ } = _e(e), c = te();
  je(() => {
    e.startDate && (a.value && e.focusStartDate || !a.value) && (c.value = ye(j(e.startDate)));
  });
  const C = (k) => Array.isArray(a.value) ? a.value.some((z) => ye(z) === k) : a.value ? ye(a.value) === k : !1, m = (k) => i.value.enabled && Array.isArray(a.value) ? Jt(a.value, n.value, H(k)) : !1, P = (k) => _.value.allowedDates instanceof Map ? _.value.allowedDates.size ? _.value.allowedDates.has(`${k}`) : !1 : !0, U = (k) => _.value.disabledDates instanceof Map ? _.value.disabledDates.size ? _.value.disabledDates.has(`${k}`) : !1 : !0, N = Q(() => zt(qa(e.yearRange, e.locale, e.reverseYears), (k) => {
    const z = C(k.value), q = Xt(
      k.value,
      Ht(_.value.minDate),
      Ht(_.value.maxDate)
    ) || y.value.years.includes(k.value) || !P(k.value) || U(k.value), ee = m(k.value) && !z, x = Za(u.value, k.value);
    return { active: z, disabled: q, isBetween: ee, highlighted: x };
  })), H = (k) => ft(dt(da(/* @__PURE__ */ new Date())), k);
  return {
    groupedYears: N,
    modelValue: a,
    focusYear: c,
    setHoverValue: (k) => {
      n.value = ft(dt(/* @__PURE__ */ new Date()), k);
    },
    selectYear: (k) => {
      var z;
      if (t("update-month-year", { instance: 0, year: k }), d.value.enabled)
        return a.value ? Array.isArray(a.value) && (((z = a.value) == null ? void 0 : z.map((ee) => ye(ee))).includes(k) ? a.value = a.value.filter((ee) => ye(ee) !== k) : a.value.push(ft(We(j()), k))) : a.value = [ft(We(da(j())), k)], t("auto-apply", !0);
      i.value.enabled ? (a.value = en(a, H(k), t), nt().then(() => {
        pa(a.value, t, e.autoApply, e.modelAuto);
      })) : (a.value = H(k), t("auto-apply"));
    }
  };
}, hr = /* @__PURE__ */ Ve({
  compatConfig: {
    MODE: 3
  },
  __name: "YearPicker",
  props: {
    ...ct
  },
  emits: [
    "update:internal-model-value",
    "reset-flow",
    "range-start",
    "range-end",
    "auto-apply",
    "update-month-year"
  ],
  setup(e, { expose: t, emit: r }) {
    const a = r, n = e, { groupedYears: u, modelValue: d, focusYear: y, selectYear: i, setHoverValue: _ } = gr(n, a), { defaultedConfig: c } = _e(n);
    return t({ getSidebarProps: () => ({
      modelValue: d,
      selectYear: i
    }) }), (m, P) => (A(), V("div", null, [
      m.$slots["top-extra"] ? de(m.$slots, "top-extra", {
        key: 0,
        value: m.internalModelValue
      }) : J("", !0),
      m.$slots["month-year"] ? de(m.$slots, "month-year", ze(He({ key: 1 }, {
        years: s(u),
        selectYear: s(i)
      }))) : (A(), Te(ea, {
        key: 2,
        items: s(u),
        "is-last": m.autoApply && !s(c).keepActionRow,
        height: s(c).modeHeight,
        config: m.config,
        "no-overlay-focus": !!(m.noOverlayFocus || m.textInput),
        "focus-value": s(y),
        type: "year",
        "use-relative": "",
        onSelected: s(i),
        onHoverValue: s(_)
      }, qe({ _: 2 }, [
        m.$slots["year-overlay-value"] ? {
          name: "item",
          fn: we(({ item: U }) => [
            de(m.$slots, "year-overlay-value", {
              text: U.text,
              value: U.value
            })
          ]),
          key: "0"
        } : void 0
      ]), 1032, ["items", "is-last", "height", "config", "no-overlay-focus", "focus-value", "onSelected", "onHoverValue"]))
    ]));
  }
}), br = {
  key: 0,
  class: "dp__time_input"
}, kr = ["data-compact", "data-collapsed"], wr = ["data-test-id", "aria-label", "onKeydown", "onClick", "onMousedown"], Dr = ["aria-label", "disabled", "data-test-id", "onKeydown", "onClick"], Mr = ["data-test-id", "aria-label", "onKeydown", "onClick", "onMousedown"], $r = { key: 0 }, Ar = ["aria-label", "data-compact"], Tr = /* @__PURE__ */ Ve({
  compatConfig: {
    MODE: 3
  },
  __name: "TimeInput",
  props: {
    hours: { type: Number, default: 0 },
    minutes: { type: Number, default: 0 },
    seconds: { type: Number, default: 0 },
    closeTimePickerBtn: { type: Object, default: null },
    order: { type: Number, default: 0 },
    disabledTimesConfig: { type: Function, default: null },
    validateTime: { type: Function, default: () => !1 },
    ...ct
  },
  emits: [
    "set-hours",
    "set-minutes",
    "update:hours",
    "update:minutes",
    "update:seconds",
    "reset-flow",
    "mounted",
    "overlay-closed",
    "overlay-opened",
    "am-pm-change"
  ],
  setup(e, { expose: t, emit: r }) {
    const a = r, n = e, { setTimePickerElements: u, setTimePickerBackRef: d } = At(), {
      defaultedAriaLabels: y,
      defaultedTransitions: i,
      defaultedFilters: _,
      defaultedConfig: c,
      defaultedRange: C,
      defaultedMultiCalendars: m
    } = _e(n), { transitionName: P, showTransition: U } = ta(i), N = Zt({
      hours: !1,
      minutes: !1,
      seconds: !1
    }), H = te("AM"), f = te(null), I = te([]), k = te(), z = te(!1);
    je(() => {
      a("mounted");
    });
    const q = (o) => Pe(/* @__PURE__ */ new Date(), {
      hours: o.hours,
      minutes: o.minutes,
      seconds: n.enableSeconds ? o.seconds : 0,
      milliseconds: 0
    }), ee = Q(
      () => (o) => T(o, n[o]) || S(o, n[o])
    ), x = Q(() => ({ hours: n.hours, minutes: n.minutes, seconds: n.seconds })), S = (o, E) => C.value.enabled && !C.value.disableTimeRangeValidation ? !n.validateTime(o, E) : !1, X = (o, E) => {
      if (C.value.enabled && !C.value.disableTimeRangeValidation) {
        const ce = E ? +n[`${o}Increment`] : -+n[`${o}Increment`], B = n[o] + ce;
        return !n.validateTime(o, B);
      }
      return !1;
    }, O = Q(() => (o) => !Z(+n[o] + +n[`${o}Increment`], o) || X(o, !0)), K = Q(() => (o) => !Z(+n[o] - +n[`${o}Increment`], o) || X(o, !1)), fe = (o, E) => An(Pe(j(), o), E), me = (o, E) => cl(Pe(j(), o), E), v = Q(
      () => ({
        dp__time_col: !0,
        dp__time_col_block: !n.timePickerInline,
        dp__time_col_reg_block: !n.enableSeconds && n.is24 && !n.timePickerInline,
        dp__time_col_reg_inline: !n.enableSeconds && n.is24 && n.timePickerInline,
        dp__time_col_reg_with_button: !n.enableSeconds && !n.is24,
        dp__time_col_sec: n.enableSeconds && n.is24,
        dp__time_col_sec_with_button: n.enableSeconds && !n.is24
      })
    ), L = Q(
      () => n.timePickerInline && C.value.enabled && !m.value.count
    ), ne = Q(() => {
      const o = [{ type: "hours" }];
      return n.enableMinutes && o.push({ type: "", separator: !0 }, {
        type: "minutes"
      }), n.enableSeconds && o.push({ type: "", separator: !0 }, {
        type: "seconds"
      }), o;
    }), p = Q(() => ne.value.filter((o) => !o.separator)), W = Q(() => (o) => {
      if (o === "hours") {
        const E = ue(+n.hours);
        return { text: E < 10 ? `0${E}` : `${E}`, value: E };
      }
      return { text: n[o] < 10 ? `0${n[o]}` : `${n[o]}`, value: n[o] };
    }), T = (o, E) => {
      var B;
      if (!n.disabledTimesConfig) return !1;
      const ce = n.disabledTimesConfig(n.order, o === "hours" ? E : void 0);
      return ce[o] ? !!((B = ce[o]) != null && B.includes(E)) : !0;
    }, oe = (o, E) => E !== "hours" || H.value === "AM" ? o : o + 12, $ = (o) => {
      const E = n.is24 ? 24 : 12, ce = o === "hours" ? E : 60, B = +n[`${o}GridIncrement`], Me = o === "hours" && !n.is24 ? B : 0, be = [];
      for (let Se = Me; Se < ce; Se += B)
        be.push({ value: n.is24 ? Se : oe(Se, o), text: Se < 10 ? `0${Se}` : `${Se}` });
      return o === "hours" && !n.is24 && be.unshift({ value: H.value === "PM" ? 12 : 0, text: "12" }), zt(be, (Se) => ({ active: !1, disabled: _.value.times[o].includes(Se.value) || !Z(Se.value, o) || T(o, Se.value) || S(o, Se.value) }));
    }, Y = (o) => o >= 0 ? o : 59, g = (o) => o >= 0 ? o : 23, Z = (o, E) => {
      const ce = n.minTime ? q(Sa(n.minTime)) : null, B = n.maxTime ? q(Sa(n.maxTime)) : null, Me = q(
        Sa(
          x.value,
          E,
          E === "minutes" || E === "seconds" ? Y(o) : g(o)
        )
      );
      return ce && B ? (Ft(Me, B) || Et(Me, B)) && (Ot(Me, ce) || Et(Me, ce)) : ce ? Ot(Me, ce) || Et(Me, ce) : B ? Ft(Me, B) || Et(Me, B) : !0;
    }, se = (o) => n[`no${o[0].toUpperCase() + o.slice(1)}Overlay`], R = (o) => {
      se(o) || (N[o] = !N[o], N[o] ? (z.value = !0, a("overlay-opened", o)) : (z.value = !1, a("overlay-closed", o)));
    }, ae = (o) => o === "hours" ? ht : o === "minutes" ? $t : Lt, l = () => {
      k.value && clearTimeout(k.value);
    }, D = (o, E = !0, ce) => {
      const B = E ? fe : me, Me = E ? +n[`${o}Increment`] : -+n[`${o}Increment`];
      Z(+n[o] + Me, o) && a(
        `update:${o}`,
        ae(o)(B({ [o]: +n[o] }, { [o]: +n[`${o}Increment`] }))
      ), !(ce != null && ce.keyboard) && c.value.timeArrowHoldThreshold && (k.value = setTimeout(() => {
        D(o, E);
      }, c.value.timeArrowHoldThreshold));
    }, ue = (o) => n.is24 ? o : (o >= 12 ? H.value = "PM" : H.value = "AM", $l(o)), M = () => {
      H.value === "PM" ? (H.value = "AM", a("update:hours", n.hours - 12)) : (H.value = "PM", a("update:hours", n.hours + 12)), a("am-pm-change", H.value);
    }, he = (o) => {
      N[o] = !0;
    }, pe = (o, E, ce) => {
      if (o && n.arrowNavigation) {
        Array.isArray(I.value[E]) ? I.value[E][ce] = o : I.value[E] = [o];
        const B = I.value.reduce(
          (Me, be) => be.map((Se, b) => [...Me[b] || [], be[b]]),
          []
        );
        d(n.closeTimePickerBtn), f.value && (B[1] = B[1].concat(f.value)), u(B, n.order);
      }
    }, re = (o, E) => (R(o), a(`update:${o}`, E));
    return t({ openChildCmp: he }), (o, E) => {
      var ce;
      return o.disabled ? J("", !0) : (A(), V("div", br, [
        (A(!0), V(ke, null, Be(ne.value, (B, Me) => {
          var be, Se, b;
          return A(), V("div", {
            key: Me,
            class: De(v.value),
            "data-compact": L.value && !o.enableSeconds,
            "data-collapsed": L.value && o.enableSeconds
          }, [
            B.separator ? (A(), V(ke, { key: 0 }, [
              z.value ? J("", !0) : (A(), V(ke, { key: 0 }, [
                gt(":")
              ], 64))
            ], 64)) : (A(), V(ke, { key: 1 }, [
              ge("button", {
                ref_for: !0,
                ref: (F) => pe(F, Me, 0),
                type: "button",
                class: De({
                  dp__btn: !0,
                  dp__inc_dec_button: !o.timePickerInline,
                  dp__inc_dec_button_inline: o.timePickerInline,
                  dp__tp_inline_btn_top: o.timePickerInline,
                  dp__inc_dec_button_disabled: O.value(B.type),
                  "dp--hidden-el": z.value
                }),
                "data-test-id": `${B.type}-time-inc-btn-${n.order}`,
                "aria-label": (be = s(y)) == null ? void 0 : be.incrementValue(B.type),
                tabindex: "0",
                onKeydown: (F) => s(Ze)(F, () => D(B.type, !0, { keyboard: !0 }), !0),
                onClick: (F) => s(c).timeArrowHoldThreshold ? void 0 : D(B.type, !0),
                onMousedown: (F) => s(c).timeArrowHoldThreshold ? D(B.type, !0) : void 0,
                onMouseup: l
              }, [
                n.timePickerInline ? (A(), V(ke, { key: 1 }, [
                  o.$slots["tp-inline-arrow-up"] ? de(o.$slots, "tp-inline-arrow-up", { key: 0 }) : (A(), V(ke, { key: 1 }, [
                    E[2] || (E[2] = ge("span", { class: "dp__tp_inline_btn_bar dp__tp_btn_in_l" }, null, -1)),
                    E[3] || (E[3] = ge("span", { class: "dp__tp_inline_btn_bar dp__tp_btn_in_r" }, null, -1))
                  ], 64))
                ], 64)) : (A(), V(ke, { key: 0 }, [
                  o.$slots["arrow-up"] ? de(o.$slots, "arrow-up", { key: 0 }) : J("", !0),
                  o.$slots["arrow-up"] ? J("", !0) : (A(), Te(s(Ka), { key: 1 }))
                ], 64))
              ], 42, wr),
              ge("button", {
                ref_for: !0,
                ref: (F) => pe(F, Me, 1),
                type: "button",
                "aria-label": `${W.value(B.type).text}-${(Se = s(y)) == null ? void 0 : Se.openTpOverlay(B.type)}`,
                class: De({
                  dp__time_display: !0,
                  dp__time_display_block: !o.timePickerInline,
                  dp__time_display_inline: o.timePickerInline,
                  "dp--time-invalid": ee.value(B.type),
                  "dp--time-overlay-btn": !ee.value(B.type),
                  "dp--hidden-el": z.value
                }),
                disabled: se(B.type),
                tabindex: "0",
                "data-test-id": `${B.type}-toggle-overlay-btn-${n.order}`,
                onKeydown: (F) => s(Ze)(F, () => R(B.type), !0),
                onClick: (F) => R(B.type)
              }, [
                o.$slots[B.type] ? de(o.$slots, B.type, {
                  key: 0,
                  text: W.value(B.type).text,
                  value: W.value(B.type).value
                }) : J("", !0),
                o.$slots[B.type] ? J("", !0) : (A(), V(ke, { key: 1 }, [
                  gt(Qe(W.value(B.type).text), 1)
                ], 64))
              ], 42, Dr),
              ge("button", {
                ref_for: !0,
                ref: (F) => pe(F, Me, 2),
                type: "button",
                class: De({
                  dp__btn: !0,
                  dp__inc_dec_button: !o.timePickerInline,
                  dp__inc_dec_button_inline: o.timePickerInline,
                  dp__tp_inline_btn_bottom: o.timePickerInline,
                  dp__inc_dec_button_disabled: K.value(B.type),
                  "dp--hidden-el": z.value
                }),
                "data-test-id": `${B.type}-time-dec-btn-${n.order}`,
                "aria-label": (b = s(y)) == null ? void 0 : b.decrementValue(B.type),
                tabindex: "0",
                onKeydown: (F) => s(Ze)(F, () => D(B.type, !1, { keyboard: !0 }), !0),
                onClick: (F) => s(c).timeArrowHoldThreshold ? void 0 : D(B.type, !1),
                onMousedown: (F) => s(c).timeArrowHoldThreshold ? D(B.type, !1) : void 0,
                onMouseup: l
              }, [
                n.timePickerInline ? (A(), V(ke, { key: 1 }, [
                  o.$slots["tp-inline-arrow-down"] ? de(o.$slots, "tp-inline-arrow-down", { key: 0 }) : (A(), V(ke, { key: 1 }, [
                    E[4] || (E[4] = ge("span", { class: "dp__tp_inline_btn_bar dp__tp_btn_in_l" }, null, -1)),
                    E[5] || (E[5] = ge("span", { class: "dp__tp_inline_btn_bar dp__tp_btn_in_r" }, null, -1))
                  ], 64))
                ], 64)) : (A(), V(ke, { key: 0 }, [
                  o.$slots["arrow-down"] ? de(o.$slots, "arrow-down", { key: 0 }) : J("", !0),
                  o.$slots["arrow-down"] ? J("", !0) : (A(), Te(s(Ga), { key: 1 }))
                ], 64))
              ], 42, Mr)
            ], 64))
          ], 10, kr);
        }), 128)),
        o.is24 ? J("", !0) : (A(), V("div", $r, [
          o.$slots["am-pm-button"] ? de(o.$slots, "am-pm-button", {
            key: 0,
            toggle: M,
            value: H.value
          }) : J("", !0),
          o.$slots["am-pm-button"] ? J("", !0) : (A(), V("button", {
            key: 1,
            ref_key: "amPmButton",
            ref: f,
            type: "button",
            class: "dp__pm_am_button",
            role: "button",
            "aria-label": (ce = s(y)) == null ? void 0 : ce.amPmButton,
            tabindex: "0",
            "data-compact": L.value,
            onClick: M,
            onKeydown: E[0] || (E[0] = (B) => s(Ze)(B, () => M(), !0))
          }, Qe(H.value), 41, Ar))
        ])),
        (A(!0), V(ke, null, Be(p.value, (B, Me) => (A(), Te(Ut, {
          key: Me,
          name: s(P)(N[B.type]),
          css: s(U)
        }, {
          default: we(() => {
            var be, Se;
            return [
              N[B.type] ? (A(), Te(ea, {
                key: 0,
                items: $(B.type),
                "is-last": o.autoApply && !s(c).keepActionRow,
                "esc-close": o.escClose,
                type: B.type,
                "text-input": o.textInput,
                config: o.config,
                "arrow-navigation": o.arrowNavigation,
                "aria-labels": o.ariaLabels,
                "overlay-label": (Se = (be = s(y)).timeOverlay) == null ? void 0 : Se.call(be, B.type),
                onSelected: (b) => re(B.type, b),
                onToggle: (b) => R(B.type),
                onResetFlow: E[1] || (E[1] = (b) => o.$emit("reset-flow"))
              }, qe({
                "button-icon": we(() => [
                  o.$slots["clock-icon"] ? de(o.$slots, "clock-icon", { key: 0 }) : J("", !0),
                  o.$slots["clock-icon"] ? J("", !0) : (A(), Te(fa(o.timePickerInline ? s(Wt) : s(ja)), { key: 1 }))
                ]),
                _: 2
              }, [
                o.$slots[`${B.type}-overlay-value`] ? {
                  name: "item",
                  fn: we(({ item: b }) => [
                    de(o.$slots, `${B.type}-overlay-value`, {
                      text: b.text,
                      value: b.value
                    })
                  ]),
                  key: "0"
                } : void 0,
                o.$slots[`${B.type}-overlay-header`] ? {
                  name: "header",
                  fn: we(() => [
                    de(o.$slots, `${B.type}-overlay-header`, {
                      toggle: () => R(B.type)
                    })
                  ]),
                  key: "1"
                } : void 0
              ]), 1032, ["items", "is-last", "esc-close", "type", "text-input", "config", "arrow-navigation", "aria-labels", "overlay-label", "onSelected", "onToggle"])) : J("", !0)
            ];
          }),
          _: 2
        }, 1032, ["name", "css"]))), 128))
      ]));
    };
  }
}), Sr = ["data-dp-mobile"], Pr = ["aria-label", "tabindex"], Rr = ["role", "aria-label", "tabindex"], Cr = ["aria-label"], Vn = /* @__PURE__ */ Ve({
  compatConfig: {
    MODE: 3
  },
  __name: "TimePicker",
  props: {
    hours: { type: [Number, Array], default: 0 },
    minutes: { type: [Number, Array], default: 0 },
    seconds: { type: [Number, Array], default: 0 },
    disabledTimesConfig: { type: Function, default: null },
    validateTime: {
      type: Function,
      default: () => !1
    },
    ...ct
  },
  emits: [
    "update:hours",
    "update:minutes",
    "update:seconds",
    "mount",
    "reset-flow",
    "overlay-opened",
    "overlay-closed",
    "am-pm-change"
  ],
  setup(e, { expose: t, emit: r }) {
    const a = r, n = e, { buildMatrix: u, setTimePicker: d } = At(), y = Bt(), { defaultedTransitions: i, defaultedAriaLabels: _, defaultedTextInput: c, defaultedConfig: C, defaultedRange: m } = _e(n), { transitionName: P, showTransition: U } = ta(i), { hideNavigationButtons: N } = ya(), H = te(null), f = te(null), I = te([]), k = te(null), z = te(!1);
    je(() => {
      a("mount"), !n.timePicker && n.arrowNavigation ? u([Le(H.value)], "time") : d(!0, n.timePicker);
    });
    const q = Q(() => m.value.enabled && n.modelAuto ? Pn(n.internalModelValue) : !0), ee = te(!1), x = (T) => ({
      hours: Array.isArray(n.hours) ? n.hours[T] : n.hours,
      minutes: Array.isArray(n.minutes) ? n.minutes[T] : n.minutes,
      seconds: Array.isArray(n.seconds) ? n.seconds[T] : n.seconds
    }), S = Q(() => {
      const T = [];
      if (m.value.enabled)
        for (let oe = 0; oe < 2; oe++)
          T.push(x(oe));
      else
        T.push(x(0));
      return T;
    }), X = (T, oe = !1, $ = "") => {
      oe || a("reset-flow"), ee.value = T, a(T ? "overlay-opened" : "overlay-closed", Ge.time), n.arrowNavigation && d(T), nt(() => {
        $ !== "" && I.value[0] && I.value[0].openChildCmp($);
      });
    }, O = Q(() => ({
      dp__btn: !0,
      dp__button: !0,
      dp__button_bottom: n.autoApply && !C.value.keepActionRow
    })), K = tt(y, "timePicker"), fe = (T, oe, $) => m.value.enabled ? oe === 0 ? [T, S.value[1][$]] : [S.value[0][$], T] : T, me = (T) => {
      a("update:hours", T);
    }, v = (T) => {
      a("update:minutes", T);
    }, L = (T) => {
      a("update:seconds", T);
    }, ne = () => {
      if (k.value && !c.value.enabled && !n.noOverlayFocus) {
        const T = Rn(k.value);
        T && T.focus({ preventScroll: !0 });
      }
    }, p = (T) => {
      z.value = !1, a("overlay-closed", T);
    }, W = (T) => {
      z.value = !0, a("overlay-opened", T);
    };
    return t({ toggleTimePicker: X }), (T, oe) => {
      var $;
      return A(), V("div", {
        class: "dp--tp-wrap",
        "data-dp-mobile": T.isMobile
      }, [
        !T.timePicker && !T.timePickerInline ? sa((A(), V("button", {
          key: 0,
          ref_key: "openTimePickerBtn",
          ref: H,
          type: "button",
          class: De({ ...O.value, "dp--hidden-el": ee.value }),
          "aria-label": ($ = s(_)) == null ? void 0 : $.openTimePicker,
          tabindex: T.noOverlayFocus ? void 0 : 0,
          "data-test-id": "open-time-picker-btn",
          onKeydown: oe[0] || (oe[0] = (Y) => s(Ze)(Y, () => X(!0))),
          onClick: oe[1] || (oe[1] = (Y) => X(!0))
        }, [
          T.$slots["clock-icon"] ? de(T.$slots, "clock-icon", { key: 0 }) : J("", !0),
          T.$slots["clock-icon"] ? J("", !0) : (A(), Te(s(ja), { key: 1 }))
        ], 42, Pr)), [
          [ua, !s(N)(T.hideNavigation, "time")]
        ]) : J("", !0),
        at(Ut, {
          name: s(P)(ee.value),
          css: s(U) && !T.timePickerInline
        }, {
          default: we(() => {
            var Y, g;
            return [
              ee.value || T.timePicker || T.timePickerInline ? (A(), V("div", {
                key: 0,
                ref_key: "overlayRef",
                ref: k,
                role: T.timePickerInline ? void 0 : "dialog",
                class: De({
                  dp__overlay: !T.timePickerInline,
                  "dp--overlay-absolute": !n.timePicker && !T.timePickerInline,
                  "dp--overlay-relative": n.timePicker
                }),
                style: rt(T.timePicker ? { height: `${s(C).modeHeight}px` } : void 0),
                "aria-label": (Y = s(_)) == null ? void 0 : Y.timePicker,
                tabindex: T.timePickerInline ? void 0 : 0
              }, [
                ge("div", {
                  class: De(
                    T.timePickerInline ? "dp__time_picker_inline_container" : "dp__overlay_container dp__container_flex dp__time_picker_overlay_container"
                  ),
                  style: { display: "flex" }
                }, [
                  T.$slots["time-picker-overlay"] ? de(T.$slots, "time-picker-overlay", {
                    key: 0,
                    hours: e.hours,
                    minutes: e.minutes,
                    seconds: e.seconds,
                    setHours: me,
                    setMinutes: v,
                    setSeconds: L
                  }) : J("", !0),
                  T.$slots["time-picker-overlay"] ? J("", !0) : (A(), V("div", {
                    key: 1,
                    class: De(T.timePickerInline ? "dp__flex" : "dp__overlay_row dp__flex_row")
                  }, [
                    (A(!0), V(ke, null, Be(S.value, (Z, se) => sa((A(), Te(Tr, He({
                      key: se,
                      ref_for: !0
                    }, {
                      ...T.$props,
                      order: se,
                      hours: Z.hours,
                      minutes: Z.minutes,
                      seconds: Z.seconds,
                      closeTimePickerBtn: f.value,
                      disabledTimesConfig: e.disabledTimesConfig,
                      disabled: se === 0 ? s(m).fixedStart : s(m).fixedEnd
                    }, {
                      ref_for: !0,
                      ref_key: "timeInputRefs",
                      ref: I,
                      "validate-time": (R, ae) => e.validateTime(R, fe(ae, se, R)),
                      "onUpdate:hours": (R) => me(fe(R, se, "hours")),
                      "onUpdate:minutes": (R) => v(fe(R, se, "minutes")),
                      "onUpdate:seconds": (R) => L(fe(R, se, "seconds")),
                      onMounted: ne,
                      onOverlayClosed: p,
                      onOverlayOpened: W,
                      onAmPmChange: oe[2] || (oe[2] = (R) => T.$emit("am-pm-change", R))
                    }), qe({ _: 2 }, [
                      Be(s(K), (R, ae) => ({
                        name: R,
                        fn: we((l) => [
                          de(T.$slots, R, He({ ref_for: !0 }, l))
                        ])
                      }))
                    ]), 1040, ["validate-time", "onUpdate:hours", "onUpdate:minutes", "onUpdate:seconds"])), [
                      [ua, se === 0 ? !0 : q.value]
                    ])), 128))
                  ], 2)),
                  !T.timePicker && !T.timePickerInline ? sa((A(), V("button", {
                    key: 2,
                    ref_key: "closeTimePickerBtn",
                    ref: f,
                    type: "button",
                    class: De({ ...O.value, "dp--hidden-el": z.value }),
                    "aria-label": (g = s(_)) == null ? void 0 : g.closeTimePicker,
                    tabindex: "0",
                    onKeydown: oe[3] || (oe[3] = (Z) => s(Ze)(Z, () => X(!1))),
                    onClick: oe[4] || (oe[4] = (Z) => X(!1))
                  }, [
                    T.$slots["calendar-icon"] ? de(T.$slots, "calendar-icon", { key: 0 }) : J("", !0),
                    T.$slots["calendar-icon"] ? J("", !0) : (A(), Te(s(Wt), { key: 1 }))
                  ], 42, Cr)), [
                    [ua, !s(N)(T.hideNavigation, "time")]
                  ]) : J("", !0)
                ], 2)
              ], 14, Rr)) : J("", !0)
            ];
          }),
          _: 3
        }, 8, ["name", "css"])
      ], 8, Sr);
    };
  }
}), jn = (e, t, r, a) => {
  const { defaultedRange: n } = _e(e), u = (k, z) => Array.isArray(t[k]) ? t[k][z] : t[k], d = (k) => e.enableSeconds ? Array.isArray(t.seconds) ? t.seconds[k] : t.seconds : 0, y = (k, z) => k ? z !== void 0 ? Mt(k, u("hours", z), u("minutes", z), d(z)) : Mt(k, t.hours, t.minutes, d()) : bn(j(), d(z)), i = (k, z) => {
    t[k] = z;
  }, _ = Q(() => e.modelAuto && n.value.enabled ? Array.isArray(r.value) ? r.value.length > 1 : !1 : n.value.enabled), c = (k, z) => {
    const q = Object.fromEntries(
      Object.keys(t).map((ee) => ee === k ? [ee, z] : [ee, t[ee]].slice())
    );
    if (_.value && !n.value.disableTimeRangeValidation) {
      const ee = (S) => r.value ? Mt(
        r.value[S],
        q.hours[S],
        q.minutes[S],
        q.seconds[S]
      ) : null, x = (S) => kn(r.value[S], 0);
      return !(Ae(ee(0), ee(1)) && (Ot(ee(0), x(1)) || Ft(ee(1), x(0))));
    }
    return !0;
  }, C = (k, z) => {
    c(k, z) && (i(k, z), a && a());
  }, m = (k) => {
    C("hours", k);
  }, P = (k) => {
    C("minutes", k);
  }, U = (k) => {
    C("seconds", k);
  }, N = (k, z, q, ee) => {
    z && m(k), !z && !q && P(k), q && U(k), r.value && ee(r.value);
  }, H = (k) => {
    if (k) {
      const z = Array.isArray(k), q = z ? [+k[0].hours, +k[1].hours] : +k.hours, ee = z ? [+k[0].minutes, +k[1].minutes] : +k.minutes, x = z ? [+k[0].seconds, +k[1].seconds] : +k.seconds;
      i("hours", q), i("minutes", ee), e.enableSeconds && i("seconds", x);
    }
  }, f = (k, z) => {
    const q = {
      hours: Array.isArray(t.hours) ? t.hours[k] : t.hours,
      disabledArr: []
    };
    return (z || z === 0) && (q.hours = z), Array.isArray(e.disabledTimes) && (q.disabledArr = n.value.enabled && Array.isArray(e.disabledTimes[k]) ? e.disabledTimes[k] : e.disabledTimes), q;
  }, I = Q(() => (k, z) => {
    var q;
    if (Array.isArray(e.disabledTimes)) {
      const { disabledArr: ee, hours: x } = f(k, z), S = ee.filter((X) => +X.hours === x);
      return ((q = S[0]) == null ? void 0 : q.minutes) === "*" ? { hours: [x], minutes: void 0, seconds: void 0 } : {
        hours: [],
        minutes: (S == null ? void 0 : S.map((X) => +X.minutes)) ?? [],
        seconds: (S == null ? void 0 : S.map((X) => X.seconds ? +X.seconds : void 0)) ?? []
      };
    }
    return { hours: [], minutes: [], seconds: [] };
  });
  return {
    setTime: i,
    updateHours: m,
    updateMinutes: P,
    updateSeconds: U,
    getSetDateTime: y,
    updateTimeValues: N,
    getSecondsValue: d,
    assignStartTime: H,
    validateTime: c,
    disabledTimesConfig: I
  };
}, Or = (e, t) => {
  const r = () => {
    e.isTextInputDate && z();
  }, { modelValue: a, time: n } = aa(e, t, r), { defaultedStartTime: u, defaultedRange: d, defaultedTz: y } = _e(e), { updateTimeValues: i, getSetDateTime: _, setTime: c, assignStartTime: C, disabledTimesConfig: m, validateTime: P } = jn(e, n, a, U);
  function U() {
    t("update-flow-step");
  }
  const N = (x) => {
    const { hours: S, minutes: X, seconds: O } = x;
    return { hours: +S, minutes: +X, seconds: O ? +O : 0 };
  }, H = () => {
    if (e.startTime) {
      if (Array.isArray(e.startTime)) {
        const S = N(e.startTime[0]), X = N(e.startTime[1]);
        return [Pe(j(), S), Pe(j(), X)];
      }
      const x = N(e.startTime);
      return Pe(j(), x);
    }
    return d.value.enabled ? [null, null] : null;
  }, f = () => {
    if (d.value.enabled) {
      const [x, S] = H();
      a.value = [
        et(_(x, 0), y.value.timezone),
        et(_(S, 1), y.value.timezone)
      ];
    } else
      a.value = et(_(H()), y.value.timezone);
  }, I = (x) => Array.isArray(x) ? [Ct(j(x[0])), Ct(j(x[1]))] : [Ct(x ?? j())], k = (x, S, X) => {
    c("hours", x), c("minutes", S), c("seconds", e.enableSeconds ? X : 0);
  }, z = () => {
    const [x, S] = I(a.value);
    return d.value.enabled ? k(
      [x.hours, S.hours],
      [x.minutes, S.minutes],
      [x.seconds, S.seconds]
    ) : k(x.hours, x.minutes, x.seconds);
  };
  je(() => {
    if (!e.shadow)
      return C(u.value), a.value ? z() : f();
  });
  const q = () => {
    Array.isArray(a.value) ? a.value = a.value.map((x, S) => x && _(x, S)) : a.value = _(a.value), t("time-update");
  };
  return {
    modelValue: a,
    time: n,
    disabledTimesConfig: m,
    updateTime: (x, S = !0, X = !1) => {
      i(x, S, X, q);
    },
    validateTime: P
  };
}, Br = /* @__PURE__ */ Ve({
  compatConfig: {
    MODE: 3
  },
  __name: "TimePickerSolo",
  props: {
    ...ct
  },
  emits: [
    "update:internal-model-value",
    "time-update",
    "am-pm-change",
    "mount",
    "reset-flow",
    "update-flow-step",
    "overlay-toggle"
  ],
  setup(e, { expose: t, emit: r }) {
    const a = r, n = e, u = Bt(), d = tt(u, "timePicker"), y = te(null), { time: i, modelValue: _, disabledTimesConfig: c, updateTime: C, validateTime: m } = Or(n, a);
    return je(() => {
      n.shadow || a("mount", null);
    }), t({ getSidebarProps: () => ({
      modelValue: _,
      time: i,
      updateTime: C
    }), toggleTimePicker: (N, H = !1, f = "") => {
      var I;
      (I = y.value) == null || I.toggleTimePicker(N, H, f);
    } }), (N, H) => (A(), Te(ma, {
      "multi-calendars": 0,
      stretch: "",
      "is-mobile": N.isMobile
    }, {
      default: we(() => [
        at(Vn, He({
          ref_key: "tpRef",
          ref: y
        }, N.$props, {
          hours: s(i).hours,
          minutes: s(i).minutes,
          seconds: s(i).seconds,
          "internal-model-value": N.internalModelValue,
          "disabled-times-config": s(c),
          "validate-time": s(m),
          "onUpdate:hours": H[0] || (H[0] = (f) => s(C)(f)),
          "onUpdate:minutes": H[1] || (H[1] = (f) => s(C)(f, !1)),
          "onUpdate:seconds": H[2] || (H[2] = (f) => s(C)(f, !1, !0)),
          onAmPmChange: H[3] || (H[3] = (f) => N.$emit("am-pm-change", f)),
          onResetFlow: H[4] || (H[4] = (f) => N.$emit("reset-flow")),
          onOverlayClosed: H[5] || (H[5] = (f) => N.$emit("overlay-toggle", { open: !1, overlay: f })),
          onOverlayOpened: H[6] || (H[6] = (f) => N.$emit("overlay-toggle", { open: !0, overlay: f }))
        }), qe({ _: 2 }, [
          Be(s(d), (f, I) => ({
            name: f,
            fn: we((k) => [
              de(N.$slots, f, ze(xe(k)))
            ])
          }))
        ]), 1040, ["hours", "minutes", "seconds", "internal-model-value", "disabled-times-config", "validate-time"])
      ]),
      _: 3
    }, 8, ["is-mobile"]));
  }
}), _r = { class: "dp--header-wrap" }, Yr = {
  key: 0,
  class: "dp__month_year_wrap"
}, Ir = { key: 0 }, Er = { class: "dp__month_year_wrap" }, Nr = ["data-dp-element", "aria-label", "data-test-id", "onClick", "onKeydown"], Fr = /* @__PURE__ */ Ve({
  compatConfig: {
    MODE: 3
  },
  __name: "DpHeader",
  props: {
    month: { type: Number, default: 0 },
    year: { type: Number, default: 0 },
    instance: { type: Number, default: 0 },
    years: { type: Array, default: () => [] },
    months: { type: Array, default: () => [] },
    ...ct
  },
  emits: ["update-month-year", "mount", "reset-flow", "overlay-closed", "overlay-opened"],
  setup(e, { expose: t, emit: r }) {
    const a = r, n = e, {
      defaultedTransitions: u,
      defaultedAriaLabels: d,
      defaultedMultiCalendars: y,
      defaultedFilters: i,
      defaultedConfig: _,
      defaultedHighlight: c,
      propDates: C,
      defaultedUI: m
    } = _e(n), { transitionName: P, showTransition: U } = ta(u), { buildMatrix: N } = At(), { handleMonthYearChange: H, isDisabled: f, updateMonthYear: I } = lr(n, a), { showLeftIcon: k, showRightIcon: z } = ya(), q = te(!1), ee = te(!1), x = te(!1), S = te([null, null, null, null]);
    je(() => {
      a("mount");
    });
    const X = (g) => ({
      get: () => n[g],
      set: (Z) => {
        const se = g === ut.month ? ut.year : ut.month;
        a("update-month-year", { [g]: Z, [se]: n[se] }), g === ut.month ? p(!0) : W(!0);
      }
    }), O = Q(X(ut.month)), K = Q(X(ut.year)), fe = Q(() => (g) => ({
      month: n.month,
      year: n.year,
      items: g === ut.month ? n.months : n.years,
      instance: n.instance,
      updateMonthYear: I,
      toggle: g === ut.month ? p : W
    })), me = Q(() => {
      const g = n.months.find((Z) => Z.value === n.month);
      return g || { text: "", value: 0 };
    }), v = Q(() => zt(n.months, (g) => {
      const Z = n.month === g.value, se = Xt(
        g.value,
        Bn(n.year, C.value.minDate),
        _n(n.year, C.value.maxDate)
      ) || i.value.months.includes(g.value), R = Fn(c.value, g.value, n.year);
      return { active: Z, disabled: se, highlighted: R };
    })), L = Q(() => zt(n.years, (g) => {
      const Z = n.year === g.value, se = Xt(
        g.value,
        Ht(C.value.minDate),
        Ht(C.value.maxDate)
      ) || i.value.years.includes(g.value), R = Za(c.value, g.value);
      return { active: Z, disabled: se, highlighted: R };
    })), ne = (g, Z, se) => {
      se !== void 0 ? g.value = se : g.value = !g.value, g.value ? (x.value = !0, a("overlay-opened", Z)) : (x.value = !1, a("overlay-closed", Z));
    }, p = (g = !1, Z) => {
      T(g), ne(q, Ge.month, Z);
    }, W = (g = !1, Z) => {
      T(g), ne(ee, Ge.year, Z);
    }, T = (g) => {
      g || a("reset-flow");
    }, oe = (g, Z) => {
      n.arrowNavigation && (S.value[Z] = Le(g), N(S.value, "monthYear"));
    }, $ = Q(() => {
      var g, Z, se, R, ae, l;
      return [
        {
          type: ut.month,
          index: 1,
          toggle: p,
          modelValue: O.value,
          updateModelValue: (D) => O.value = D,
          text: me.value.text,
          showSelectionGrid: q.value,
          items: v.value,
          ariaLabel: (g = d.value) == null ? void 0 : g.openMonthsOverlay,
          overlayLabel: ((se = (Z = d.value).monthPicker) == null ? void 0 : se.call(Z, !0)) ?? void 0
        },
        {
          type: ut.year,
          index: 2,
          toggle: W,
          modelValue: K.value,
          updateModelValue: (D) => K.value = D,
          text: Cn(n.year, n.locale),
          showSelectionGrid: ee.value,
          items: L.value,
          ariaLabel: (R = d.value) == null ? void 0 : R.openYearsOverlay,
          overlayLabel: ((l = (ae = d.value).yearPicker) == null ? void 0 : l.call(ae, !0)) ?? void 0
        }
      ];
    }), Y = Q(() => n.disableYearSelect ? [$.value[0]] : n.yearFirst ? [...$.value].reverse() : $.value);
    return t({
      toggleMonthPicker: p,
      toggleYearPicker: W,
      handleMonthYearChange: H
    }), (g, Z) => {
      var se, R, ae, l, D, ue;
      return A(), V("div", _r, [
        g.$slots["month-year"] ? (A(), V("div", Yr, [
          de(g.$slots, "month-year", ze(xe({
            month: e.month,
            year: e.year,
            months: e.months,
            years: e.years,
            updateMonthYear: s(I),
            handleMonthYearChange: s(H),
            instance: e.instance,
            isDisabled: s(f)
          })))
        ])) : (A(), V(ke, { key: 1 }, [
          g.$slots["top-extra"] ? (A(), V("div", Ir, [
            de(g.$slots, "top-extra", { value: g.internalModelValue })
          ])) : J("", !0),
          ge("div", Er, [
            s(k)(s(y), e.instance) && !g.vertical ? (A(), Te(Kt, {
              key: 0,
              "aria-label": (se = s(d)) == null ? void 0 : se.prevMonth,
              disabled: s(f)(!1),
              class: De((R = s(m)) == null ? void 0 : R.navBtnPrev),
              "el-name": "action-prev",
              onActivate: Z[0] || (Z[0] = (M) => s(H)(!1, !0)),
              onSetRef: Z[1] || (Z[1] = (M) => oe(M, 0))
            }, {
              default: we(() => [
                g.$slots["arrow-left"] ? de(g.$slots, "arrow-left", { key: 0 }) : J("", !0),
                g.$slots["arrow-left"] ? J("", !0) : (A(), Te(s(Wa), { key: 1 }))
              ]),
              _: 3
            }, 8, ["aria-label", "disabled", "class"])) : J("", !0),
            ge("div", {
              class: De(["dp__month_year_wrap", {
                dp__year_disable_select: g.disableYearSelect
              }])
            }, [
              (A(!0), V(ke, null, Be(Y.value, (M, he) => (A(), V(ke, {
                key: M.type
              }, [
                ge("button", {
                  ref_for: !0,
                  ref: (pe) => oe(pe, he + 1),
                  type: "button",
                  "data-dp-element": `overlay-${M.type}`,
                  class: De(["dp__btn dp__month_year_select", { "dp--hidden-el": x.value }]),
                  "aria-label": `${M.text}-${M.ariaLabel}`,
                  "data-test-id": `${M.type}-toggle-overlay-${e.instance}`,
                  onClick: M.toggle,
                  onKeydown: (pe) => s(Ze)(pe, () => M.toggle(), !0)
                }, [
                  g.$slots[M.type] ? de(g.$slots, M.type, {
                    key: 0,
                    text: M.text,
                    value: n[M.type]
                  }) : J("", !0),
                  g.$slots[M.type] ? J("", !0) : (A(), V(ke, { key: 1 }, [
                    gt(Qe(M.text), 1)
                  ], 64))
                ], 42, Nr),
                at(Ut, {
                  name: s(P)(M.showSelectionGrid),
                  css: s(U)
                }, {
                  default: we(() => [
                    M.showSelectionGrid ? (A(), Te(ea, {
                      key: 0,
                      items: M.items,
                      "arrow-navigation": g.arrowNavigation,
                      "hide-navigation": g.hideNavigation,
                      "is-last": g.autoApply && !s(_).keepActionRow,
                      "skip-button-ref": !1,
                      config: g.config,
                      type: M.type,
                      "header-refs": [],
                      "esc-close": g.escClose,
                      "menu-wrap-ref": g.menuWrapRef,
                      "text-input": g.textInput,
                      "aria-labels": g.ariaLabels,
                      "overlay-label": M.overlayLabel,
                      onSelected: M.updateModelValue,
                      onToggle: M.toggle
                    }, qe({
                      "button-icon": we(() => [
                        g.$slots["calendar-icon"] ? de(g.$slots, "calendar-icon", { key: 0 }) : J("", !0),
                        g.$slots["calendar-icon"] ? J("", !0) : (A(), Te(s(Wt), { key: 1 }))
                      ]),
                      _: 2
                    }, [
                      g.$slots[`${M.type}-overlay-value`] ? {
                        name: "item",
                        fn: we(({ item: pe }) => [
                          de(g.$slots, `${M.type}-overlay-value`, {
                            text: pe.text,
                            value: pe.value
                          })
                        ]),
                        key: "0"
                      } : void 0,
                      g.$slots[`${M.type}-overlay`] ? {
                        name: "overlay",
                        fn: we(() => [
                          de(g.$slots, `${M.type}-overlay`, He({ ref_for: !0 }, fe.value(M.type)))
                        ]),
                        key: "1"
                      } : void 0,
                      g.$slots[`${M.type}-overlay-header`] ? {
                        name: "header",
                        fn: we(() => [
                          de(g.$slots, `${M.type}-overlay-header`, {
                            toggle: M.toggle
                          })
                        ]),
                        key: "2"
                      } : void 0
                    ]), 1032, ["items", "arrow-navigation", "hide-navigation", "is-last", "config", "type", "esc-close", "menu-wrap-ref", "text-input", "aria-labels", "overlay-label", "onSelected", "onToggle"])) : J("", !0)
                  ]),
                  _: 2
                }, 1032, ["name", "css"])
              ], 64))), 128))
            ], 2),
            s(k)(s(y), e.instance) && g.vertical ? (A(), Te(Kt, {
              key: 1,
              "aria-label": (ae = s(d)) == null ? void 0 : ae.prevMonth,
              "el-name": "action-prev",
              disabled: s(f)(!1),
              class: De((l = s(m)) == null ? void 0 : l.navBtnPrev),
              onActivate: Z[2] || (Z[2] = (M) => s(H)(!1, !0))
            }, {
              default: we(() => [
                g.$slots["arrow-up"] ? de(g.$slots, "arrow-up", { key: 0 }) : J("", !0),
                g.$slots["arrow-up"] ? J("", !0) : (A(), Te(s(Ka), { key: 1 }))
              ]),
              _: 3
            }, 8, ["aria-label", "disabled", "class"])) : J("", !0),
            s(z)(s(y), e.instance) ? (A(), Te(Kt, {
              key: 2,
              ref: "rightIcon",
              "el-name": "action-next",
              disabled: s(f)(!0),
              "aria-label": (D = s(d)) == null ? void 0 : D.nextMonth,
              class: De((ue = s(m)) == null ? void 0 : ue.navBtnNext),
              onActivate: Z[3] || (Z[3] = (M) => s(H)(!0, !0)),
              onSetRef: Z[4] || (Z[4] = (M) => oe(M, g.disableYearSelect ? 2 : 3))
            }, {
              default: we(() => [
                g.$slots[g.vertical ? "arrow-down" : "arrow-right"] ? de(g.$slots, g.vertical ? "arrow-down" : "arrow-right", { key: 0 }) : J("", !0),
                g.$slots[g.vertical ? "arrow-down" : "arrow-right"] ? J("", !0) : (A(), Te(fa(g.vertical ? s(Ga) : s(Va)), { key: 1 }))
              ]),
              _: 3
            }, 8, ["disabled", "aria-label", "class"])) : J("", !0)
          ])
        ], 64))
      ]);
    };
  }
}), Lr = {
  class: "dp__calendar_header",
  role: "row"
}, zr = {
  key: 0,
  class: "dp__calendar_header_item",
  role: "gridcell"
}, Hr = ["aria-label"], Ur = {
  key: 0,
  class: "dp__calendar_item dp__week_num",
  role: "gridcell"
}, Wr = { class: "dp__cell_inner" }, Vr = ["id", "aria-pressed", "aria-disabled", "aria-label", "tabindex", "data-test-id", "onClick", "onTouchend", "onKeydown", "onMouseenter", "onMouseleave", "onMousedown"], jr = /* @__PURE__ */ Ve({
  compatConfig: {
    MODE: 3
  },
  __name: "DpCalendar",
  props: {
    mappedDates: { type: Array, default: () => [] },
    instance: { type: Number, default: 0 },
    month: { type: Number, default: 0 },
    year: { type: Number, default: 0 },
    ...ct
  },
  emits: [
    "select-date",
    "set-hover-date",
    "handle-scroll",
    "mount",
    "handle-swipe",
    "handle-space",
    "tooltip-open",
    "tooltip-close"
  ],
  setup(e, { expose: t, emit: r }) {
    const a = r, n = e, { buildMultiLevelMatrix: u } = At(), {
      defaultedTransitions: d,
      defaultedConfig: y,
      defaultedAriaLabels: i,
      defaultedMultiCalendars: _,
      defaultedWeekNumbers: c,
      defaultedMultiDates: C,
      defaultedUI: m
    } = _e(n), P = te(null), U = te({
      bottom: "",
      left: "",
      transform: ""
    }), N = te([]), H = te(null), f = te(!0), I = te(""), k = te({ startX: 0, endX: 0, startY: 0, endY: 0 }), z = te([]), q = te({ left: "50%" }), ee = te(!1), x = Q(() => n.calendar ? n.calendar(n.mappedDates) : n.mappedDates), S = Q(() => n.dayNames ? Array.isArray(n.dayNames) ? n.dayNames : n.dayNames(n.locale, +n.weekStart) : Ml(n.formatLocale, n.locale, +n.weekStart));
    je(() => {
      a("mount", { cmp: "calendar", refs: N }), y.value.noSwipe || H.value && (H.value.addEventListener("touchstart", oe, { passive: !1 }), H.value.addEventListener("touchend", $, { passive: !1 }), H.value.addEventListener("touchmove", Y, { passive: !1 })), n.monthChangeOnScroll && H.value && H.value.addEventListener("wheel", se, { passive: !1 });
    });
    const X = (M) => M ? n.vertical ? "vNext" : "next" : n.vertical ? "vPrevious" : "previous", O = (M, he) => {
      if (n.transitions) {
        const pe = We(yt(j(), n.month, n.year));
        I.value = Ee(We(yt(j(), M, he)), pe) ? d.value[X(!0)] : d.value[X(!1)], f.value = !1, nt(() => {
          f.value = !0;
        });
      }
    }, K = Q(
      () => ({
        ...m.value.calendar ?? {}
      })
    ), fe = Q(() => (M) => {
      const he = Al(M);
      return {
        dp__marker_dot: he.type === "dot",
        dp__marker_line: he.type === "line"
      };
    }), me = Q(() => (M) => Ae(M, P.value)), v = Q(() => ({
      dp__calendar: !0,
      dp__calendar_next: _.value.count > 0 && n.instance !== 0
    })), L = Q(() => (M) => n.hideOffsetDates ? M.current : !0), ne = async (M, he) => {
      const { width: pe, height: re } = M.getBoundingClientRect();
      P.value = he.value;
      let o = { left: `${pe / 2}px` }, E = -50;
      if (await nt(), z.value[0]) {
        const { left: ce, width: B } = z.value[0].getBoundingClientRect();
        ce < 0 && (o = { left: "0" }, E = 0, q.value.left = `${pe / 2}px`), window.innerWidth < ce + B && (o = { right: "0" }, E = 0, q.value.left = `${B - pe / 2}px`);
      }
      U.value = {
        bottom: `${re}px`,
        ...o,
        transform: `translateX(${E}%)`
      };
    }, p = async (M, he, pe) => {
      var o, E, ce;
      const re = Le(N.value[he][pe]);
      re && ((o = M.marker) != null && o.customPosition && ((ce = (E = M.marker) == null ? void 0 : E.tooltip) != null && ce.length) ? U.value = M.marker.customPosition(re) : await ne(re, M), a("tooltip-open", M.marker));
    }, W = async (M, he, pe) => {
      var re, o;
      if (ee.value && C.value.enabled && C.value.dragSelect)
        return a("select-date", M);
      if (a("set-hover-date", M), (o = (re = M.marker) == null ? void 0 : re.tooltip) != null && o.length) {
        if (n.hideOffsetDates && !M.current) return;
        await p(M, he, pe);
      }
    }, T = (M) => {
      P.value && (P.value = null, U.value = JSON.parse(JSON.stringify({ bottom: "", left: "", transform: "" })), a("tooltip-close", M.marker));
    }, oe = (M) => {
      k.value.startX = M.changedTouches[0].screenX, k.value.startY = M.changedTouches[0].screenY;
    }, $ = (M) => {
      k.value.endX = M.changedTouches[0].screenX, k.value.endY = M.changedTouches[0].screenY, g();
    }, Y = (M) => {
      n.vertical && !n.inline && M.preventDefault();
    }, g = () => {
      const M = n.vertical ? "Y" : "X";
      Math.abs(k.value[`start${M}`] - k.value[`end${M}`]) > 10 && a("handle-swipe", k.value[`start${M}`] > k.value[`end${M}`] ? "right" : "left");
    }, Z = (M, he, pe) => {
      M && (Array.isArray(N.value[he]) ? N.value[he][pe] = M : N.value[he] = [M]), n.arrowNavigation && u(N.value, "calendar");
    }, se = (M) => {
      n.monthChangeOnScroll && (M.preventDefault(), a("handle-scroll", M));
    }, R = (M) => c.value.type === "local" ? fl(M.value, { weekStartsOn: +n.weekStart }) : c.value.type === "iso" ? vl(M.value) : typeof c.value.type == "function" ? c.value.type(M.value) : "", ae = (M) => {
      const he = M[0];
      return c.value.hideOnOffsetDates ? M.some((pe) => pe.current) ? R(he) : "" : R(he);
    }, l = (M, he, pe = !0) => {
      !pe && Cl() || (!C.value.enabled || y.value.allowPreventDefault) && (Dt(M, y.value), a("select-date", he));
    }, D = (M) => {
      Dt(M, y.value);
    }, ue = (M) => {
      C.value.enabled && C.value.dragSelect ? (ee.value = !0, a("select-date", M)) : C.value.enabled && a("select-date", M);
    };
    return t({ triggerTransition: O }), (M, he) => (A(), V("div", {
      class: De(v.value)
    }, [
      ge("div", {
        ref_key: "calendarWrapRef",
        ref: H,
        class: De(K.value),
        role: "grid"
      }, [
        ge("div", Lr, [
          M.weekNumbers ? (A(), V("div", zr, Qe(M.weekNumName), 1)) : J("", !0),
          (A(!0), V(ke, null, Be(S.value, (pe, re) => {
            var o, E;
            return A(), V("div", {
              key: re,
              class: "dp__calendar_header_item",
              role: "gridcell",
              "data-test-id": "calendar-header",
              "aria-label": (E = (o = s(i)) == null ? void 0 : o.weekDay) == null ? void 0 : E.call(o, re)
            }, [
              M.$slots["calendar-header"] ? de(M.$slots, "calendar-header", {
                key: 0,
                day: pe,
                index: re
              }) : J("", !0),
              M.$slots["calendar-header"] ? J("", !0) : (A(), V(ke, { key: 1 }, [
                gt(Qe(pe), 1)
              ], 64))
            ], 8, Hr);
          }), 128))
        ]),
        he[2] || (he[2] = ge("div", { class: "dp__calendar_header_separator" }, null, -1)),
        at(Ut, {
          name: I.value,
          css: !!M.transitions
        }, {
          default: we(() => [
            f.value ? (A(), V("div", {
              key: 0,
              class: "dp__calendar",
              role: "rowgroup",
              onMouseleave: he[1] || (he[1] = (pe) => ee.value = !1)
            }, [
              (A(!0), V(ke, null, Be(x.value, (pe, re) => (A(), V("div", {
                key: re,
                class: "dp__calendar_row",
                role: "row"
              }, [
                M.weekNumbers ? (A(), V("div", Ur, [
                  ge("div", Wr, Qe(ae(pe.days)), 1)
                ])) : J("", !0),
                (A(!0), V(ke, null, Be(pe.days, (o, E) => {
                  var ce, B, Me;
                  return A(), V("div", {
                    id: s(Ha)(o.value),
                    ref_for: !0,
                    ref: (be) => Z(be, re, E),
                    key: E + re,
                    role: "gridcell",
                    class: "dp__calendar_item",
                    "aria-pressed": (o.classData.dp__active_date || o.classData.dp__range_start || o.classData.dp__range_start) ?? void 0,
                    "aria-disabled": o.classData.dp__cell_disabled || void 0,
                    "aria-label": (B = (ce = s(i)) == null ? void 0 : ce.day) == null ? void 0 : B.call(ce, o),
                    tabindex: !o.current && M.hideOffsetDates ? void 0 : 0,
                    "data-test-id": s(Ha)(o.value),
                    onClick: Qt((be) => l(be, o), ["prevent"]),
                    onTouchend: (be) => l(be, o, !1),
                    onKeydown: (be) => s(Ze)(be, () => M.$emit("select-date", o)),
                    onMouseenter: (be) => W(o, re, E),
                    onMouseleave: (be) => T(o),
                    onMousedown: (be) => ue(o),
                    onMouseup: he[0] || (he[0] = (be) => ee.value = !1)
                  }, [
                    ge("div", {
                      class: De(["dp__cell_inner", o.classData])
                    }, [
                      M.$slots.day && L.value(o) ? de(M.$slots, "day", {
                        key: 0,
                        day: +o.text,
                        date: o.value
                      }) : J("", !0),
                      M.$slots.day ? J("", !0) : (A(), V(ke, { key: 1 }, [
                        gt(Qe(o.text), 1)
                      ], 64)),
                      o.marker && L.value(o) ? (A(), V(ke, { key: 2 }, [
                        M.$slots.marker ? de(M.$slots, "marker", {
                          key: 0,
                          marker: o.marker,
                          day: +o.text,
                          date: o.value
                        }) : (A(), V("div", {
                          key: 1,
                          class: De(fe.value(o.marker)),
                          style: rt(o.marker.color ? { backgroundColor: o.marker.color } : {})
                        }, null, 6))
                      ], 64)) : J("", !0),
                      me.value(o.value) ? (A(), V("div", {
                        key: 3,
                        ref_for: !0,
                        ref_key: "activeTooltip",
                        ref: z,
                        class: "dp__marker_tooltip",
                        style: rt(U.value)
                      }, [
                        (Me = o.marker) != null && Me.tooltip ? (A(), V("div", {
                          key: 0,
                          class: "dp__tooltip_content",
                          onClick: D
                        }, [
                          (A(!0), V(ke, null, Be(o.marker.tooltip, (be, Se) => (A(), V("div", {
                            key: Se,
                            class: "dp__tooltip_text"
                          }, [
                            M.$slots["marker-tooltip"] ? de(M.$slots, "marker-tooltip", {
                              key: 0,
                              tooltip: be,
                              day: o.value
                            }) : J("", !0),
                            M.$slots["marker-tooltip"] ? J("", !0) : (A(), V(ke, { key: 1 }, [
                              ge("div", {
                                class: "dp__tooltip_mark",
                                style: rt(be.color ? { backgroundColor: be.color } : {})
                              }, null, 4),
                              ge("div", null, Qe(be.text), 1)
                            ], 64))
                          ]))), 128)),
                          ge("div", {
                            class: "dp__arrow_bottom_tp",
                            style: rt(q.value)
                          }, null, 4)
                        ])) : J("", !0)
                      ], 4)) : J("", !0)
                    ], 2)
                  ], 40, Vr);
                }), 128))
              ]))), 128))
            ], 32)) : J("", !0)
          ]),
          _: 3
        }, 8, ["name", "css"])
      ], 2)
    ], 2));
  }
}), yn = (e) => Array.isArray(e), Kr = (e, t, r, a) => {
  const n = te([]), u = te(/* @__PURE__ */ new Date()), d = te(), y = () => $(e.isTextInputDate), { modelValue: i, calendars: _, time: c, today: C } = aa(e, t, y), {
    defaultedMultiCalendars: m,
    defaultedStartTime: P,
    defaultedRange: U,
    defaultedConfig: N,
    defaultedTz: H,
    propDates: f,
    defaultedMultiDates: I
  } = _e(e), { validateMonthYearInRange: k, isDisabled: z, isDateRangeAllowed: q, checkMinMaxRange: ee } = Tt(e), { updateTimeValues: x, getSetDateTime: S, setTime: X, assignStartTime: O, validateTime: K, disabledTimesConfig: fe } = jn(e, c, i, a), me = Q(
    () => (h) => _.value[h] ? _.value[h].month : 0
  ), v = Q(
    () => (h) => _.value[h] ? _.value[h].year : 0
  ), L = (h) => !N.value.keepViewOnOffsetClick || h ? !0 : !d.value, ne = (h, le, w, G = !1) => {
    var ie, Xe;
    L(G) && (_.value[h] || (_.value[h] = { month: 0, year: 0 }), _.value[h].month = dn(le) ? (ie = _.value[h]) == null ? void 0 : ie.month : le, _.value[h].year = dn(w) ? (Xe = _.value[h]) == null ? void 0 : Xe.year : w);
  }, p = () => {
    e.autoApply && t("select-date");
  }, W = () => {
    P.value && O(P.value);
  };
  je(() => {
    e.shadow || (i.value || (he(), W()), $(!0), e.focusStartDate && e.startDate && he());
  });
  const T = Q(() => {
    var h;
    return (h = e.flow) != null && h.length && !e.partialFlow ? e.flowStep === e.flow.length : !0;
  }), oe = () => {
    e.autoApply && T.value && t("auto-apply", e.partialFlow ? e.flowStep !== e.flow.length : !1);
  }, $ = (h = !1) => {
    if (i.value)
      return Array.isArray(i.value) ? (n.value = i.value, l(h)) : Z(i.value, h);
    if (m.value.count && h && !e.startDate)
      return g(j(), h);
  }, Y = () => Array.isArray(i.value) && U.value.enabled ? $e(i.value[0]) === $e(i.value[1] ?? i.value[0]) : !1, g = (h = /* @__PURE__ */ new Date(), le = !1) => {
    if ((!m.value.count || !m.value.static || le) && ne(0, $e(h), ye(h)), m.value.count && (!i.value || Y() || !m.value.solo) && (!m.value.solo || le))
      for (let w = 1; w < m.value.count; w++) {
        const G = Pe(j(), { month: me.value(w - 1), year: v.value(w - 1) }), ie = An(G, { months: 1 });
        _.value[w] = { month: $e(ie), year: ye(ie) };
      }
  }, Z = (h, le) => {
    g(h), X("hours", ht(h)), X("minutes", $t(h)), X("seconds", Lt(h)), m.value.count && le && M();
  }, se = (h) => {
    if (m.value.count) {
      if (m.value.solo) return 0;
      const le = $e(h[0]), w = $e(h[1]);
      return Math.abs(w - le) < m.value.count ? 0 : 1;
    }
    return 1;
  }, R = (h, le) => {
    h[1] && U.value.showLastInRange ? g(h[se(h)], le) : g(h[0], le);
    const w = (G, ie) => [
      G(h[0]),
      h[1] ? G(h[1]) : c[ie][1]
    ];
    X("hours", w(ht, "hours")), X("minutes", w($t, "minutes")), X("seconds", w(Lt, "seconds"));
  }, ae = (h, le) => {
    if ((U.value.enabled || e.weekPicker) && !I.value.enabled)
      return R(h, le);
    if (I.value.enabled && le) {
      const w = h[h.length - 1];
      return Z(w, le);
    }
  }, l = (h) => {
    const le = i.value;
    ae(le, h), m.value.count && m.value.solo && M();
  }, D = (h, le) => {
    const w = Pe(j(), { month: me.value(le), year: v.value(le) }), G = h < 0 ? Pt(w, 1) : qt(w, 1);
    k($e(G), ye(G), h < 0, e.preventMinMaxNavigation) && (ne(le, $e(G), ye(G)), t("update-month-year", { instance: le, month: $e(G), year: ye(G) }), m.value.count && !m.value.solo && ue(le), r());
  }, ue = (h) => {
    for (let le = h - 1; le >= 0; le--) {
      const w = qt(Pe(j(), { month: me.value(le + 1), year: v.value(le + 1) }), 1);
      ne(le, $e(w), ye(w));
    }
    for (let le = h + 1; le <= m.value.count - 1; le++) {
      const w = Pt(Pe(j(), { month: me.value(le - 1), year: v.value(le - 1) }), 1);
      ne(le, $e(w), ye(w));
    }
  }, M = () => {
    if (Array.isArray(i.value) && i.value.length === 2) {
      const h = j(
        j(i.value[1] ? i.value[1] : Pt(i.value[0], 1))
      ), [le, w] = [$e(i.value[0]), ye(i.value[0])], [G, ie] = [$e(i.value[1]), ye(i.value[1])];
      (le !== G || le === G && w !== ie) && m.value.solo && ne(1, $e(h), ye(h));
    } else i.value && !Array.isArray(i.value) && (ne(0, $e(i.value), ye(i.value)), g(j()));
  }, he = () => {
    e.startDate && (ne(0, $e(j(e.startDate)), ye(j(e.startDate))), m.value.count && ue(0));
  }, pe = (h, le) => {
    if (e.monthChangeOnScroll) {
      const w = (/* @__PURE__ */ new Date()).getTime() - u.value.getTime(), G = Math.abs(h.deltaY);
      let ie = 500;
      G > 1 && (ie = 100), G > 100 && (ie = 0), w > ie && (u.value = /* @__PURE__ */ new Date(), D(e.monthChangeOnScroll !== "inverse" ? -h.deltaY : h.deltaY, le));
    }
  }, re = (h, le, w = !1) => {
    e.monthChangeOnArrows && e.vertical === w && o(h, le);
  }, o = (h, le) => {
    D(h === "right" ? -1 : 1, le);
  }, E = (h) => {
    if (f.value.markers)
      return ca(h.value, f.value.markers);
  }, ce = (h, le) => {
    switch (e.sixWeeks === !0 ? "append" : e.sixWeeks) {
      case "prepend":
        return [!0, !1];
      case "center":
        return [h == 0, !0];
      case "fair":
        return [h == 0 || le > h, !0];
      case "append":
        return [!1, !1];
      default:
        return [!1, !1];
    }
  }, B = (h, le, w, G) => {
    if (e.sixWeeks && h.length < 6) {
      const ie = 6 - h.length, Xe = (le.getDay() + 7 - G) % 7, _t = 6 - (w.getDay() + 7 - G) % 7, [Vt, $a] = ce(Xe, _t);
      for (let St = 1; St <= ie; St++)
        if ($a ? !!(St % 2) == Vt : Vt) {
          const la = h[0].days[0], Aa = Me(kt(la.value, -7), $e(le));
          h.unshift({ days: Aa });
        } else {
          const la = h[h.length - 1], Aa = la.days[la.days.length - 1], Qn = Me(kt(Aa.value, 1), $e(le));
          h.push({ days: Qn });
        }
    }
    return h;
  }, Me = (h, le) => {
    const w = j(h), G = [];
    for (let ie = 0; ie < 7; ie++) {
      const Xe = kt(w, ie), st = $e(Xe) !== le;
      G.push({
        text: e.hideOffsetDates && st ? "" : Xe.getDate(),
        value: Xe,
        current: !st,
        classData: {}
      });
    }
    return G;
  }, be = (h, le) => {
    const w = [], G = new Date(le, h), ie = new Date(le, h + 1, 0), Xe = e.weekStart, st = Ua(G, { weekStartsOn: Xe }), _t = (Vt) => {
      const $a = Me(Vt, h);
      if (w.push({ days: $a }), !w[w.length - 1].days.some(
        (St) => Ae(We(St.value), We(ie))
      )) {
        const St = kt(Vt, 7);
        _t(St);
      }
    };
    return _t(st), B(w, G, ie, Xe);
  }, Se = (h) => {
    const le = Mt(j(h.value), c.hours, c.minutes, Ue());
    t("date-update", le), I.value.enabled ? xa(le, i, I.value.limit) : i.value = le, a(), nt().then(() => {
      oe();
    });
  }, b = (h) => U.value.noDisabledRange ? Yn(n.value[0], h).some((w) => z(w)) : !1, F = () => {
    n.value = i.value ? i.value.slice() : [], n.value.length === 2 && !(U.value.fixedStart || U.value.fixedEnd) && (n.value = []);
  }, Re = (h, le) => {
    const w = [
      j(h.value),
      kt(j(h.value), +U.value.autoRange)
    ];
    q(w) ? (le && Fe(h.value), n.value = w) : t("invalid-date", h.value);
  }, Fe = (h) => {
    const le = $e(j(h)), w = ye(j(h));
    if (ne(0, le, w), m.value.count > 0)
      for (let G = 1; G < m.value.count; G++) {
        const ie = Yl(
          Pe(j(h), { year: v.value(G - 1), month: me.value(G - 1) })
        );
        ne(G, ie.month, ie.year);
      }
  }, mt = (h) => {
    if (b(h.value) || !ee(h.value, i.value, U.value.fixedStart ? 0 : 1))
      return t("invalid-date", h.value);
    n.value = Un(j(h.value), i, t, U);
  }, ve = (h, le) => {
    if (F(), U.value.autoRange) return Re(h, le);
    if (U.value.fixedStart || U.value.fixedEnd) return mt(h);
    n.value[0] ? ee(j(h.value), i.value) && !b(h.value) ? Ye(j(h.value), j(n.value[0])) ? (n.value.unshift(j(h.value)), t("range-end", n.value[0])) : (n.value[1] = j(h.value), t("range-end", n.value[1])) : (e.autoApply && t("auto-apply-invalid", h.value), t("invalid-date", h.value)) : (n.value[0] = j(h.value), t("range-start", n.value[0]));
  }, Ue = (h = !0) => e.enableSeconds ? Array.isArray(c.seconds) ? h ? c.seconds[0] : c.seconds[1] : c.seconds : 0, lt = (h) => {
    n.value[h] = Mt(
      n.value[h],
      c.hours[h],
      c.minutes[h],
      Ue(h !== 1)
    );
  }, ga = () => {
    var h, le;
    n.value[0] && n.value[1] && +((h = n.value) == null ? void 0 : h[0]) > +((le = n.value) == null ? void 0 : le[1]) && (n.value.reverse(), t("range-start", n.value[0]), t("range-end", n.value[1]));
  }, na = () => {
    n.value.length && (n.value[0] && !n.value[1] ? lt(0) : (lt(0), lt(1), a()), ga(), i.value = n.value.slice(), pa(n.value, t, e.autoApply, e.modelAuto));
  }, ha = (h, le = !1) => {
    if (z(h.value) || !h.current && e.hideOffsetDates) return t("invalid-date", h.value);
    if (d.value = JSON.parse(JSON.stringify(h)), !U.value.enabled) return Se(h);
    yn(c.hours) && yn(c.minutes) && !I.value.enabled && (ve(h, le), na());
  }, ba = (h, le) => {
    var G;
    ne(h, le.month, le.year, !0), m.value.count && !m.value.solo && ue(h), t("update-month-year", { instance: h, month: le.month, year: le.year }), r(m.value.solo ? h : void 0);
    const w = (G = e.flow) != null && G.length ? e.flow[e.flowStep] : void 0;
    !le.fromNav && (w === Ge.month || w === Ge.year) && a();
  }, ka = (h, le) => {
    Hn({
      value: h,
      modelValue: i,
      range: U.value.enabled,
      timezone: le ? void 0 : H.value.timezone
    }), p(), e.multiCalendars && nt().then(() => $(!0));
  }, wa = () => {
    const h = Qa(j(), H.value);
    !U.value.enabled && !I.value.enabled ? i.value = h : i.value && Array.isArray(i.value) && i.value[0] ? I.value.enabled ? i.value = [...i.value, h] : i.value = Ye(h, i.value[0]) ? [h, i.value[0]] : [i.value[0], h] : i.value = [h], p();
  }, Da = () => {
    if (Array.isArray(i.value))
      if (I.value.enabled) {
        const h = Ma();
        i.value[i.value.length - 1] = S(h);
      } else
        i.value = i.value.map((h, le) => h && S(h, le));
    else
      i.value = S(i.value);
    t("time-update");
  }, Ma = () => Array.isArray(i.value) && i.value.length ? i.value[i.value.length - 1] : null;
  return {
    calendars: _,
    modelValue: i,
    month: me,
    year: v,
    time: c,
    disabledTimesConfig: fe,
    today: C,
    validateTime: K,
    getCalendarDays: be,
    getMarker: E,
    handleScroll: pe,
    handleSwipe: o,
    handleArrow: re,
    selectDate: ha,
    updateMonthYear: ba,
    presetDate: ka,
    selectCurrentDate: wa,
    updateTime: (h, le = !0, w = !1) => {
      x(h, le, w, Da);
    },
    assignMonthAndYear: g,
    setStartTime: W
  };
}, Gr = { key: 0 }, Qr = /* @__PURE__ */ Ve({
  __name: "DatePicker",
  props: {
    ...ct
  },
  emits: [
    "tooltip-open",
    "tooltip-close",
    "mount",
    "update:internal-model-value",
    "update-flow-step",
    "reset-flow",
    "auto-apply",
    "focus-menu",
    "select-date",
    "range-start",
    "range-end",
    "invalid-fixed-range",
    "time-update",
    "am-pm-change",
    "time-picker-open",
    "time-picker-close",
    "recalculate-position",
    "update-month-year",
    "auto-apply-invalid",
    "date-update",
    "invalid-date",
    "overlay-toggle"
  ],
  setup(e, { expose: t, emit: r }) {
    const a = r, n = e, {
      calendars: u,
      month: d,
      year: y,
      modelValue: i,
      time: _,
      disabledTimesConfig: c,
      today: C,
      validateTime: m,
      getCalendarDays: P,
      getMarker: U,
      handleArrow: N,
      handleScroll: H,
      handleSwipe: f,
      selectDate: I,
      updateMonthYear: k,
      presetDate: z,
      selectCurrentDate: q,
      updateTime: ee,
      assignMonthAndYear: x,
      setStartTime: S
    } = Kr(n, a, Y, g), X = Bt(), { setHoverDate: O, getDayClassData: K, clearHoverDate: fe } = fo(i, n), { defaultedMultiCalendars: me } = _e(n), v = te([]), L = te([]), ne = te(null), p = tt(X, "calendar"), W = tt(X, "monthYear"), T = tt(X, "timePicker"), oe = (re) => {
      n.shadow || a("mount", re);
    };
    ot(
      u,
      () => {
        n.shadow || setTimeout(() => {
          a("recalculate-position");
        }, 0);
      },
      { deep: !0 }
    ), ot(
      me,
      (re, o) => {
        re.count - o.count > 0 && x();
      },
      { deep: !0 }
    );
    const $ = Q(() => (re) => P(d.value(re), y.value(re)).map((o) => ({
      ...o,
      days: o.days.map((E) => (E.marker = U(E), E.classData = K(E), E))
    })));
    function Y(re) {
      var o;
      re || re === 0 ? (o = L.value[re]) == null || o.triggerTransition(d.value(re), y.value(re)) : L.value.forEach((E, ce) => E.triggerTransition(d.value(ce), y.value(ce)));
    }
    function g() {
      a("update-flow-step");
    }
    const Z = (re, o = !1) => {
      I(re, o), n.spaceConfirm && a("select-date");
    }, se = (re, o, E = 0) => {
      var ce;
      (ce = v.value[E]) == null || ce.toggleMonthPicker(re, o);
    }, R = (re, o, E = 0) => {
      var ce;
      (ce = v.value[E]) == null || ce.toggleYearPicker(re, o);
    }, ae = (re, o, E) => {
      var ce;
      (ce = ne.value) == null || ce.toggleTimePicker(re, o, E);
    }, l = (re, o) => {
      var E;
      if (!n.range) {
        const ce = i.value ? i.value : C, B = o ? new Date(o) : ce, Me = re ? Ua(B, { weekStartsOn: 1 }) : wn(B, { weekStartsOn: 1 });
        I({
          value: Me,
          current: $e(B) === d.value(0),
          text: "",
          classData: {}
        }), (E = document.getElementById(Ha(Me))) == null || E.focus();
      }
    }, D = (re) => {
      var o;
      (o = v.value[0]) == null || o.handleMonthYearChange(re, !0);
    }, ue = (re) => {
      k(0, { month: d.value(0), year: y.value(0) + (re ? 1 : -1), fromNav: !0 });
    }, M = (re, o) => {
      re === Ge.time && a(`time-picker-${o ? "open" : "close"}`), a("overlay-toggle", { open: o, overlay: re });
    }, he = (re) => {
      a("overlay-toggle", { open: !1, overlay: re }), a("focus-menu");
    };
    return t({
      clearHoverDate: fe,
      presetDate: z,
      selectCurrentDate: q,
      toggleMonthPicker: se,
      toggleYearPicker: R,
      toggleTimePicker: ae,
      handleArrow: N,
      updateMonthYear: k,
      getSidebarProps: () => ({
        modelValue: i,
        month: d,
        year: y,
        time: _,
        updateTime: ee,
        updateMonthYear: k,
        selectDate: I,
        presetDate: z
      }),
      changeMonth: D,
      changeYear: ue,
      selectWeekDate: l,
      setStartTime: S
    }), (re, o) => (A(), V(ke, null, [
      at(ma, {
        "multi-calendars": s(me).count,
        collapse: re.collapse,
        "is-mobile": re.isMobile
      }, {
        default: we(({ instance: E, index: ce }) => [
          re.disableMonthYearSelect ? J("", !0) : (A(), Te(Fr, He({
            key: 0,
            ref: (B) => {
              B && (v.value[ce] = B);
            },
            months: s(Sn)(re.formatLocale, re.locale, re.monthNameFormat),
            years: s(qa)(re.yearRange, re.locale, re.reverseYears),
            month: s(d)(E),
            year: s(y)(E),
            instance: E
          }, re.$props, {
            onMount: o[0] || (o[0] = (B) => oe(s(Rt).header)),
            onResetFlow: o[1] || (o[1] = (B) => re.$emit("reset-flow")),
            onUpdateMonthYear: (B) => s(k)(E, B),
            onOverlayClosed: he,
            onOverlayOpened: o[2] || (o[2] = (B) => re.$emit("overlay-toggle", { open: !0, overlay: B }))
          }), qe({ _: 2 }, [
            Be(s(W), (B, Me) => ({
              name: B,
              fn: we((be) => [
                de(re.$slots, B, ze(xe(be)))
              ])
            }))
          ]), 1040, ["months", "years", "month", "year", "instance", "onUpdateMonthYear"])),
          at(jr, He({
            ref: (B) => {
              B && (L.value[ce] = B);
            },
            "mapped-dates": $.value(E),
            month: s(d)(E),
            year: s(y)(E),
            instance: E
          }, re.$props, {
            onSelectDate: (B) => s(I)(B, E !== 1),
            onHandleSpace: (B) => Z(B, E !== 1),
            onSetHoverDate: o[3] || (o[3] = (B) => s(O)(B)),
            onHandleScroll: (B) => s(H)(B, E),
            onHandleSwipe: (B) => s(f)(B, E),
            onMount: o[4] || (o[4] = (B) => oe(s(Rt).calendar)),
            onResetFlow: o[5] || (o[5] = (B) => re.$emit("reset-flow")),
            onTooltipOpen: o[6] || (o[6] = (B) => re.$emit("tooltip-open", B)),
            onTooltipClose: o[7] || (o[7] = (B) => re.$emit("tooltip-close", B))
          }), qe({ _: 2 }, [
            Be(s(p), (B, Me) => ({
              name: B,
              fn: we((be) => [
                de(re.$slots, B, ze(xe({ ...be })))
              ])
            }))
          ]), 1040, ["mapped-dates", "month", "year", "instance", "onSelectDate", "onHandleSpace", "onHandleScroll", "onHandleSwipe"])
        ]),
        _: 3
      }, 8, ["multi-calendars", "collapse", "is-mobile"]),
      re.enableTimePicker ? (A(), V("div", Gr, [
        re.$slots["time-picker"] ? de(re.$slots, "time-picker", ze(He({ key: 0 }, { time: s(_), updateTime: s(ee) }))) : (A(), Te(Vn, He({
          key: 1,
          ref_key: "timePickerRef",
          ref: ne
        }, re.$props, {
          hours: s(_).hours,
          minutes: s(_).minutes,
          seconds: s(_).seconds,
          "internal-model-value": re.internalModelValue,
          "disabled-times-config": s(c),
          "validate-time": s(m),
          onMount: o[8] || (o[8] = (E) => oe(s(Rt).timePicker)),
          "onUpdate:hours": o[9] || (o[9] = (E) => s(ee)(E)),
          "onUpdate:minutes": o[10] || (o[10] = (E) => s(ee)(E, !1)),
          "onUpdate:seconds": o[11] || (o[11] = (E) => s(ee)(E, !1, !0)),
          onResetFlow: o[12] || (o[12] = (E) => re.$emit("reset-flow")),
          onOverlayClosed: o[13] || (o[13] = (E) => M(E, !1)),
          onOverlayOpened: o[14] || (o[14] = (E) => M(E, !0)),
          onAmPmChange: o[15] || (o[15] = (E) => re.$emit("am-pm-change", E))
        }), qe({ _: 2 }, [
          Be(s(T), (E, ce) => ({
            name: E,
            fn: we((B) => [
              de(re.$slots, E, ze(xe(B)))
            ])
          }))
        ]), 1040, ["hours", "minutes", "seconds", "internal-model-value", "disabled-times-config", "validate-time"]))
      ])) : J("", !0)
    ], 64));
  }
}), qr = (e, t) => {
  const r = te(), {
    defaultedMultiCalendars: a,
    defaultedConfig: n,
    defaultedHighlight: u,
    defaultedRange: d,
    propDates: y,
    defaultedFilters: i,
    defaultedMultiDates: _
  } = _e(e), { modelValue: c, year: C, month: m, calendars: P } = aa(e, t), { isDisabled: U } = Tt(e), { selectYear: N, groupedYears: H, showYearPicker: f, isDisabled: I, toggleYearPicker: k, handleYearSelect: z, handleYear: q } = Wn({
    modelValue: c,
    multiCalendars: a,
    range: d,
    highlight: u,
    calendars: P,
    propDates: y,
    month: m,
    year: C,
    filters: i,
    props: e,
    emit: t
  }), ee = (p, W) => [p, W].map((T) => vt(T, "MMMM", { locale: e.formatLocale })).join("-"), x = Q(() => (p) => c.value ? Array.isArray(c.value) ? c.value.some((W) => ln(p, W)) : ln(c.value, p) : !1), S = (p) => {
    if (d.value.enabled) {
      if (Array.isArray(c.value)) {
        const W = Ae(p, c.value[0]) || Ae(p, c.value[1]);
        return Jt(c.value, r.value, p) && !W;
      }
      return !1;
    }
    return !1;
  }, X = (p, W) => p.quarter === on(W) && p.year === ye(W), O = (p) => typeof u.value == "function" ? u.value({ quarter: on(p), year: ye(p) }) : !!u.value.quarters.find((W) => X(W, p)), K = Q(() => (p) => {
    const W = Pe(/* @__PURE__ */ new Date(), { year: C.value(p) });
    return ml({
      start: da(W),
      end: $n(W)
    }).map((T) => {
      const oe = pl(T), $ = rn(T), Y = U(T), g = S(oe), Z = O(oe);
      return {
        text: ee(oe, $),
        value: oe,
        active: x.value(oe),
        highlighted: Z,
        disabled: Y,
        isBetween: g
      };
    });
  }), fe = (p) => {
    xa(p, c, _.value.limit), t("auto-apply", !0);
  }, me = (p) => {
    c.value = en(c, p, t), pa(c.value, t, e.autoApply, e.modelAuto);
  }, v = (p) => {
    c.value = p, t("auto-apply");
  };
  return {
    defaultedConfig: n,
    defaultedMultiCalendars: a,
    groupedYears: H,
    year: C,
    isDisabled: I,
    quarters: K,
    showYearPicker: f,
    modelValue: c,
    setHoverDate: (p) => {
      r.value = p;
    },
    selectYear: N,
    selectQuarter: (p, W, T) => {
      if (!T)
        return P.value[W].month = $e(rn(p)), _.value.enabled ? fe(p) : d.value.enabled ? me(p) : v(p);
    },
    toggleYearPicker: k,
    handleYearSelect: z,
    handleYear: q
  };
}, Xr = { class: "dp--quarter-items" }, Jr = ["data-test-id", "disabled", "onClick", "onMouseover"], Zr = /* @__PURE__ */ Ve({
  compatConfig: {
    MODE: 3
  },
  __name: "QuarterPicker",
  props: {
    ...ct
  },
  emits: [
    "update:internal-model-value",
    "reset-flow",
    "overlay-closed",
    "auto-apply",
    "range-start",
    "range-end",
    "overlay-toggle",
    "update-month-year"
  ],
  setup(e, { expose: t, emit: r }) {
    const a = r, n = e, u = Bt(), d = tt(u, "yearMode"), {
      defaultedMultiCalendars: y,
      defaultedConfig: i,
      groupedYears: _,
      year: c,
      isDisabled: C,
      quarters: m,
      modelValue: P,
      showYearPicker: U,
      setHoverDate: N,
      selectQuarter: H,
      toggleYearPicker: f,
      handleYearSelect: I,
      handleYear: k
    } = qr(n, a);
    return t({ getSidebarProps: () => ({
      modelValue: P,
      year: c,
      selectQuarter: H,
      handleYearSelect: I,
      handleYear: k
    }) }), (q, ee) => (A(), Te(ma, {
      "multi-calendars": s(y).count,
      collapse: q.collapse,
      stretch: "",
      "is-mobile": q.isMobile
    }, {
      default: we(({ instance: x }) => [
        ge("div", {
          class: "dp-quarter-picker-wrap",
          style: rt({ minHeight: `${s(i).modeHeight}px` })
        }, [
          q.$slots["top-extra"] ? de(q.$slots, "top-extra", {
            key: 0,
            value: q.internalModelValue
          }) : J("", !0),
          ge("div", null, [
            at(zn, He(q.$props, {
              items: s(_)(x),
              instance: x,
              "show-year-picker": s(U)[x],
              year: s(c)(x),
              "is-disabled": (S) => s(C)(x, S),
              onHandleYear: (S) => s(k)(x, S),
              onYearSelect: (S) => s(I)(S, x),
              onToggleYearPicker: (S) => s(f)(x, S == null ? void 0 : S.flow, S == null ? void 0 : S.show)
            }), qe({ _: 2 }, [
              Be(s(d), (S, X) => ({
                name: S,
                fn: we((O) => [
                  de(q.$slots, S, ze(xe(O)))
                ])
              }))
            ]), 1040, ["items", "instance", "show-year-picker", "year", "is-disabled", "onHandleYear", "onYearSelect", "onToggleYearPicker"])
          ]),
          ge("div", Xr, [
            (A(!0), V(ke, null, Be(s(m)(x), (S, X) => (A(), V("div", { key: X }, [
              ge("button", {
                type: "button",
                class: De(["dp--qr-btn", {
                  "dp--qr-btn-active": S.active,
                  "dp--qr-btn-between": S.isBetween,
                  "dp--qr-btn-disabled": S.disabled,
                  "dp--highlighted": S.highlighted
                }]),
                "data-test-id": S.value,
                disabled: S.disabled,
                onClick: (O) => s(H)(S.value, x, S.disabled),
                onMouseover: (O) => s(N)(S.value)
              }, [
                q.$slots.quarter ? de(q.$slots, "quarter", {
                  key: 0,
                  value: S.value,
                  text: S.text
                }) : (A(), V(ke, { key: 1 }, [
                  gt(Qe(S.text), 1)
                ], 64))
              ], 42, Jr)
            ]))), 128))
          ])
        ], 4)
      ]),
      _: 3
    }, 8, ["multi-calendars", "collapse", "is-mobile"]));
  }
}), Kn = (e, t) => {
  const r = te(0);
  je(() => {
    a(), window.addEventListener("resize", a, { passive: !0 });
  }), xt(() => {
    window.removeEventListener("resize", a);
  });
  const a = () => {
    r.value = window.document.documentElement.clientWidth;
  };
  return {
    isMobile: Q(
      () => r.value <= e.value.mobileBreakpoint && !t ? !0 : void 0
    )
  };
}, xr = ["id", "tabindex", "role", "aria-label"], eo = {
  key: 0,
  class: "dp--menu-load-container"
}, to = {
  key: 1,
  class: "dp--menu-header"
}, ao = ["data-dp-mobile"], no = {
  key: 0,
  class: "dp__sidebar_left"
}, lo = ["data-dp-mobile"], ro = ["data-test-id", "data-dp-mobile", "onClick", "onKeydown"], oo = {
  key: 2,
  class: "dp__sidebar_right"
}, so = {
  key: 3,
  class: "dp__action_extra"
}, gn = /* @__PURE__ */ Ve({
  compatConfig: {
    MODE: 3
  },
  __name: "DatepickerMenu",
  props: {
    ...va,
    shadow: { type: Boolean, default: !1 },
    openOnTop: { type: Boolean, default: !1 },
    internalModelValue: { type: [Date, Array], default: null },
    noOverlayFocus: { type: Boolean, default: !1 },
    collapse: { type: Boolean, default: !1 },
    getInputRect: { type: Function, default: () => ({}) },
    isTextInputDate: { type: Boolean, default: !1 }
  },
  emits: [
    "close-picker",
    "select-date",
    "auto-apply",
    "time-update",
    "flow-step",
    "update-month-year",
    "invalid-select",
    "update:internal-model-value",
    "recalculate-position",
    "invalid-fixed-range",
    "tooltip-open",
    "tooltip-close",
    "time-picker-open",
    "time-picker-close",
    "am-pm-change",
    "range-start",
    "range-end",
    "auto-apply-invalid",
    "date-update",
    "invalid-date",
    "overlay-toggle",
    "menu-blur"
  ],
  setup(e, { expose: t, emit: r }) {
    const a = r, n = e, u = te(null), d = Q(() => {
      const { openOnTop: b, ...F } = n;
      return {
        ...F,
        isMobile: N.value,
        flowStep: K.value,
        menuWrapRef: u.value
      };
    }), { setMenuFocused: y, setShiftKey: i, control: _ } = Ln(), c = Bt(), { defaultedTextInput: C, defaultedInline: m, defaultedConfig: P, defaultedUI: U } = _e(n), { isMobile: N } = Kn(P, n.shadow), H = te(null), f = te(0), I = te(null), k = te(!1), z = te(null), q = te(!1);
    je(() => {
      if (!n.shadow) {
        k.value = !0, ee(), window.addEventListener("resize", ee);
        const b = Le(u);
        if (b && !C.value.enabled && !m.value.enabled && (y(!0), W()), b) {
          const F = (Re) => {
            q.value = !0, P.value.allowPreventDefault && Re.preventDefault(), Dt(Re, P.value, !0);
          };
          b.addEventListener("pointerdown", F), b.addEventListener("mousedown", F);
        }
      }
      document.addEventListener("mousedown", be);
    }), xt(() => {
      window.removeEventListener("resize", ee), document.addEventListener("mousedown", be);
    });
    const ee = () => {
      const b = Le(I);
      b && (f.value = b.getBoundingClientRect().width);
    }, { arrowRight: x, arrowLeft: S, arrowDown: X, arrowUp: O } = At(), { flowStep: K, updateFlowStep: fe, childMount: me, resetFlow: v, handleFlow: L } = vo(n, a, z), ne = Q(() => n.monthPicker ? yr : n.yearPicker ? hr : n.timePicker ? Br : n.quarterPicker ? Zr : Qr), p = Q(() => {
      var Re;
      if (P.value.arrowLeft) return P.value.arrowLeft;
      const b = (Re = u.value) == null ? void 0 : Re.getBoundingClientRect(), F = n.getInputRect();
      return (F == null ? void 0 : F.width) < (f == null ? void 0 : f.value) && (F == null ? void 0 : F.left) <= ((b == null ? void 0 : b.left) ?? 0) ? `${(F == null ? void 0 : F.width) / 2}px` : (F == null ? void 0 : F.right) >= ((b == null ? void 0 : b.right) ?? 0) && (F == null ? void 0 : F.width) < (f == null ? void 0 : f.value) ? `${(f == null ? void 0 : f.value) - (F == null ? void 0 : F.width) / 2}px` : "50%";
    }), W = () => {
      const b = Le(u);
      b && b.focus({ preventScroll: !0 });
    }, T = Q(() => {
      var b;
      return ((b = z.value) == null ? void 0 : b.getSidebarProps()) || {};
    }), oe = () => {
      n.openOnTop && a("recalculate-position");
    }, $ = tt(c, "action"), Y = Q(() => n.monthPicker || n.yearPicker ? tt(c, "monthYear") : n.timePicker ? tt(c, "timePicker") : tt(c, "shared")), g = Q(() => n.openOnTop ? "dp__arrow_bottom" : "dp__arrow_top"), Z = Q(() => ({
      dp__menu_disabled: n.disabled,
      dp__menu_readonly: n.readonly,
      "dp-menu-loading": n.loading
    })), se = Q(
      () => ({
        dp__menu: !0,
        dp__menu_index: !m.value.enabled,
        dp__relative: m.value.enabled,
        ...U.value.menu ?? {}
      })
    ), R = (b) => {
      Dt(b, P.value, !0);
    }, ae = () => {
      n.escClose && a("close-picker");
    }, l = (b) => {
      if (n.arrowNavigation) {
        if (b === Je.up) return O();
        if (b === Je.down) return X();
        if (b === Je.left) return S();
        if (b === Je.right) return x();
      } else b === Je.left || b === Je.up ? pe("handleArrow", Je.left, 0, b === Je.up) : pe("handleArrow", Je.right, 0, b === Je.down);
    }, D = (b) => {
      i(b.shiftKey), !n.disableMonthYearSelect && b.code === Oe.tab && b.target.classList.contains("dp__menu") && _.value.shiftKeyInMenu && (b.preventDefault(), Dt(b, P.value, !0), a("close-picker"));
    }, ue = () => {
      W(), a("time-picker-close");
    }, M = (b) => {
      var F, Re, Fe;
      (F = z.value) == null || F.toggleTimePicker(!1, !1), (Re = z.value) == null || Re.toggleMonthPicker(!1, !1, b), (Fe = z.value) == null || Fe.toggleYearPicker(!1, !1, b);
    }, he = (b, F = 0) => {
      var Re, Fe, mt;
      return b === "month" ? (Re = z.value) == null ? void 0 : Re.toggleMonthPicker(!1, !0, F) : b === "year" ? (Fe = z.value) == null ? void 0 : Fe.toggleYearPicker(!1, !0, F) : b === "time" ? (mt = z.value) == null ? void 0 : mt.toggleTimePicker(!0, !1) : M(F);
    }, pe = (b, ...F) => {
      var Re, Fe;
      (Re = z.value) != null && Re[b] && ((Fe = z.value) == null || Fe[b](...F));
    }, re = () => {
      pe("selectCurrentDate");
    }, o = (b, F) => {
      pe("presetDate", Zn(b), F);
    }, E = () => {
      pe("clearHoverDate");
    }, ce = (b, F) => {
      pe("updateMonthYear", b, F);
    }, B = (b, F) => {
      b.preventDefault(), l(F);
    }, Me = (b) => {
      var F, Re, Fe;
      if (D(b), b.key === Oe.home || b.key === Oe.end)
        return pe(
          "selectWeekDate",
          b.key === Oe.home,
          b.target.getAttribute("id")
        );
      switch ((b.key === Oe.pageUp || b.key === Oe.pageDown) && (b.shiftKey ? (pe("changeYear", b.key === Oe.pageUp), (F = La(u.value, "overlay-year")) == null || F.focus()) : (pe("changeMonth", b.key === Oe.pageUp), (Re = La(u.value, b.key === Oe.pageUp ? "action-prev" : "action-next")) == null || Re.focus()), b.target.getAttribute("id") && ((Fe = u.value) == null || Fe.focus({ preventScroll: !0 }))), b.key) {
        case Oe.esc:
          return ae();
        case Oe.arrowLeft:
          return B(b, Je.left);
        case Oe.arrowRight:
          return B(b, Je.right);
        case Oe.arrowUp:
          return B(b, Je.up);
        case Oe.arrowDown:
          return B(b, Je.down);
        default:
          return;
      }
    }, be = (b) => {
      var F;
      m.value.enabled && !m.value.input && !((F = u.value) != null && F.contains(b.target)) && q.value && (q.value = !1, a("menu-blur"));
    };
    return t({
      updateMonthYear: ce,
      switchView: he,
      handleFlow: L,
      onValueCleared: () => {
        var b, F;
        (F = (b = z.value) == null ? void 0 : b.setStartTime) == null || F.call(b);
      }
    }), (b, F) => {
      var Re, Fe, mt;
      return A(), V("div", {
        id: b.uid ? `dp-menu-${b.uid}` : void 0,
        ref_key: "dpMenuRef",
        ref: u,
        tabindex: s(m).enabled ? void 0 : "0",
        role: s(m).enabled ? void 0 : "dialog",
        "aria-label": (Re = b.ariaLabels) == null ? void 0 : Re.menu,
        class: De(se.value),
        style: rt({ "--dp-arrow-left": p.value }),
        onMouseleave: E,
        onClick: R,
        onKeydown: Me
      }, [
        (b.disabled || b.readonly) && s(m).enabled || b.loading ? (A(), V("div", {
          key: 0,
          class: De(Z.value)
        }, [
          b.loading ? (A(), V("div", eo, F[19] || (F[19] = [
            ge("span", { class: "dp--menu-loader" }, null, -1)
          ]))) : J("", !0)
        ], 2)) : J("", !0),
        b.$slots["menu-header"] ? (A(), V("div", to, [
          de(b.$slots, "menu-header")
        ])) : J("", !0),
        !s(m).enabled && !b.teleportCenter ? (A(), V("div", {
          key: 2,
          class: De(g.value)
        }, null, 2)) : J("", !0),
        ge("div", {
          ref_key: "innerMenuRef",
          ref: I,
          class: De({
            dp__menu_content_wrapper: ((Fe = b.presetDates) == null ? void 0 : Fe.length) || !!b.$slots["left-sidebar"] || !!b.$slots["right-sidebar"],
            "dp--menu-content-wrapper-collapsed": e.collapse && (((mt = b.presetDates) == null ? void 0 : mt.length) || !!b.$slots["left-sidebar"] || !!b.$slots["right-sidebar"])
          }),
          "data-dp-mobile": s(N),
          style: rt({ "--dp-menu-width": `${f.value}px` })
        }, [
          b.$slots["left-sidebar"] ? (A(), V("div", no, [
            de(b.$slots, "left-sidebar", ze(xe(T.value)))
          ])) : J("", !0),
          b.presetDates.length ? (A(), V("div", {
            key: 1,
            class: De({ "dp--preset-dates-collapsed": e.collapse, "dp--preset-dates": !0 }),
            "data-dp-mobile": s(N)
          }, [
            (A(!0), V(ke, null, Be(b.presetDates, (ve, Ue) => (A(), V(ke, { key: Ue }, [
              ve.slot ? de(b.$slots, ve.slot, {
                key: 0,
                presetDate: o,
                label: ve.label,
                value: ve.value
              }) : (A(), V("button", {
                key: 1,
                type: "button",
                style: rt(ve.style || {}),
                class: De(["dp__btn dp--preset-range", { "dp--preset-range-collapsed": e.collapse }]),
                "data-test-id": ve.testId ?? void 0,
                "data-dp-mobile": s(N),
                onClick: Qt((lt) => o(ve.value, ve.noTz), ["prevent"]),
                onKeydown: (lt) => s(Ze)(lt, () => o(ve.value, ve.noTz), !0)
              }, Qe(ve.label), 47, ro))
            ], 64))), 128))
          ], 10, lo)) : J("", !0),
          ge("div", {
            ref_key: "calendarWrapperRef",
            ref: H,
            class: "dp__instance_calendar",
            role: "document"
          }, [
            (A(), Te(fa(ne.value), He({
              ref_key: "dynCmpRef",
              ref: z
            }, d.value, {
              "flow-step": s(K),
              onMount: s(me),
              onUpdateFlowStep: s(fe),
              onResetFlow: s(v),
              onFocusMenu: W,
              onSelectDate: F[0] || (F[0] = (ve) => b.$emit("select-date")),
              onDateUpdate: F[1] || (F[1] = (ve) => b.$emit("date-update", ve)),
              onTooltipOpen: F[2] || (F[2] = (ve) => b.$emit("tooltip-open", ve)),
              onTooltipClose: F[3] || (F[3] = (ve) => b.$emit("tooltip-close", ve)),
              onAutoApply: F[4] || (F[4] = (ve) => b.$emit("auto-apply", ve)),
              onRangeStart: F[5] || (F[5] = (ve) => b.$emit("range-start", ve)),
              onRangeEnd: F[6] || (F[6] = (ve) => b.$emit("range-end", ve)),
              onInvalidFixedRange: F[7] || (F[7] = (ve) => b.$emit("invalid-fixed-range", ve)),
              onTimeUpdate: F[8] || (F[8] = (ve) => b.$emit("time-update")),
              onAmPmChange: F[9] || (F[9] = (ve) => b.$emit("am-pm-change", ve)),
              onTimePickerOpen: F[10] || (F[10] = (ve) => b.$emit("time-picker-open", ve)),
              onTimePickerClose: ue,
              onRecalculatePosition: oe,
              onUpdateMonthYear: F[11] || (F[11] = (ve) => b.$emit("update-month-year", ve)),
              onAutoApplyInvalid: F[12] || (F[12] = (ve) => b.$emit("auto-apply-invalid", ve)),
              onInvalidDate: F[13] || (F[13] = (ve) => b.$emit("invalid-date", ve)),
              onOverlayToggle: F[14] || (F[14] = (ve) => b.$emit("overlay-toggle", ve)),
              "onUpdate:internalModelValue": F[15] || (F[15] = (ve) => b.$emit("update:internal-model-value", ve))
            }), qe({ _: 2 }, [
              Be(Y.value, (ve, Ue) => ({
                name: ve,
                fn: we((lt) => [
                  de(b.$slots, ve, ze(xe({ ...lt })))
                ])
              }))
            ]), 1040, ["flow-step", "onMount", "onUpdateFlowStep", "onResetFlow"]))
          ], 512),
          b.$slots["right-sidebar"] ? (A(), V("div", oo, [
            de(b.$slots, "right-sidebar", ze(xe(T.value)))
          ])) : J("", !0),
          b.$slots["action-extra"] ? (A(), V("div", so, [
            b.$slots["action-extra"] ? de(b.$slots, "action-extra", {
              key: 0,
              selectCurrentDate: re
            }) : J("", !0)
          ])) : J("", !0)
        ], 14, ao),
        !b.autoApply || s(P).keepActionRow ? (A(), Te(sr, He({
          key: 3,
          "menu-mount": k.value
        }, d.value, {
          "calendar-width": f.value,
          onClosePicker: F[16] || (F[16] = (ve) => b.$emit("close-picker")),
          onSelectDate: F[17] || (F[17] = (ve) => b.$emit("select-date")),
          onInvalidSelect: F[18] || (F[18] = (ve) => b.$emit("invalid-select")),
          onSelectNow: re
        }), qe({ _: 2 }, [
          Be(s($), (ve, Ue) => ({
            name: ve,
            fn: we((lt) => [
              de(b.$slots, ve, ze(xe({ ...lt })))
            ])
          }))
        ]), 1040, ["menu-mount", "calendar-width"])) : J("", !0)
      ], 46, xr);
    };
  }
});
var It = /* @__PURE__ */ ((e) => (e.center = "center", e.left = "left", e.right = "right", e))(It || {});
const uo = ({
  menuRef: e,
  menuRefInner: t,
  inputRef: r,
  pickerWrapperRef: a,
  inline: n,
  emit: u,
  props: d,
  slots: y
}) => {
  const { defaultedConfig: i } = _e(d), _ = te({}), c = te(!1), C = te({
    top: "0",
    left: "0"
  }), m = te(!1), P = Gt(d, "teleportCenter");
  ot(P, () => {
    C.value = JSON.parse(JSON.stringify({})), q();
  });
  const U = (p) => {
    if (d.teleport) {
      const W = p.getBoundingClientRect();
      return {
        left: W.left + window.scrollX,
        top: W.top + window.scrollY
      };
    }
    return { top: 0, left: 0 };
  }, N = (p, W) => {
    C.value.left = `${p + W - _.value.width}px`;
  }, H = (p) => {
    C.value.left = `${p}px`;
  }, f = (p, W) => {
    d.position === It.left && H(p), d.position === It.right && N(p, W), d.position === It.center && (C.value.left = `${p + W / 2 - _.value.width / 2}px`);
  }, I = (p) => {
    const { width: W, height: T } = p.getBoundingClientRect(), { top: oe, left: $ } = U(p);
    return { top: +oe, left: +$, width: W, height: T };
  }, k = () => {
    C.value.left = "50%", C.value.top = "50%", C.value.transform = "translate(-50%, -50%)", C.value.position = "fixed", delete C.value.opacity;
  }, z = () => {
    const p = Le(r);
    C.value = d.altPosition(p);
  }, q = (p = !0) => {
    var W;
    if (!n.value.enabled) {
      if (P.value) return k();
      if (d.altPosition !== null) return z();
      if (p) {
        const T = d.teleport ? (W = t.value) == null ? void 0 : W.$el : e.value;
        T && (_.value = T.getBoundingClientRect()), u("recalculate-position");
      }
      return fe();
    }
  }, ee = ({ inputEl: p, left: W, width: T }) => {
    window.screen.width > 768 && !c.value && f(W, T), X(p);
  }, x = (p) => {
    const { top: W, left: T, height: oe, width: $ } = I(p);
    C.value.top = `${oe + W + +d.offset}px`, m.value = !1, c.value || (C.value.left = `${T + $ / 2 - _.value.width / 2}px`), ee({ inputEl: p, left: T, width: $ });
  }, S = (p) => {
    const { top: W, left: T, width: oe } = I(p);
    C.value.top = `${W - +d.offset - _.value.height}px`, m.value = !0, ee({ inputEl: p, left: T, width: oe });
  }, X = (p) => {
    if (d.autoPosition) {
      const { left: W, width: T } = I(p), { left: oe, right: $ } = _.value;
      if (!c.value) {
        if (Math.abs(oe) !== Math.abs($)) {
          if (oe <= 0)
            return c.value = !0, H(W);
          if ($ >= document.documentElement.clientWidth)
            return c.value = !0, N(W, T);
        }
        return f(W, T);
      }
    }
  }, O = () => {
    const p = Le(r);
    if (p) {
      if (d.autoPosition === it.top) return it.top;
      if (d.autoPosition === it.bottom) return it.bottom;
      const { height: W } = _.value, { top: T, height: oe } = p.getBoundingClientRect(), Y = window.innerHeight - T - oe, g = T;
      return W <= Y ? it.bottom : W > Y && W <= g ? it.top : Y >= g ? it.bottom : it.top;
    }
    return it.bottom;
  }, K = (p) => O() === it.bottom ? x(p) : S(p), fe = () => {
    const p = Le(r);
    if (p)
      return d.autoPosition ? K(p) : x(p);
  }, me = function(p) {
    if (p) {
      const W = p.scrollHeight > p.clientHeight, oe = window.getComputedStyle(p).overflowY.indexOf("hidden") !== -1;
      return W && !oe;
    }
    return !0;
  }, v = function(p) {
    return !p || p === document.body || p.nodeType === Node.DOCUMENT_FRAGMENT_NODE ? window : me(p) ? p : v(
      p.assignedSlot && i.value.shadowDom ? p.assignedSlot.parentNode : p.parentNode
    );
  }, L = (p) => {
    if (p)
      switch (d.position) {
        case It.left:
          return { left: 0, transform: "translateX(0)" };
        case It.right:
          return { left: `${p.width}px`, transform: "translateX(-100%)" };
        default:
          return { left: `${p.width / 2}px`, transform: "translateX(-50%)" };
      }
    return {};
  };
  return {
    openOnTop: m,
    menuStyle: C,
    xCorrect: c,
    setMenuPosition: q,
    getScrollableParent: v,
    shadowRender: (p, W) => {
      var se, R, ae;
      const T = document.createElement("div"), oe = (se = Le(r)) == null ? void 0 : se.getBoundingClientRect();
      T.setAttribute("id", "dp--temp-container");
      const $ = (R = a.value) != null && R.clientWidth ? a.value : document.body;
      $.append(T);
      const Y = L(oe), g = i.value.shadowDom ? Object.keys(y).filter(
        (l) => ["right-sidebar", "left-sidebar", "top-extra", "action-extra"].includes(l)
      ) : Object.keys(y), Z = xn(
        p,
        {
          ...W,
          shadow: !0,
          style: { opacity: 0, position: "absolute", ...Y }
        },
        Object.fromEntries(g.map((l) => [l, y[l]]))
      );
      an(Z, T), _.value = (ae = Z.el) == null ? void 0 : ae.getBoundingClientRect(), an(null, T), $.removeChild(T);
    }
  };
}, bt = [
  { name: "clock-icon", use: ["time", "calendar", "shared"] },
  { name: "arrow-left", use: ["month-year", "calendar", "shared", "year-mode"] },
  { name: "arrow-right", use: ["month-year", "calendar", "shared", "year-mode"] },
  { name: "arrow-up", use: ["time", "calendar", "month-year", "shared"] },
  { name: "arrow-down", use: ["time", "calendar", "month-year", "shared"] },
  { name: "calendar-icon", use: ["month-year", "time", "calendar", "shared", "year-mode"] },
  { name: "day", use: ["calendar", "shared"] },
  { name: "month-overlay-value", use: ["calendar", "month-year", "shared"] },
  { name: "year-overlay-value", use: ["calendar", "month-year", "shared", "year-mode"] },
  { name: "year-overlay", use: ["month-year", "shared"] },
  { name: "month-overlay", use: ["month-year", "shared"] },
  { name: "month-overlay-header", use: ["month-year", "shared"] },
  { name: "year-overlay-header", use: ["month-year", "shared"] },
  { name: "hours-overlay-value", use: ["calendar", "time", "shared"] },
  { name: "hours-overlay-header", use: ["calendar", "time", "shared"] },
  { name: "minutes-overlay-value", use: ["calendar", "time", "shared"] },
  { name: "minutes-overlay-header", use: ["calendar", "time", "shared"] },
  { name: "seconds-overlay-value", use: ["calendar", "time", "shared"] },
  { name: "seconds-overlay-header", use: ["calendar", "time", "shared"] },
  { name: "hours", use: ["calendar", "time", "shared"] },
  { name: "minutes", use: ["calendar", "time", "shared"] },
  { name: "month", use: ["calendar", "month-year", "shared"] },
  { name: "year", use: ["calendar", "month-year", "shared", "year-mode"] },
  { name: "action-buttons", use: ["action"] },
  { name: "action-preview", use: ["action"] },
  { name: "calendar-header", use: ["calendar", "shared"] },
  { name: "marker-tooltip", use: ["calendar", "shared"] },
  { name: "action-extra", use: ["menu"] },
  { name: "time-picker-overlay", use: ["calendar", "time", "shared"] },
  { name: "am-pm-button", use: ["calendar", "time", "shared"] },
  { name: "left-sidebar", use: ["menu"] },
  { name: "right-sidebar", use: ["menu"] },
  { name: "month-year", use: ["month-year", "shared"] },
  { name: "time-picker", use: ["menu", "shared"] },
  { name: "action-row", use: ["action"] },
  { name: "marker", use: ["calendar", "shared"] },
  { name: "quarter", use: ["shared"] },
  { name: "top-extra", use: ["shared", "month-year"] },
  { name: "tp-inline-arrow-up", use: ["shared", "time"] },
  { name: "tp-inline-arrow-down", use: ["shared", "time"] },
  { name: "menu-header", use: ["menu"] }
], io = [{ name: "trigger" }, { name: "input-icon" }, { name: "clear-icon" }, { name: "dp-input" }], co = {
  all: () => bt,
  monthYear: () => bt.filter((e) => e.use.includes("month-year")),
  input: () => io,
  timePicker: () => bt.filter((e) => e.use.includes("time")),
  action: () => bt.filter((e) => e.use.includes("action")),
  calendar: () => bt.filter((e) => e.use.includes("calendar")),
  menu: () => bt.filter((e) => e.use.includes("menu")),
  shared: () => bt.filter((e) => e.use.includes("shared")),
  yearMode: () => bt.filter((e) => e.use.includes("year-mode"))
}, tt = (e, t, r) => {
  const a = [];
  return co[t]().forEach((n) => {
    e[n.name] && a.push(n.name);
  }), r != null && r.length && r.forEach((n) => {
    n.slot && a.push(n.slot);
  }), a;
}, ta = (e) => {
  const t = Q(() => (a) => e.value ? a ? e.value.open : e.value.close : ""), r = Q(() => (a) => e.value ? a ? e.value.menuAppearTop : e.value.menuAppearBottom : "");
  return { transitionName: t, showTransition: !!e.value, menuTransition: r };
}, aa = (e, t, r) => {
  const { defaultedRange: a, defaultedTz: n } = _e(e), u = j(et(j(), n.value.timezone)), d = te([{ month: $e(u), year: ye(u) }]), y = (m) => {
    const P = {
      hours: ht(u),
      minutes: $t(u),
      seconds: 0
    };
    return a.value.enabled ? [P[m], P[m]] : P[m];
  }, i = Zt({
    hours: y("hours"),
    minutes: y("minutes"),
    seconds: y("seconds")
  });
  ot(
    a,
    (m, P) => {
      m.enabled !== P.enabled && (i.hours = y("hours"), i.minutes = y("minutes"), i.seconds = y("seconds"));
    },
    { deep: !0 }
  );
  const _ = Q({
    get: () => e.internalModelValue,
    set: (m) => {
      !e.readonly && !e.disabled && t("update:internal-model-value", m);
    }
  }), c = Q(
    () => (m) => d.value[m] ? d.value[m].month : 0
  ), C = Q(
    () => (m) => d.value[m] ? d.value[m].year : 0
  );
  return ot(
    _,
    (m, P) => {
      r && JSON.stringify(m ?? {}) !== JSON.stringify(P ?? {}) && r();
    },
    { deep: !0 }
  ), {
    calendars: d,
    time: i,
    modelValue: _,
    month: c,
    year: C,
    today: u
  };
}, fo = (e, t) => {
  const {
    defaultedMultiCalendars: r,
    defaultedMultiDates: a,
    defaultedUI: n,
    defaultedHighlight: u,
    defaultedTz: d,
    propDates: y,
    defaultedRange: i
  } = _e(t), { isDisabled: _ } = Tt(t), c = te(null), C = te(et(/* @__PURE__ */ new Date(), d.value.timezone)), m = (l) => {
    !l.current && t.hideOffsetDates || (c.value = l.value);
  }, P = () => {
    c.value = null;
  }, U = (l) => Array.isArray(e.value) && i.value.enabled && e.value[0] && c.value ? l ? Ee(c.value, e.value[0]) : Ye(c.value, e.value[0]) : !0, N = (l, D) => {
    const ue = () => e.value ? D ? e.value[0] || null : e.value[1] : null, M = e.value && Array.isArray(e.value) ? ue() : null;
    return Ae(j(l.value), M);
  }, H = (l) => {
    const D = Array.isArray(e.value) ? e.value[0] : null;
    return l ? !Ye(c.value ?? null, D) : !0;
  }, f = (l, D = !0) => (i.value.enabled || t.weekPicker) && Array.isArray(e.value) && e.value.length === 2 ? t.hideOffsetDates && !l.current ? !1 : Ae(j(l.value), e.value[D ? 0 : 1]) : i.value.enabled ? N(l, D) && H(D) || Ae(l.value, Array.isArray(e.value) ? e.value[0] : null) && U(D) : !1, I = (l, D) => {
    if (Array.isArray(e.value) && e.value[0] && e.value.length === 1) {
      const ue = Ae(l.value, c.value);
      return D ? Ee(e.value[0], l.value) && ue : Ye(e.value[0], l.value) && ue;
    }
    return !1;
  }, k = (l) => !e.value || t.hideOffsetDates && !l.current ? !1 : i.value.enabled ? t.modelAuto && Array.isArray(e.value) ? Ae(l.value, e.value[0] ? e.value[0] : C.value) : !1 : a.value.enabled && Array.isArray(e.value) ? e.value.some((D) => Ae(D, l.value)) : Ae(l.value, e.value ? e.value : C.value), z = (l) => {
    if (i.value.autoRange || t.weekPicker) {
      if (c.value) {
        if (t.hideOffsetDates && !l.current) return !1;
        const D = kt(c.value, +i.value.autoRange), ue = pt(j(c.value), t.weekStart);
        return t.weekPicker ? Ae(ue[1], j(l.value)) : Ae(D, j(l.value));
      }
      return !1;
    }
    return !1;
  }, q = (l) => {
    if (i.value.autoRange || t.weekPicker) {
      if (c.value) {
        const D = kt(c.value, +i.value.autoRange);
        if (t.hideOffsetDates && !l.current) return !1;
        const ue = pt(j(c.value), t.weekStart);
        return t.weekPicker ? Ee(l.value, ue[0]) && Ye(l.value, ue[1]) : Ee(l.value, c.value) && Ye(l.value, D);
      }
      return !1;
    }
    return !1;
  }, ee = (l) => {
    if (i.value.autoRange || t.weekPicker) {
      if (c.value) {
        if (t.hideOffsetDates && !l.current) return !1;
        const D = pt(j(c.value), t.weekStart);
        return t.weekPicker ? Ae(D[0], l.value) : Ae(c.value, l.value);
      }
      return !1;
    }
    return !1;
  }, x = (l) => Jt(e.value, c.value, l.value), S = () => t.modelAuto && Array.isArray(t.internalModelValue) ? !!t.internalModelValue[0] : !1, X = () => t.modelAuto ? Pn(t.internalModelValue) : !0, O = (l) => {
    if (t.weekPicker) return !1;
    const D = i.value.enabled ? !f(l) && !f(l, !1) : !0;
    return !_(l.value) && !k(l) && !(!l.current && t.hideOffsetDates) && D;
  }, K = (l) => i.value.enabled ? t.modelAuto ? S() && k(l) : !1 : k(l), fe = (l) => u.value ? Rl(l.value, y.value.highlight) : !1, me = (l) => {
    const D = _(l.value);
    return D && (typeof u.value == "function" ? !u.value(l.value, D) : !u.value.options.highlightDisabled);
  }, v = (l) => {
    var D;
    return typeof u.value == "function" ? u.value(l.value) : (D = u.value.weekdays) == null ? void 0 : D.includes(l.value.getDay());
  }, L = (l) => (i.value.enabled || t.weekPicker) && (!(r.value.count > 0) || l.current) && X() && !(!l.current && t.hideOffsetDates) && !k(l) ? x(l) : !1, ne = (l) => {
    if (Array.isArray(e.value) && e.value.length === 1) {
      const { before: D, after: ue } = vn(+i.value.maxRange, e.value[0]);
      return Ft(l.value, D) || Ot(l.value, ue);
    }
    return !1;
  }, p = (l) => {
    if (Array.isArray(e.value) && e.value.length === 1) {
      const { before: D, after: ue } = vn(+i.value.minRange, e.value[0]);
      return Jt([D, ue], e.value[0], l.value);
    }
    return !1;
  }, W = (l) => i.value.enabled && (i.value.maxRange || i.value.minRange) ? i.value.maxRange && i.value.minRange ? ne(l) || p(l) : i.value.maxRange ? ne(l) : p(l) : !1, T = (l) => {
    const { isRangeStart: D, isRangeEnd: ue } = g(l), M = i.value.enabled ? D || ue : !1;
    return {
      dp__cell_offset: !l.current,
      dp__pointer: !t.disabled && !(!l.current && t.hideOffsetDates) && !_(l.value) && !W(l),
      dp__cell_disabled: _(l.value) || W(l),
      dp__cell_highlight: !me(l) && (fe(l) || v(l)) && !K(l) && !M && !ee(l) && !(L(l) && t.weekPicker) && !ue,
      dp__cell_highlight_active: !me(l) && (fe(l) || v(l)) && K(l),
      dp__today: !t.noToday && Ae(l.value, C.value) && l.current,
      "dp--past": Ye(l.value, C.value),
      "dp--future": Ee(l.value, C.value)
    };
  }, oe = (l) => ({
    dp__active_date: K(l),
    dp__date_hover: O(l)
  }), $ = (l) => {
    if (e.value && !Array.isArray(e.value)) {
      const D = pt(e.value, t.weekStart);
      return {
        ...se(l),
        dp__range_start: Ae(D[0], l.value),
        dp__range_end: Ae(D[1], l.value),
        dp__range_between_week: Ee(l.value, D[0]) && Ye(l.value, D[1])
      };
    }
    return {
      ...se(l)
    };
  }, Y = (l) => {
    if (e.value && Array.isArray(e.value)) {
      const D = pt(e.value[0], t.weekStart), ue = e.value[1] ? pt(e.value[1], t.weekStart) : [];
      return {
        ...se(l),
        dp__range_start: Ae(D[0], l.value) || Ae(ue[0], l.value),
        dp__range_end: Ae(D[1], l.value) || Ae(ue[1], l.value),
        dp__range_between_week: Ee(l.value, D[0]) && Ye(l.value, D[1]) || Ee(l.value, ue[0]) && Ye(l.value, ue[1]),
        dp__range_between: Ee(l.value, D[1]) && Ye(l.value, ue[0])
      };
    }
    return {
      ...se(l)
    };
  }, g = (l) => {
    const D = r.value.count > 0 ? l.current && f(l) && X() : f(l) && X(), ue = r.value.count > 0 ? l.current && f(l, !1) && X() : f(l, !1) && X();
    return { isRangeStart: D, isRangeEnd: ue };
  }, Z = (l) => {
    const { isRangeStart: D, isRangeEnd: ue } = g(l);
    return {
      dp__range_start: D,
      dp__range_end: ue,
      dp__range_between: L(l),
      dp__date_hover: Ae(l.value, c.value) && !D && !ue && !t.weekPicker,
      dp__date_hover_start: I(l, !0),
      dp__date_hover_end: I(l, !1)
    };
  }, se = (l) => ({
    ...Z(l),
    dp__cell_auto_range: q(l),
    dp__cell_auto_range_start: ee(l),
    dp__cell_auto_range_end: z(l)
  }), R = (l) => i.value.enabled ? i.value.autoRange ? se(l) : t.modelAuto ? { ...oe(l), ...Z(l) } : t.weekPicker ? Y(l) : Z(l) : t.weekPicker ? $(l) : oe(l);
  return {
    setHoverDate: m,
    clearHoverDate: P,
    getDayClassData: (l) => t.hideOffsetDates && !l.current ? {} : {
      ...T(l),
      ...R(l),
      [t.dayClass ? t.dayClass(l.value, t.internalModelValue) : ""]: !0,
      ...n.value.calendarCell ?? {}
    }
  };
}, Tt = (e) => {
  const { defaultedFilters: t, defaultedRange: r, propDates: a, defaultedMultiDates: n } = _e(e), u = (v) => a.value.disabledDates ? typeof a.value.disabledDates == "function" ? a.value.disabledDates(j(v)) : !!ca(v, a.value.disabledDates) : !1, d = (v) => a.value.maxDate ? e.yearPicker ? ye(v) > ye(a.value.maxDate) : Ee(v, a.value.maxDate) : !1, y = (v) => a.value.minDate ? e.yearPicker ? ye(v) < ye(a.value.minDate) : Ye(v, a.value.minDate) : !1, i = (v) => {
    const L = d(v), ne = y(v), p = u(v), T = t.value.months.map((Z) => +Z).includes($e(v)), oe = e.disabledWeekDays.length ? e.disabledWeekDays.some((Z) => +Z === yl(v)) : !1, $ = P(v), Y = ye(v), g = Y < +e.yearRange[0] || Y > +e.yearRange[1];
    return !(L || ne || p || T || g || oe || $);
  }, _ = (v, L) => Ye(...wt(a.value.minDate, v, L)) || Ae(...wt(a.value.minDate, v, L)), c = (v, L) => Ee(...wt(a.value.maxDate, v, L)) || Ae(...wt(a.value.maxDate, v, L)), C = (v, L, ne) => {
    let p = !1;
    return a.value.maxDate && ne && c(v, L) && (p = !0), a.value.minDate && !ne && _(v, L) && (p = !0), p;
  }, m = (v, L, ne, p) => {
    let W = !1;
    return p && (a.value.minDate || a.value.maxDate) ? a.value.minDate && a.value.maxDate ? W = C(v, L, ne) : (a.value.minDate && _(v, L) || a.value.maxDate && c(v, L)) && (W = !0) : W = !0, W;
  }, P = (v) => Array.isArray(a.value.allowedDates) && !a.value.allowedDates.length ? !0 : a.value.allowedDates ? !ca(v, a.value.allowedDates) : !1, U = (v) => !i(v), N = (v) => r.value.noDisabledRange ? !hn({ start: v[0], end: v[1] }).some((ne) => U(ne)) : !0, H = (v) => {
    if (v) {
      const L = ye(v);
      return L >= +e.yearRange[0] && L <= e.yearRange[1];
    }
    return !0;
  }, f = (v, L) => !!(Array.isArray(v) && v[L] && (r.value.maxRange || r.value.minRange) && H(v[L])), I = (v, L, ne = 0) => {
    if (f(L, ne) && H(v)) {
      const p = gl(v, L[ne]), W = Yn(L[ne], v), T = W.length === 1 ? 0 : W.filter(($) => U($)).length, oe = Math.abs(p) - (r.value.minMaxRawRange ? 0 : T);
      if (r.value.minRange && r.value.maxRange)
        return oe >= +r.value.minRange && oe <= +r.value.maxRange;
      if (r.value.minRange) return oe >= +r.value.minRange;
      if (r.value.maxRange) return oe <= +r.value.maxRange;
    }
    return !0;
  }, k = () => !e.enableTimePicker || e.monthPicker || e.yearPicker || e.ignoreTimeValidation, z = (v) => Array.isArray(v) ? [v[0] ? Ca(v[0]) : null, v[1] ? Ca(v[1]) : null] : Ca(v), q = (v, L, ne) => v.find(
    (p) => +p.hours === ht(L) && p.minutes === "*" ? !0 : +p.minutes === $t(L) && +p.hours === ht(L)
  ) && ne, ee = (v, L, ne) => {
    const [p, W] = v, [T, oe] = L;
    return !q(p, T, ne) && !q(W, oe, ne) && ne;
  }, x = (v, L) => {
    const ne = Array.isArray(L) ? L : [L];
    return Array.isArray(e.disabledTimes) ? Array.isArray(e.disabledTimes[0]) ? ee(e.disabledTimes, ne, v) : !ne.some((p) => q(e.disabledTimes, p, v)) : v;
  }, S = (v, L) => {
    const ne = Array.isArray(L) ? [Ct(L[0]), L[1] ? Ct(L[1]) : void 0] : Ct(L), p = !e.disabledTimes(ne);
    return v && p;
  }, X = (v, L) => e.disabledTimes ? Array.isArray(e.disabledTimes) ? x(L, v) : S(L, v) : L, O = (v) => {
    let L = !0;
    if (!v || k()) return !0;
    const ne = !a.value.minDate && !a.value.maxDate ? z(v) : v;
    return (e.maxTime || a.value.maxDate) && (L = fn(
      e.maxTime,
      a.value.maxDate,
      "max",
      Ne(ne),
      L
    )), (e.minTime || a.value.minDate) && (L = fn(
      e.minTime,
      a.value.minDate,
      "min",
      Ne(ne),
      L
    )), X(v, L);
  }, K = (v) => {
    if (!e.monthPicker) return !0;
    let L = !0;
    const ne = j(dt(v));
    if (a.value.minDate && a.value.maxDate) {
      const p = j(dt(a.value.minDate)), W = j(dt(a.value.maxDate));
      return Ee(ne, p) && Ye(ne, W) || Ae(ne, p) || Ae(ne, W);
    }
    if (a.value.minDate) {
      const p = j(dt(a.value.minDate));
      L = Ee(ne, p) || Ae(ne, p);
    }
    if (a.value.maxDate) {
      const p = j(dt(a.value.maxDate));
      L = Ye(ne, p) || Ae(ne, p);
    }
    return L;
  }, fe = Q(() => (v) => !e.enableTimePicker || e.ignoreTimeValidation ? !0 : O(v)), me = Q(() => (v) => e.monthPicker ? Array.isArray(v) && (r.value.enabled || n.value.enabled) ? !v.filter((ne) => !K(ne)).length : K(v) : !0);
  return {
    isDisabled: U,
    validateDate: i,
    validateMonthYearInRange: m,
    isDateRangeAllowed: N,
    checkMinMaxRange: I,
    isValidTime: O,
    isTimeValid: fe,
    isMonthValid: me
  };
}, ya = () => {
  const e = Q(() => (a, n) => a == null ? void 0 : a.includes(n)), t = Q(() => (a, n) => a.count ? a.solo ? !0 : n === 0 : !0), r = Q(() => (a, n) => a.count ? a.solo ? !0 : n === a.count - 1 : !0);
  return { hideNavigationButtons: e, showLeftIcon: t, showRightIcon: r };
}, vo = (e, t, r) => {
  const a = te(0), n = Zt({
    [Rt.timePicker]: !e.enableTimePicker || e.timePicker || e.monthPicker,
    [Rt.calendar]: !1,
    [Rt.header]: !1
  }), u = Q(() => e.monthPicker || e.timePicker), d = (C) => {
    var m;
    if ((m = e.flow) != null && m.length) {
      if (!C && u.value) return c();
      n[C] = !0, Object.keys(n).filter((P) => !n[P]).length || c();
    }
  }, y = () => {
    var C, m;
    (C = e.flow) != null && C.length && a.value !== -1 && (a.value += 1, t("flow-step", a.value), c()), ((m = e.flow) == null ? void 0 : m.length) === a.value && nt().then(() => i());
  }, i = () => {
    a.value = -1;
  }, _ = (C, m, ...P) => {
    var U, N;
    e.flow[a.value] === C && r.value && ((N = (U = r.value)[m]) == null || N.call(U, ...P));
  }, c = (C = 0) => {
    C && (a.value += C), _(Ge.month, "toggleMonthPicker", !0), _(Ge.year, "toggleYearPicker", !0), _(Ge.calendar, "toggleTimePicker", !1, !0), _(Ge.time, "toggleTimePicker", !0, !0);
    const m = e.flow[a.value];
    (m === Ge.hours || m === Ge.minutes || m === Ge.seconds) && _(m, "toggleTimePicker", !0, !0, m);
  };
  return { childMount: d, updateFlowStep: y, resetFlow: i, handleFlow: c, flowStep: a };
}, mo = {
  key: 1,
  class: "dp__input_wrap"
}, po = ["id", "name", "inputmode", "placeholder", "disabled", "readonly", "required", "value", "autocomplete", "aria-label", "aria-disabled", "aria-invalid"], yo = {
  key: 2,
  class: "dp--clear-btn"
}, go = ["aria-label"], ho = /* @__PURE__ */ Ve({
  compatConfig: {
    MODE: 3
  },
  __name: "DatepickerInput",
  props: {
    isMenuOpen: { type: Boolean, default: !1 },
    inputValue: { type: String, default: "" },
    ...va
  },
  emits: [
    "clear",
    "open",
    "update:input-value",
    "set-input-date",
    "close",
    "select-date",
    "set-empty-date",
    "toggle",
    "focus-prev",
    "focus",
    "blur",
    "real-blur",
    "text-input"
  ],
  setup(e, { expose: t, emit: r }) {
    const a = r, n = e, {
      defaultedTextInput: u,
      defaultedAriaLabels: d,
      defaultedInline: y,
      defaultedConfig: i,
      defaultedRange: _,
      defaultedMultiDates: c,
      defaultedUI: C,
      getDefaultPattern: m,
      getDefaultStartTime: P
    } = _e(n), { checkMinMaxRange: U } = Tt(n), N = te(), H = te(null), f = te(!1), I = te(!1), k = Q(
      () => ({
        dp__pointer: !n.disabled && !n.readonly && !u.value.enabled,
        dp__disabled: n.disabled,
        dp__input_readonly: !u.value.enabled,
        dp__input: !0,
        dp__input_icon_pad: !n.hideInputIcon,
        dp__input_valid: typeof n.state == "boolean" ? n.state : !1,
        dp__input_invalid: typeof n.state == "boolean" ? !n.state : !1,
        dp__input_focus: f.value || n.isMenuOpen,
        dp__input_reg: !u.value.enabled,
        ...C.value.input ?? {}
      })
    ), z = () => {
      a("set-input-date", null), n.clearable && n.autoApply && (a("set-empty-date"), N.value = null);
    }, q = ($) => {
      const Y = P();
      return Bl(
        $,
        u.value.format ?? m(),
        Y ?? In({}, n.enableSeconds),
        n.inputValue,
        I.value,
        n.formatLocale
      );
    }, ee = ($) => {
      const { rangeSeparator: Y } = u.value, [g, Z] = $.split(`${Y}`);
      if (g) {
        const se = q(g.trim()), R = Z ? q(Z.trim()) : null;
        if (Ot(se, R)) return;
        const ae = se && R ? [se, R] : [se];
        U(R, ae, 0) && (N.value = se ? ae : null);
      }
    }, x = () => {
      I.value = !0;
    }, S = ($) => {
      if (_.value.enabled)
        ee($);
      else if (c.value.enabled) {
        const Y = $.split(";");
        N.value = Y.map((g) => q(g.trim())).filter((g) => g);
      } else
        N.value = q($);
    }, X = ($) => {
      var g;
      const Y = typeof $ == "string" ? $ : (g = $.target) == null ? void 0 : g.value;
      Y !== "" ? (u.value.openMenu && !n.isMenuOpen && a("open"), S(Y), a("set-input-date", N.value)) : z(), I.value = !1, a("update:input-value", Y), a("text-input", $, N.value);
    }, O = ($) => {
      u.value.enabled ? (S($.target.value), u.value.enterSubmit && za(N.value) && n.inputValue !== "" ? (a("set-input-date", N.value, !0), N.value = null) : u.value.enterSubmit && n.inputValue === "" && (N.value = null, a("clear"))) : me($);
    }, K = ($, Y) => {
      u.value.enabled && u.value.tabSubmit && !Y && S($.target.value), u.value.tabSubmit && za(N.value) && n.inputValue !== "" ? (a("set-input-date", N.value, !0, !0), N.value = null) : u.value.tabSubmit && n.inputValue === "" && (N.value = null, a("clear", !0));
    }, fe = () => {
      f.value = !0, a("focus"), nt().then(() => {
        var $;
        u.value.enabled && u.value.selectOnFocus && (($ = H.value) == null || $.select());
      });
    }, me = ($) => {
      if (Dt($, i.value, !0), u.value.enabled && u.value.openMenu && !y.value.input) {
        if (u.value.openMenu === "open" && !n.isMenuOpen) return a("open");
        if (u.value.openMenu === "toggle") return a("toggle");
      } else u.value.enabled || a("toggle");
    }, v = () => {
      a("real-blur"), f.value = !1, (!n.isMenuOpen || y.value.enabled && y.value.input) && a("blur"), n.autoApply && u.value.enabled && N.value && !n.isMenuOpen && (a("set-input-date", N.value), a("select-date"), N.value = null);
    }, L = ($) => {
      Dt($, i.value, !0), a("clear");
    }, ne = () => {
      a("close");
    }, p = ($) => {
      if ($.key === "Tab" && K($), $.key === "Enter" && O($), $.key === "Escape" && u.value.escClose && ne(), !u.value.enabled) {
        if ($.code === "Tab") return;
        $.preventDefault();
      }
    }, W = () => {
      var $;
      ($ = H.value) == null || $.focus({ preventScroll: !0 });
    }, T = ($) => {
      N.value = $;
    }, oe = ($) => {
      $.key === Oe.tab && K($, !0);
    };
    return t({
      focusInput: W,
      setParsedDate: T
    }), ($, Y) => {
      var g, Z, se;
      return A(), V("div", { onClick: me }, [
        $.$slots.trigger && !$.$slots["dp-input"] && !s(y).enabled ? de($.$slots, "trigger", { key: 0 }) : J("", !0),
        !$.$slots.trigger && (!s(y).enabled || s(y).input) ? (A(), V("div", mo, [
          $.$slots["dp-input"] && !$.$slots.trigger && (!s(y).enabled || s(y).enabled && s(y).input) ? de($.$slots, "dp-input", {
            key: 0,
            value: e.inputValue,
            isMenuOpen: e.isMenuOpen,
            onInput: X,
            onEnter: O,
            onTab: K,
            onClear: L,
            onBlur: v,
            onKeypress: p,
            onPaste: x,
            onFocus: fe,
            openMenu: () => $.$emit("open"),
            closeMenu: () => $.$emit("close"),
            toggleMenu: () => $.$emit("toggle")
          }) : J("", !0),
          $.$slots["dp-input"] ? J("", !0) : (A(), V("input", {
            key: 1,
            id: $.uid ? `dp-input-${$.uid}` : void 0,
            ref_key: "inputRef",
            ref: H,
            "data-test-id": "dp-input",
            name: $.name,
            class: De(k.value),
            inputmode: s(u).enabled ? "text" : "none",
            placeholder: $.placeholder,
            disabled: $.disabled,
            readonly: $.readonly,
            required: $.required,
            value: e.inputValue,
            autocomplete: $.autocomplete,
            "aria-label": (g = s(d)) == null ? void 0 : g.input,
            "aria-disabled": $.disabled || void 0,
            "aria-invalid": $.state === !1 ? !0 : void 0,
            onInput: X,
            onBlur: v,
            onFocus: fe,
            onKeypress: p,
            onKeydown: Y[0] || (Y[0] = (R) => p(R)),
            onPaste: x
          }, null, 42, po)),
          ge("div", {
            onClick: Y[3] || (Y[3] = (R) => a("toggle"))
          }, [
            $.$slots["input-icon"] && !$.hideInputIcon ? (A(), V("span", {
              key: 0,
              class: "dp__input_icon",
              onClick: Y[1] || (Y[1] = (R) => a("toggle"))
            }, [
              de($.$slots, "input-icon")
            ])) : J("", !0),
            !$.$slots["input-icon"] && !$.hideInputIcon && !$.$slots["dp-input"] ? (A(), Te(s(Wt), {
              key: 1,
              "aria-label": (Z = s(d)) == null ? void 0 : Z.calendarIcon,
              class: "dp__input_icon dp__input_icons",
              onClick: Y[2] || (Y[2] = (R) => a("toggle"))
            }, null, 8, ["aria-label"])) : J("", !0)
          ]),
          $.$slots["clear-icon"] && ($.alwaysClearable || e.inputValue && $.clearable && !$.disabled && !$.readonly) ? (A(), V("span", yo, [
            de($.$slots, "clear-icon", { clear: L })
          ])) : J("", !0),
          !$.$slots["clear-icon"] && ($.alwaysClearable || $.clearable && e.inputValue && !$.disabled && !$.readonly) ? (A(), V("button", {
            key: 3,
            "aria-label": (se = s(d)) == null ? void 0 : se.clearInput,
            class: "dp--clear-btn",
            type: "button",
            onKeydown: Y[4] || (Y[4] = (R) => s(Ze)(R, () => L(R), !0, oe)),
            onClick: Y[5] || (Y[5] = Qt((R) => L(R), ["prevent"]))
          }, [
            at(s(Tn), {
              class: "dp__input_icons",
              "data-test-id": "clear-icon"
            })
          ], 40, go)) : J("", !0)
        ])) : J("", !0)
      ]);
    };
  }
}), bo = typeof window < "u" ? window : void 0, Ea = () => {
}, ko = (e) => el() ? (tl(e), !0) : !1, wo = (e, t, r, a) => {
  if (!e) return Ea;
  let n = Ea;
  const u = ot(
    () => s(e),
    (y) => {
      n(), y && (y.addEventListener(t, r, a), n = () => {
        y.removeEventListener(t, r, a), n = Ea;
      });
    },
    { immediate: !0, flush: "post" }
  ), d = () => {
    u(), n();
  };
  return ko(d), d;
}, Do = (e, t, r, a = {}) => {
  const { window: n = bo, event: u = "pointerdown" } = a;
  return n ? wo(n, u, (y) => {
    const i = Le(e), _ = Le(t);
    !i || !_ || i === y.target || y.composedPath().includes(i) || y.composedPath().includes(_) || r(y);
  }, { passive: !0 }) : void 0;
}, Mo = ["data-dp-mobile"], $o = /* @__PURE__ */ Ve({
  compatConfig: {
    MODE: 3
  },
  __name: "VueDatePicker",
  props: {
    ...va
  },
  emits: [
    "update:model-value",
    "update:model-timezone-value",
    "text-submit",
    "closed",
    "cleared",
    "open",
    "focus",
    "blur",
    "internal-model-change",
    "recalculate-position",
    "flow-step",
    "update-month-year",
    "invalid-select",
    "invalid-fixed-range",
    "tooltip-open",
    "tooltip-close",
    "time-picker-open",
    "time-picker-close",
    "am-pm-change",
    "range-start",
    "range-end",
    "date-update",
    "invalid-date",
    "overlay-toggle",
    "text-input"
  ],
  setup(e, { expose: t, emit: r }) {
    const a = r, n = e, u = Bt(), d = te(!1), y = Gt(n, "modelValue"), i = Gt(n, "timezone"), _ = te(null), c = te(null), C = te(null), m = te(!1), P = te(null), U = te(!1), N = te(!1), H = te(!1), f = te(!1), { setMenuFocused: I, setShiftKey: k } = Ln(), { clearArrowNav: z } = At(), { validateDate: q, isValidTime: ee } = Tt(n), {
      defaultedTransitions: x,
      defaultedTextInput: S,
      defaultedInline: X,
      defaultedConfig: O,
      defaultedRange: K,
      defaultedMultiDates: fe
    } = _e(n), { menuTransition: me, showTransition: v } = ta(x), { isMobile: L } = Kn(O);
    je(() => {
      R(n.modelValue), nt().then(() => {
        if (!X.value.enabled) {
          const w = Y(P.value);
          w == null || w.addEventListener("scroll", E), window == null || window.addEventListener("resize", ce);
        }
      }), X.value.enabled && (d.value = !0), window == null || window.addEventListener("keyup", B), window == null || window.addEventListener("keydown", Me);
    }), xt(() => {
      if (!X.value.enabled) {
        const w = Y(P.value);
        w == null || w.removeEventListener("scroll", E), window == null || window.removeEventListener("resize", ce);
      }
      window == null || window.removeEventListener("keyup", B), window == null || window.removeEventListener("keydown", Me);
    });
    const ne = tt(u, "all", n.presetDates), p = tt(u, "input");
    ot(
      [y, i],
      () => {
        R(y.value);
      },
      { deep: !0 }
    );
    const { openOnTop: W, menuStyle: T, xCorrect: oe, setMenuPosition: $, getScrollableParent: Y, shadowRender: g } = uo({
      menuRef: _,
      menuRefInner: c,
      inputRef: C,
      pickerWrapperRef: P,
      inline: X,
      emit: a,
      props: n,
      slots: u
    }), {
      inputValue: Z,
      internalModelValue: se,
      parseExternalModelValue: R,
      emitModelValue: ae,
      formatInputValue: l,
      checkBeforeEmit: D
    } = nr(a, n, m), ue = Q(
      () => ({
        dp__main: !0,
        dp__theme_dark: n.dark,
        dp__theme_light: !n.dark,
        dp__flex_display: X.value.enabled,
        "dp--flex-display-collapsed": H.value,
        dp__flex_display_with_input: X.value.input
      })
    ), M = Q(() => n.dark ? "dp__theme_dark" : "dp__theme_light"), he = Q(() => n.teleport ? {
      to: typeof n.teleport == "boolean" ? "body" : n.teleport,
      disabled: !n.teleport || X.value.enabled
    } : {}), pe = Q(() => ({ class: "dp__outer_menu_wrap" })), re = Q(() => X.value.enabled && (n.timePicker || n.monthPicker || n.yearPicker || n.quarterPicker)), o = () => {
      var w, G;
      return ((G = (w = C.value) == null ? void 0 : w.$el) == null ? void 0 : G.getBoundingClientRect()) ?? { width: 0, left: 0, right: 0 };
    }, E = () => {
      d.value && (O.value.closeOnScroll ? Ue() : $());
    }, ce = () => {
      var G;
      d.value && $();
      const w = ((G = c.value) == null ? void 0 : G.$el.getBoundingClientRect().width) ?? 0;
      H.value = document.body.offsetWidth <= w;
    }, B = (w) => {
      w.key === "Tab" && !X.value.enabled && !n.teleport && O.value.tabOutClosesMenu && (P.value.contains(document.activeElement) || Ue()), N.value = w.shiftKey;
    }, Me = (w) => {
      N.value = w.shiftKey;
    }, be = () => {
      !n.disabled && !n.readonly && (g(gn, n), $(!1), d.value = !0, d.value && a("open"), d.value || ve(), R(n.modelValue));
    }, Se = () => {
      var w, G;
      Z.value = "", ve(), (w = c.value) == null || w.onValueCleared(), (G = C.value) == null || G.setParsedDate(null), a("update:model-value", null), a("update:model-timezone-value", null), a("cleared"), O.value.closeOnClearValue && Ue();
    }, b = () => {
      const w = se.value;
      return !w || !Array.isArray(w) && q(w) ? !0 : Array.isArray(w) ? fe.value.enabled || w.length === 2 && q(w[0]) && q(w[1]) ? !0 : K.value.partialRange && !n.timePicker ? q(w[0]) : !1 : !1;
    }, F = () => {
      D() && b() ? (ae(), Ue()) : a("invalid-select", se.value);
    }, Re = (w) => {
      Fe(), ae(), O.value.closeOnAutoApply && !w && Ue();
    }, Fe = () => {
      C.value && S.value.enabled && C.value.setParsedDate(se.value);
    }, mt = (w = !1) => {
      n.autoApply && ee(se.value) && b() && (K.value.enabled && Array.isArray(se.value) ? (K.value.partialRange || se.value.length === 2) && Re(w) : Re(w));
    }, ve = () => {
      S.value.enabled || (se.value = null);
    }, Ue = (w = !1) => {
      w && se.value && O.value.setDateOnMenuClose && F(), X.value.enabled || (d.value && (d.value = !1, oe.value = !1, I(!1), k(!1), z(), a("closed"), Z.value && R(y.value)), ve(), a("blur"));
    }, lt = (w, G, ie = !1) => {
      if (!w) {
        se.value = null;
        return;
      }
      const Xe = Array.isArray(w) ? !w.some((_t) => !q(_t)) : q(w), st = ee(w);
      Xe && st ? (f.value = !0, se.value = w, G && (U.value = ie, F(), a("text-submit")), nt().then(() => {
        f.value = !1;
      })) : a("invalid-date", w);
    }, ga = () => {
      n.autoApply && ee(se.value) && ae(), Fe();
    }, na = () => d.value ? Ue() : be(), ha = (w) => {
      se.value = w;
    }, ba = () => {
      S.value.enabled && (m.value = !0, l()), a("focus");
    }, ka = () => {
      if (S.value.enabled && (m.value = !1, R(n.modelValue), U.value)) {
        const w = Pl(P.value, N.value);
        w == null || w.focus();
      }
      a("blur");
    }, wa = (w) => {
      c.value && c.value.updateMonthYear(0, {
        month: un(w.month),
        year: un(w.year)
      });
    }, Da = (w) => {
      R(w ?? n.modelValue);
    }, Ma = (w, G) => {
      var ie;
      (ie = c.value) == null || ie.switchView(w, G);
    }, tn = (w, G) => O.value.onClickOutside ? O.value.onClickOutside(w, G) : Ue(!0), h = (w = 0) => {
      var G;
      (G = c.value) == null || G.handleFlow(w);
    }, le = () => _;
    return Do(
      _,
      C,
      (w) => tn(b, w)
    ), t({
      closeMenu: Ue,
      selectDate: F,
      clearValue: Se,
      openMenu: be,
      onScroll: E,
      formatInputValue: l,
      // exposed for testing purposes
      updateInternalModelValue: ha,
      // modify internal modelValue
      setMonthYear: wa,
      parseModel: Da,
      switchView: Ma,
      toggleMenu: na,
      handleFlow: h,
      getDpWrapMenuRef: le
    }), (w, G) => (A(), V("div", {
      ref_key: "pickerWrapperRef",
      ref: P,
      class: De(ue.value),
      "data-datepicker-instance": "",
      "data-dp-mobile": s(L)
    }, [
      at(ho, He({
        ref_key: "inputRef",
        ref: C,
        "input-value": s(Z),
        "onUpdate:inputValue": G[0] || (G[0] = (ie) => nn(Z) ? Z.value = ie : null),
        "is-menu-open": d.value
      }, w.$props, {
        onClear: Se,
        onOpen: be,
        onSetInputDate: lt,
        onSetEmptyDate: s(ae),
        onSelectDate: F,
        onToggle: na,
        onClose: Ue,
        onFocus: ba,
        onBlur: ka,
        onRealBlur: G[1] || (G[1] = (ie) => m.value = !1),
        onTextInput: G[2] || (G[2] = (ie) => w.$emit("text-input", ie))
      }), qe({ _: 2 }, [
        Be(s(p), (ie, Xe) => ({
          name: ie,
          fn: we((st) => [
            de(w.$slots, ie, ze(xe(st)))
          ])
        }))
      ]), 1040, ["input-value", "is-menu-open", "onSetEmptyDate"]),
      (A(), Te(fa(w.teleport ? al : "div"), ze(xe(he.value)), {
        default: we(() => [
          at(Ut, {
            name: s(me)(s(W)),
            css: s(v) && !s(X).enabled
          }, {
            default: we(() => [
              d.value ? (A(), V("div", He({
                key: 0,
                ref_key: "dpWrapMenuRef",
                ref: _
              }, pe.value, {
                class: { "dp--menu-wrapper": !s(X).enabled },
                style: s(X).enabled ? void 0 : s(T)
              }), [
                at(gn, He({
                  ref_key: "dpMenuRef",
                  ref: c
                }, w.$props, {
                  "internal-model-value": s(se),
                  "onUpdate:internalModelValue": G[3] || (G[3] = (ie) => nn(se) ? se.value = ie : null),
                  class: { [M.value]: !0, "dp--menu-wrapper": w.teleport },
                  "open-on-top": s(W),
                  "no-overlay-focus": re.value,
                  collapse: H.value,
                  "get-input-rect": o,
                  "is-text-input-date": f.value,
                  onClosePicker: Ue,
                  onSelectDate: F,
                  onAutoApply: mt,
                  onTimeUpdate: ga,
                  onFlowStep: G[4] || (G[4] = (ie) => w.$emit("flow-step", ie)),
                  onUpdateMonthYear: G[5] || (G[5] = (ie) => w.$emit("update-month-year", ie)),
                  onInvalidSelect: G[6] || (G[6] = (ie) => w.$emit("invalid-select", s(se))),
                  onAutoApplyInvalid: G[7] || (G[7] = (ie) => w.$emit("invalid-select", ie)),
                  onInvalidFixedRange: G[8] || (G[8] = (ie) => w.$emit("invalid-fixed-range", ie)),
                  onRecalculatePosition: s($),
                  onTooltipOpen: G[9] || (G[9] = (ie) => w.$emit("tooltip-open", ie)),
                  onTooltipClose: G[10] || (G[10] = (ie) => w.$emit("tooltip-close", ie)),
                  onTimePickerOpen: G[11] || (G[11] = (ie) => w.$emit("time-picker-open", ie)),
                  onTimePickerClose: G[12] || (G[12] = (ie) => w.$emit("time-picker-close", ie)),
                  onAmPmChange: G[13] || (G[13] = (ie) => w.$emit("am-pm-change", ie)),
                  onRangeStart: G[14] || (G[14] = (ie) => w.$emit("range-start", ie)),
                  onRangeEnd: G[15] || (G[15] = (ie) => w.$emit("range-end", ie)),
                  onDateUpdate: G[16] || (G[16] = (ie) => w.$emit("date-update", ie)),
                  onInvalidDate: G[17] || (G[17] = (ie) => w.$emit("invalid-date", ie)),
                  onOverlayToggle: G[18] || (G[18] = (ie) => w.$emit("overlay-toggle", ie)),
                  onMenuBlur: G[19] || (G[19] = (ie) => w.$emit("blur"))
                }), qe({ _: 2 }, [
                  Be(s(ne), (ie, Xe) => ({
                    name: ie,
                    fn: we((st) => [
                      de(w.$slots, ie, ze(xe({ ...st })))
                    ])
                  }))
                ]), 1040, ["internal-model-value", "class", "open-on-top", "no-overlay-focus", "collapse", "is-text-input-date", "onRecalculatePosition"])
              ], 16)) : J("", !0)
            ]),
            _: 3
          }, 8, ["name", "css"])
        ]),
        _: 3
      }, 16))
    ], 10, Mo));
  }
}), Gn = /* @__PURE__ */ (() => {
  const e = $o;
  return e.install = (t) => {
    t.component("Vue3DatePicker", e);
  }, e;
})(), Ao = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  default: Gn
}, Symbol.toStringTag, { value: "Module" }));
Object.entries(Ao).forEach(([e, t]) => {
  e !== "default" && (Gn[e] = t);
});
export {
  Gn as default
};
