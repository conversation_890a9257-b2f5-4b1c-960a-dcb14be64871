from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Response
from fastapi.responses import FileResponse
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import List
import sqlite3
import uuid
from datetime import datetime
import os
import sys
import json
import base64

# Add the parent directory to the Python path to import gen module
ROOT_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(ROOT_DIR)
from gen import LicenseEntity, FunctionModuleEntity, encrypt_licence

app = FastAPI()

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 允许所有来源
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize SQLite database
def init_db():
    try:
        print("Checking database...")
        conn = sqlite3.connect('licenses.db')
        c = conn.cursor()
        
        # 检查表是否存在
        c.execute('''
            SELECT count(name) FROM sqlite_master 
            WHERE type='table' AND name='licenses'
        ''')
        
        # 只在表不存在时创建表
        if c.fetchone()[0] == 0:
            print("Creating licenses table...")
            c.execute('''
                CREATE TABLE licenses (
                    id TEXT PRIMARY KEY,
                    name TEXT,
                    custom_code TEXT,
                    maintenance_type TEXT,
                    maintenance_expired TEXT,
                    adapter_number INTEGER,
                    function_module TEXT,
                    created_at TEXT,
                    license_content TEXT,
                    file_path TEXT
                )
            ''')
            conn.commit()
            print("Database table created successfully")
        else:
            print("Database table already exists")
            
        conn.close()
    except Exception as e:
        print(f"Error initializing database: {str(e)}")

init_db()

class FunctionModuleRequest(BaseModel):
    code: str
    privileged_expired: str
    path: str

class LicenseRequest(BaseModel):
    name: str
    custom_code: str
    maintenance_type: str
    maintenance_expired: str
    adapter_number: int
    function_module: List[FunctionModuleRequest]

@app.post("/api/generate-license")
async def generate_license(license_data: LicenseRequest):
    try:
        # Convert the request data to LicenseEntity
        license_entity = LicenseEntity(
            name=license_data.name,
            custom_code=license_data.custom_code,
            maintenance_type=license_data.maintenance_type,
            maintenance_expired=license_data.maintenance_expired,
            adapter_number=license_data.adapter_number,
            function_module=[
                FunctionModuleEntity(
                    code=module.code,
                    privileged_expired=module.privileged_expired,
                    path=module.path
                )
                for module in license_data.function_module
            ]
        )

        # Read AES key from root directory
        aes_key_path = os.path.join(ROOT_DIR, "aes.key")
        with open(aes_key_path, "r") as f:
            aes_key = f.read().strip()
            aes_key = bytes.fromhex(aes_key)

        # Generate license
        license_content = encrypt_licence(aes_key=aes_key, user_info=license_entity)
        license_content = base64.b64encode(license_content)
        # Create licenses directory if it doesn't exist
        os.makedirs(os.path.join(ROOT_DIR, "licenses"), exist_ok=True)

        # Generate license file name using customer name initials and date
        customer_name = license_data.name
        initials = ''.join(word[0].upper() for word in customer_name.split() if word)
        current_date = datetime.now().strftime('%Y%m%d')
        file_name = f"{initials}_{current_date}.txt"

        # Generate license file path
        license_id = str(uuid.uuid4())
        license_path = os.path.join(ROOT_DIR, "licenses", file_name)

        # If file already exists, append a number
        base_name = file_name[:-4]  # Remove .txt
        counter = 1
        while os.path.exists(license_path):
            file_name = f"{base_name}_{counter}.txt"
            license_path = os.path.join(ROOT_DIR, "licenses", file_name)
            counter += 1

        # Save to database
        conn = sqlite3.connect('licenses.db')
        c = conn.cursor()
        c.execute('''
            INSERT INTO licenses (
                id, name, custom_code, maintenance_type, maintenance_expired,
                adapter_number, function_module, created_at, license_content, file_path
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            license_id,
            license_data.name,
            license_data.custom_code,
            license_data.maintenance_type,
            license_data.maintenance_expired,
            license_data.adapter_number,
            str([module.model_dump() for module in license_data.function_module]),
            datetime.now().isoformat(),
            license_content.hex(),
            license_path
        ))
        conn.commit()
        conn.close()

        # Save license to file
        with open(license_path, "wb") as f:
            f.write(license_content)

        return FileResponse(
            license_path,
            media_type="application/octet-stream",
            filename=file_name
        )

    except Exception as e:
        print(f"Error generating license: {str(e)}")  # Add debug logging
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/license-history")
async def get_license_history():
    try:
        print("Fetching license history...")  # Debug log
        conn = sqlite3.connect('licenses.db')
        c = conn.cursor()
        
        # 检查表是否存在
        c.execute('''
            SELECT count(name) FROM sqlite_master 
            WHERE type='table' AND name='licenses'
        ''')
        if c.fetchone()[0] == 0:
            print("Licenses table does not exist")  # Debug log
            return []
            
        print("Executing select query...")  # Debug log
        c.execute('''
            SELECT id, name, custom_code, maintenance_type, maintenance_expired,
                   adapter_number, function_module, created_at, file_path
            FROM licenses
            ORDER BY created_at DESC
        ''')
        records = c.fetchall()
        print(f"Found {len(records)} records")  # Debug log
        
        result = []
        for record in records:
            try:
                function_module = eval(record[6]) if record[6] else []
                result.append({
                    "id": record[0],
                    "name": record[1],
                    "custom_code": record[2],
                    "maintenance_type": record[3],
                    "maintenance_expired": record[4],
                    "adapter_number": record[5],
                    "function_module": function_module,
                    "created_at": record[7],
                    "file_path": record[8]
                })
            except Exception as e:
                print(f"Error processing record {record[0]}: {str(e)}")  # Debug log
                continue
                
        conn.close()
        print("Database connection closed")  # Debug log
        return result
        
    except sqlite3.Error as e:
        print(f"Database error in get_license_history: {str(e)}")  # Debug log
        raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")
    except Exception as e:
        print(f"Unexpected error in get_license_history: {str(e)}")  # Debug log
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/download-license/{license_id}")
async def download_license(license_id: str):
    try:
        # 获取License文件路径和客户名称
        conn = sqlite3.connect('licenses.db')
        cursor = conn.cursor()
        cursor.execute("SELECT file_path, name FROM licenses WHERE id = ?", (license_id,))
        result = cursor.fetchone()
        conn.close()

        if not result:
            raise HTTPException(status_code=404, detail="License not found")

        file_path, customer_name = result
        if not os.path.exists(file_path):
            raise HTTPException(status_code=404, detail="License file not found")

        # 使用原始文件名（从文件路径中获取）
        original_filename = os.path.basename(file_path)

        return FileResponse(
            file_path,
            media_type="application/octet-stream",
            filename=original_filename
        )
    except sqlite3.Error as e:
        raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")

@app.delete("/api/delete-license/{license_id}")
async def delete_license(license_id: str):
    print(f"Attempting to delete license with ID: {license_id}")  # Debug log
    
    if not license_id:
        raise HTTPException(status_code=400, detail="License ID is required")
    
    try:
        # 连接数据库
        conn = sqlite3.connect('licenses.db')
        cursor = conn.cursor()

        # 检查记录是否存在
        cursor.execute("SELECT COUNT(*) FROM licenses WHERE id = ?", (license_id,))
        count = cursor.fetchone()[0]
        
        if count == 0:
            conn.close()
            print(f"License not found with ID: {license_id}")  # Debug log
            raise HTTPException(status_code=404, detail="License not found")

        # 获取License文件路径
        cursor.execute("SELECT file_path FROM licenses WHERE id = ?", (license_id,))
        result = cursor.fetchone()
        file_path = result[0] if result else None
        
        print(f"Found license file path: {file_path}")  # Debug log

        # 删除数据库记录
        print("Deleting database record")  # Debug log
        cursor.execute("DELETE FROM licenses WHERE id = ?", (license_id,))
        deleted_count = cursor.rowcount
        conn.commit()
        conn.close()
        
        if deleted_count == 0:
            print(f"No records deleted for ID: {license_id}")  # Debug log
            raise HTTPException(status_code=404, detail="License not found")

        print("Database record deleted successfully")  # Debug log

        # 删除License文件
        if file_path and os.path.exists(file_path):
            try:
                print(f"Deleting file: {file_path}")  # Debug log
                os.remove(file_path)
                print("File deleted successfully")  # Debug log
            except Exception as e:
                print(f"Error deleting file: {str(e)}")  # Debug log
                # 文件删除失败不影响整体操作
                return {
                    "message": "License record deleted successfully, but file deletion failed",
                    "file_error": str(e)
                }
        else:
            print(f"File does not exist: {file_path}")  # Debug log

        return {"message": "License deleted successfully"}

    except sqlite3.Error as e:
        print(f"Database error: {str(e)}")  # Debug log
        raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")
    except HTTPException as he:
        raise he
    except Exception as e:
        print(f"Error in delete_license: {str(e)}")  # Debug log
        raise HTTPException(status_code=500, detail=str(e))

@app.delete("/api/clear-all-licenses")
async def clear_all_licenses():
    try:
        print("Starting to clear all licenses...")
        conn = sqlite3.connect('licenses.db')
        cursor = conn.cursor()

        # 获取所有记录的文件路径
        cursor.execute("SELECT id, file_path FROM licenses")
        records = cursor.fetchall()
        print(f"Found {len(records)} records to delete")

        # 删除所有文件
        deleted_files = 0
        for record in records:
            file_path = record[1]
            if file_path and os.path.exists(file_path):
                try:
                    os.remove(file_path)
                    deleted_files += 1
                    print(f"Deleted file: {file_path}")
                except Exception as e:
                    print(f"Error deleting file {file_path}: {str(e)}")

        # 删除所有数据库记录
        cursor.execute("DELETE FROM licenses")
        deleted_records = cursor.rowcount
        conn.commit()
        conn.close()

        return {
            "message": "All licenses cleared",
            "deleted_records": deleted_records,
            "deleted_files": deleted_files
        }

    except sqlite3.Error as e:
        print(f"Database error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")
    except Exception as e:
        print(f"Error clearing licenses: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
