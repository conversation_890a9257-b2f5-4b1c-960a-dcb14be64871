import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

// 获取后端IP地址
const backendIpAddress = process.env.BACKEND_IP

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  server: {
    proxy: {
      '/api': {
        target: `http://${backendIpAddress}:8001`,
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api/, '')
      }
    }
  }
})
