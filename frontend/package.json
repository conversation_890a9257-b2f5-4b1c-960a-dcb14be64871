{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^5.16.14", "@mui/material": "^5.16.14", "@mui/system": "^5.14.20", "@mui/x-date-pickers": "^6.20.2", "axios": "^1.3.5", "dayjs": "^1.11.13", "react": "^18.2.0", "react-dom": "^18.2.0", "serve": "^14.2.4"}, "devDependencies": {"@eslint/js": "^8.56.0", "@types/node": "^22.13.10", "@types/react": "^18.0.28", "@types/react-dom": "^18.0.11", "@vitejs/plugin-react": "^3.1.0", "eslint": "^8.56.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "globals": "^13.24.0", "typescript": "^4.9.5", "typescript-eslint": "^7.0.1", "vite": "^4.2.1"}}