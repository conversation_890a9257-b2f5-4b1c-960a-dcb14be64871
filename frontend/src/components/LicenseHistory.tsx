import { useState, useEffect } from 'react';
import {
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
  Button,
  Chip,
  Box,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Snackbar,
  Alert,
  IconButton,
  TextField,
  InputAdornment,
} from '@mui/material';
import { Download as DownloadIcon, Delete as DeleteIcon, Search as SearchIcon } from '@mui/icons-material';

const BACKEND_IP = process.env.BACKEND_IP || "***************";
const API_BASE_URL = `http://${BACKEND_IP}:8000`;

interface FunctionModule {
  code: string;
  privileged_expired: string;
  path: string;
}

interface LicenseRecord {
  id: string;
  name: string;
  custom_code: string;
  maintenance_type: string;
  maintenance_expired: string;
  adapter_number: number;
  created_at: string;
  function_module: FunctionModule[];
}

export default function LicenseHistory() {
  const [records, setRecords] = useState<LicenseRecord[]>([]);
  const [filteredRecords, setFilteredRecords] = useState<LicenseRecord[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [selectedRecordId, setSelectedRecordId] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  useEffect(() => {
    fetchLicenseHistory();
  }, []);

  useEffect(() => {
    // Filter records when search query changes
    const filtered = records.filter(record =>
      record.name.toLowerCase().includes(searchQuery.toLowerCase())
    );
    setFilteredRecords(filtered);
  }, [searchQuery, records]);

  const fetchLicenseHistory = async () => {
    try {
      setError(null);
      const response = await fetch(`${API_BASE_URL}/api/license-history`);
      if (response.ok) {
        const data = await response.json();
        setRecords(data);
      } else {
        const errorText = await response.text();
        console.error('Error response:', errorText);
        setError('获取历史记录失败');
      }
    } catch (error) {
      console.error('Error fetching license history:', error);
      setError('获取历史记录失败');
    }
  };

  const handleDownload = async (id: string) => {
    try {
      const response = await fetch(`${API_BASE_URL}/api/download-license/${id}`);
      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `license_${id}.txt`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      }
    } catch (error) {
      console.error('Error downloading license:', error);
    }
  };

  const handleDeleteClick = (id: string) => {
    setSelectedRecordId(id);
    setDeleteDialogOpen(true);
  };

  const handleDeleteConfirm = async () => {
    if (!selectedRecordId) return;

    try {
      console.log('Deleting license with ID:', selectedRecordId); // Debug log
      const response = await fetch(`${API_BASE_URL}/api/delete-license/${selectedRecordId}`, {
        method: 'DELETE',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
        },
      });

      console.log('Delete response status:', response.status); // Debug log
      const responseText = await response.text();
      console.log('Delete response text:', responseText); // Debug log

      if (response.ok) {
        let result;
        try {
          result = JSON.parse(responseText);
        } catch (e) {
          console.error('Error parsing response:', e);
          result = { message: responseText };
        }
        console.log('Delete response parsed:', result); // Debug log
        setRecords(records.filter(record => record.id !== selectedRecordId));
        setSuccessMessage('License记录已删除');
      } else {
        console.error('Error response:', responseText);
        setError(`删除License失败: ${responseText}`);
      }
    } catch (error) {
      console.error('Error deleting license:', error);
      setError('删除License失败，请重试');
    } finally {
      setDeleteDialogOpen(false);
      setSelectedRecordId(null);
    }
  };

  const handleDeleteCancel = () => {
    setDeleteDialogOpen(false);
    setSelectedRecordId(null);
  };

  return (
    <Paper 
      sx={{ 
        width: '100%',
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        borderRadius: '12px',
        background: 'rgba(255, 255, 255, 0.1)',
        backdropFilter: 'blur(10px)',
        border: '1px solid rgba(255, 255, 255, 0.1)',
        boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
        overflow: 'hidden',
        position: 'relative',
        '&::before': {
          content: '""',
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          height: '2px',
          background: 'linear-gradient(90deg, #64b5f6, #1976d2)',
        },
      }}
    >
      <Box sx={{ 
        p: 2,
        borderBottom: '1px solid rgba(255, 255, 255, 0.1)',
      }}>
        <TextField
          placeholder="搜索客户名称..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          variant="outlined"
          size="small"
          fullWidth
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon sx={{ color: 'rgba(255, 255, 255, 0.7)' }} />
              </InputAdornment>
            ),
          }}
          sx={{
            maxWidth: '300px',
            '& .MuiOutlinedInput-root': {
              backgroundColor: 'rgba(255, 255, 255, 0.05)',
              backdropFilter: 'blur(10px)',
              borderRadius: '8px',
              '& fieldset': {
                borderColor: 'rgba(255, 255, 255, 0.2)',
              },
              '&:hover fieldset': {
                borderColor: 'rgba(255, 255, 255, 0.3)',
              },
              '&.Mui-focused fieldset': {
                borderColor: '#64b5f6',
              },
            },
            '& .MuiInputBase-input': {
              color: '#fff',
              '&::placeholder': {
                color: 'rgba(255, 255, 255, 0.5)',
                opacity: 1,
              },
            },
          }}
        />
      </Box>

      <Box sx={{ 
        flex: 1,
        overflow: 'auto',
        width: '100%',
      }}>
        <TableContainer sx={{ 
          maxHeight: 'calc(100vh - 200px)',
          width: '100%',
          '&::-webkit-scrollbar': {
            width: '8px',
            height: '8px',
          },
          '&::-webkit-scrollbar-track': {
            background: 'rgba(255, 255, 255, 0.05)',
          },
          '&::-webkit-scrollbar-thumb': {
            background: 'rgba(255, 255, 255, 0.2)',
            borderRadius: '4px',
            '&:hover': {
              background: 'rgba(255, 255, 255, 0.3)',
            },
          },
        }}>
          <Table stickyHeader size="small" sx={{ tableLayout: 'fixed' }}>
            <TableHead>
              <TableRow>
                <TableCell sx={{ 
                  fontWeight: 'bold', 
                  backgroundColor: 'rgba(25, 118, 210, 0.9)',
                  color: '#fff',
                  width: '15%',
                  borderBottom: '1px solid rgba(255, 255, 255, 0.1)',
                }}>
                  客户名称
                </TableCell>
                <TableCell sx={{ 
                  fontWeight: 'bold', 
                  backgroundColor: 'rgba(25, 118, 210, 0.9)',
                  color: '#fff',
                  width: '15%',
                  borderBottom: '1px solid rgba(255, 255, 255, 0.1)',
                }}>
                  机器码
                </TableCell>
                <TableCell sx={{ 
                  fontWeight: 'bold', 
                  backgroundColor: 'rgba(25, 118, 210, 0.9)',
                  color: '#fff',
                  width: '10%',
                  borderBottom: '1px solid rgba(255, 255, 255, 0.1)',
                }}>
                  License类型
                </TableCell>
                <TableCell sx={{ 
                  fontWeight: 'bold', 
                  backgroundColor: 'rgba(25, 118, 210, 0.9)',
                  color: '#fff',
                  width: '12%',
                  borderBottom: '1px solid rgba(255, 255, 255, 0.1)',
                }}>
                  维保过期时间
                </TableCell>
                <TableCell sx={{ 
                  fontWeight: 'bold', 
                  backgroundColor: 'rgba(25, 118, 210, 0.9)',
                  color: '#fff',
                  width: '8%',
                  borderBottom: '1px solid rgba(255, 255, 255, 0.1)',
                }}>
                  适配器数量
                </TableCell>
                <TableCell sx={{ 
                  fontWeight: 'bold', 
                  backgroundColor: 'rgba(25, 118, 210, 0.9)',
                  color: '#fff',
                  width: '20%',
                  borderBottom: '1px solid rgba(255, 255, 255, 0.1)',
                }}>
                  功能模块及授权时间
                </TableCell>
                <TableCell sx={{ 
                  fontWeight: 'bold', 
                  backgroundColor: 'rgba(25, 118, 210, 0.9)',
                  color: '#fff',
                  width: '12%',
                  borderBottom: '1px solid rgba(255, 255, 255, 0.1)',
                }}>
                  生成时间
                </TableCell>
                <TableCell sx={{ 
                  fontWeight: 'bold', 
                  backgroundColor: 'rgba(25, 118, 210, 0.9)',
                  color: '#fff',
                  width: '8%',
                  borderBottom: '1px solid rgba(255, 255, 255, 0.1)',
                }}>
                  操作
                </TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {filteredRecords.map((record, index) => (
                <TableRow 
                  key={index}
                  sx={{
                    backgroundColor: 'rgba(255, 255, 255, 0.05)',
                    '&:hover': {
                      backgroundColor: 'rgba(255, 255, 255, 0.1)',
                    },
                    '& .MuiTableCell-root': {
                      color: '#fff',
                      borderBottom: '1px solid rgba(255, 255, 255, 0.1)',
                    },
                  }}
                >
                  <TableCell sx={{ overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap' }}>
                    {record.name}
                  </TableCell>
                  <TableCell sx={{ overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap' }}>
                    {record.custom_code}
                  </TableCell>
                  <TableCell>
                    <Chip
                      label={record.maintenance_type}
                      color={record.maintenance_type === '正式版' ? 'success' : 'warning'}
                      size="small"
                      sx={{ minWidth: '80px' }}
                    />
                  </TableCell>
                  <TableCell>{record.maintenance_expired}</TableCell>
                  <TableCell>{record.adapter_number}</TableCell>
                  <TableCell>
                    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                      {record.function_module.map((module) => (
                        <Box key={module.code} sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <Chip
                            label={module.code}
                            size="small"
                            variant="outlined"
                            sx={{ minWidth: '100px' }}
                          />
                          <Typography variant="body2" color="text.secondary" noWrap>
                            {module.privileged_expired}
                          </Typography>
                        </Box>
                      ))}
                    </Box>
                  </TableCell>
                  <TableCell>{new Date(record.created_at).toLocaleString()}</TableCell>
                  <TableCell>
                    <Box sx={{ display: 'flex', gap: 1 }}>
                      <Button
                        startIcon={<DownloadIcon />}
                        onClick={() => handleDownload(record.id)}
                        size="small"
                        variant="outlined"
                        sx={{ 
                          borderRadius: '8px',
                          textTransform: 'none',
                          minWidth: 0,
                          padding: '4px 8px',
                        }}
                      >
                        下载
                      </Button>
                      <IconButton
                        onClick={() => handleDeleteClick(record.id)}
                        size="small"
                        color="error"
                        sx={{ 
                          borderRadius: '8px',
                        }}
                      >
                        <DeleteIcon />
                      </IconButton>
                    </Box>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
        
        {filteredRecords.length === 0 && (
          <Box 
            sx={{ 
              height: '100%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              p: 3,
            }}
          >
            <Typography color="text.secondary" variant="h6">
              {searchQuery ? '未找到匹配的记录' : '暂无License生成记录'}
            </Typography>
          </Box>
        )}
      </Box>

      {/* 删除确认对话框 */}
      <Dialog
        open={deleteDialogOpen}
        onClose={handleDeleteCancel}
        aria-labelledby="delete-dialog-title"
      >
        <DialogTitle id="delete-dialog-title">
          确认删除
        </DialogTitle>
        <DialogContent>
          <Typography>
            确定要删除这条License记录吗？此操作不可恢复。
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleDeleteCancel} color="primary">
            取消
          </Button>
          <Button onClick={handleDeleteConfirm} color="error" variant="contained">
            删除
          </Button>
        </DialogActions>
      </Dialog>

      {/* 错误提示 */}
      <Snackbar
        open={error !== null}
        autoHideDuration={6000}
        onClose={() => setError(null)}
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
      >
        <Alert onClose={() => setError(null)} severity="error">
          {error}
        </Alert>
      </Snackbar>

      {/* 成功提示 */}
      <Snackbar
        open={successMessage !== null}
        autoHideDuration={3000}
        onClose={() => setSuccessMessage(null)}
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
      >
        <Alert onClose={() => setSuccessMessage(null)} severity="success">
          {successMessage}
        </Alert>
      </Snackbar>
    </Paper>
  );
} 