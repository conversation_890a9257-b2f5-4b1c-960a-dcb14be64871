import { useState, useEffect } from 'react';
import { TextField, Button, Box, Paper, Grid, MenuItem, Typography, Snackbar, Alert, CircularProgress, Card, CardContent, Checkbox, FormControlLabel } from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import dayjs from 'dayjs';
import menuData from '../../../menu.json';

interface FunctionModule {
  code: string;
  privileged_expired: string;
  path: string;
}

interface LicenseFormData {
  name: string;
  custom_code: string;
  maintenance_type: string;
  maintenance_expired: string;
  adapter_number: number;
  function_module: FunctionModule[];
}

interface MenuItem {
  name: string;
  path: string;
  children?: MenuItem[];
}

// 从menu.json解析功能模块（只解析一级菜单）
const parseModulesFromMenu = (menuItems: MenuItem[]): { code: string; path: string }[] => {
  return menuItems
    .filter(item => !item.path.includes('/:')) // 排除带参数的路径
    .map(item => ({
      code: item.name,
      path: item.path,
    }));
};

const defaultModules = parseModulesFromMenu(menuData);

const BACKEND_IP = process.env.BACKEND_IP || "***************";
const API_BASE_URL = `http://${BACKEND_IP}:8000`;

// 获取默认过期时间（根据类型设置不同的期限）
const getDefaultExpireDate = (isTestVersion = true) => {
  return dayjs().add(isTestVersion ? 3 : 12, 'month').format('YYYY-MM-DD');
};

export default function LicenseForm() {
  // 创建默认选中的模块状态
  const defaultSelectedModules = defaultModules.reduce((acc, module) => {
    acc[module.code] = {
      selected: true,
      expireDate: getDefaultExpireDate(true)
    };
    return acc;
  }, {} as { [key: string]: { selected: boolean; expireDate: string } });

  const [formData, setFormData] = useState<LicenseFormData>({
    name: '',
    custom_code: '',
    maintenance_type: '测试版',
    maintenance_expired: getDefaultExpireDate(true),
    adapter_number: 10,
    function_module: [],
  });

  const [selectedModules, setSelectedModules] = useState(defaultSelectedModules);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleInputChange = (field: keyof LicenseFormData) => (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = event.target.value;
    setFormData(prev => {
      const newData = { ...prev, [field]: value };
      
      // 当 License 类型改变时，自动调整过期时间
      if (field === 'maintenance_type') {
        const isTestVersion = value === '测试版';
        const newExpireDate = getDefaultExpireDate(isTestVersion);
        newData.maintenance_expired = newExpireDate;
        
        // 同时更新所有已选模块的过期时间
        const updatedModules = { ...selectedModules };
        Object.keys(updatedModules).forEach(code => {
          if (updatedModules[code].selected) {
            updatedModules[code].expireDate = newExpireDate;
          }
        });
        setSelectedModules(updatedModules);
      }
      
      return newData;
    });
  };

  const handleDateChange = (date: dayjs.Dayjs | null) => {
    if (date) {
      const newDate = date.format('YYYY-MM-DD');
      setFormData({
        ...formData,
        maintenance_expired: newDate,
      });
      
      // 更新所有选中模块的过期时间
      const updatedModules = { ...selectedModules };
      Object.keys(updatedModules).forEach(code => {
        if (updatedModules[code].selected) {
          updatedModules[code].expireDate = newDate;
        }
      });
      setSelectedModules(updatedModules);
    }
  };

  const handleModuleChange = (moduleCode: string) => {
    const currentModuleState = selectedModules[moduleCode];
    const isCurrentlySelected = currentModuleState?.selected || false;
    const isTestVersion = formData.maintenance_type === '测试版';
    
    setSelectedModules(prev => ({
      ...prev,
      [moduleCode]: {
        selected: !isCurrentlySelected,
        expireDate: !isCurrentlySelected ? getDefaultExpireDate(isTestVersion) : prev[moduleCode]?.expireDate || getDefaultExpireDate(isTestVersion),
      }
    }));
  };

  const handleModuleDateChange = (moduleCode: string, date: dayjs.Dayjs | null) => {
    if (date) {
      setSelectedModules(prev => ({
        ...prev,
        [moduleCode]: {
          ...prev[moduleCode],
          expireDate: date.format('YYYY-MM-DD'),
        }
      }));
    }
  };

  const handleSelectAllModules = (event: React.ChangeEvent<HTMLInputElement>) => {
    const checked = event.target.checked;
    const isTestVersion = formData.maintenance_type === '测试版';
    const newSelectedModules = { ...selectedModules };
    
    defaultModules.forEach(module => {
      newSelectedModules[module.code] = {
        selected: checked,
        expireDate: checked ? getDefaultExpireDate(isTestVersion) : '',
      };
    });
    
    setSelectedModules(newSelectedModules);
  };

  const isAllModulesSelected = () => {
    return defaultModules.every(module => selectedModules[module.code]?.selected);
  };

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();
    setLoading(true);
    setError(null);
    
    try {
      // Convert selected modules to function_module array
      const functionModules = Object.entries(selectedModules)
        .filter(([_, value]) => value.selected)
        .map(([code, value]) => ({
          code,
          privileged_expired: value.expireDate,
          path: defaultModules.find(m => m.code === code)?.path || '',
        }));

      const submitData = {
        ...formData,
        function_module: functionModules,
      };

      const response = await fetch(`${API_BASE_URL}/api/generate-license`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(submitData),
      });

      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'license.txt';
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      } else {
        const errorData = await response.text();
        console.error('Failed to generate license:', errorData);
        setError('生成License失败，请重试');
      }
    } catch (error) {
      console.error('Error generating license:', error);
      setError('生成License失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Paper 
      sx={{ 
        width: '100%',
        height: '100%',
        borderRadius: '12px',
        background: 'rgba(255, 255, 255, 0.1)',
        backdropFilter: 'blur(10px)',
        border: '1px solid rgba(255, 255, 255, 0.1)',
        boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
        display: 'flex',
        flexDirection: 'column',
        position: 'relative',
        overflow: 'hidden',
        '&::before': {
          content: '""',
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          height: '2px',
          background: 'linear-gradient(90deg, #64b5f6, #1976d2)',
        },
      }}
    >
      <Box 
        component="form" 
        onSubmit={handleSubmit} 
        sx={{ 
          height: '100%',
          display: 'flex',
          flexDirection: 'column',
          gap: 2,
          p: 3,
        }}
      >
        <Box sx={{ display: 'flex', justifyContent: 'flex-end' }}>
          <Button
            type="submit"
            variant="contained"
            size="large"
            disabled={loading}
            sx={{
              minWidth: '160px',
              height: '44px',
              borderRadius: '22px',
              textTransform: 'none',
              fontSize: '1rem',
              background: 'linear-gradient(90deg, #1976d2, #64b5f6)',
              boxShadow: '0 4px 12px rgba(25, 118, 210, 0.3)',
              '&:hover': {
                background: 'linear-gradient(90deg, #1565c0, #42a5f5)',
                boxShadow: '0 6px 16px rgba(25, 118, 210, 0.4)',
              },
            }}
          >
            {loading ? <CircularProgress size={24} /> : '生成License'}
          </Button>
        </Box>

        <Grid container spacing={2}>
          <Grid item xs={12} md={6}>
            <TextField
              required
              fullWidth
              label="客户名称"
              value={formData.name}
              onChange={handleInputChange('name')}
              variant="outlined"
              size="small"
              sx={{
                '& .MuiOutlinedInput-root': {
                  backgroundColor: 'rgba(255, 255, 255, 0.05)',
                  backdropFilter: 'blur(10px)',
                  borderRadius: '8px',
                  '& fieldset': {
                    borderColor: 'rgba(255, 255, 255, 0.2)',
                  },
                  '&:hover fieldset': {
                    borderColor: 'rgba(255, 255, 255, 0.3)',
                  },
                  '&.Mui-focused fieldset': {
                    borderColor: '#64b5f6',
                  },
                },
                '& .MuiInputLabel-root': {
                  color: 'rgba(255, 255, 255, 0.7)',
                },
                '& .MuiOutlinedInput-input': {
                  color: '#fff',
                },
              }}
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <TextField
              required
              fullWidth
              label="机器码"
              value={formData.custom_code}
              onChange={handleInputChange('custom_code')}
              variant="outlined"
              size="small"
              sx={{
                '& .MuiOutlinedInput-root': {
                  backgroundColor: 'rgba(255, 255, 255, 0.05)',
                  backdropFilter: 'blur(10px)',
                  borderRadius: '8px',
                  '& fieldset': {
                    borderColor: 'rgba(255, 255, 255, 0.2)',
                  },
                  '&:hover fieldset': {
                    borderColor: 'rgba(255, 255, 255, 0.3)',
                  },
                  '&.Mui-focused fieldset': {
                    borderColor: '#64b5f6',
                  },
                },
                '& .MuiInputLabel-root': {
                  color: 'rgba(255, 255, 255, 0.7)',
                },
                '& .MuiOutlinedInput-input': {
                  color: '#fff',
                },
              }}
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <TextField
              select
              required
              fullWidth
              label="License类型"
              value={formData.maintenance_type}
              onChange={handleInputChange('maintenance_type')}
              variant="outlined"
              size="small"
              sx={{
                '& .MuiOutlinedInput-root': {
                  backgroundColor: 'rgba(255, 255, 255, 0.05)',
                  backdropFilter: 'blur(10px)',
                  borderRadius: '8px',
                  '& fieldset': {
                    borderColor: 'rgba(255, 255, 255, 0.2)',
                  },
                  '&:hover fieldset': {
                    borderColor: 'rgba(255, 255, 255, 0.3)',
                  },
                  '&.Mui-focused fieldset': {
                    borderColor: '#64b5f6',
                  },
                },
                '& .MuiInputLabel-root': {
                  color: 'rgba(255, 255, 255, 0.7)',
                },
                '& .MuiSelect-select': {
                  color: '#fff',
                },
                '& .MuiMenuItem-root': {
                  color: '#fff',
                },
              }}
            >
              <MenuItem value="测试版">测试版</MenuItem>
              <MenuItem value="正式版">正式版</MenuItem>
            </TextField>
          </Grid>
          <Grid item xs={12} md={6}>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
              <LocalizationProvider dateAdapter={AdapterDayjs}>
                <DatePicker
                  label="维保过期时间"
                  value={dayjs(formData.maintenance_expired)}
                  onChange={handleDateChange}
                  sx={{ width: '100%' }}
                  slotProps={{
                    textField: {
                      variant: 'outlined',
                      size: 'small',
                      sx: {
                        '& .MuiOutlinedInput-root': {
                          backgroundColor: 'rgba(255, 255, 255, 0.05)',
                          backdropFilter: 'blur(10px)',
                          borderRadius: '8px',
                          '& fieldset': {
                            borderColor: 'rgba(255, 255, 255, 0.2)',
                          },
                          '&:hover fieldset': {
                            borderColor: 'rgba(255, 255, 255, 0.3)',
                          },
                          '&.Mui-focused fieldset': {
                            borderColor: '#64b5f6',
                          },
                        },
                        '& .MuiInputLabel-root': {
                          color: 'rgba(255, 255, 255, 0.7)',
                        },
                        '& .MuiOutlinedInput-input': {
                          color: '#fff',
                        },
                        '& .MuiSvgIcon-root': {
                          color: 'rgba(255, 255, 255, 0.7)',
                        },
                      },
                    },
                  }}
                />
              </LocalizationProvider>
              <Box sx={{ display: 'flex', gap: 1 }}>
                <Button
                  size="small"
                  variant={dayjs(formData.maintenance_expired).isSame(dayjs().add(3, 'month'), 'day') ? "contained" : "outlined"}
                  onClick={() => handleDateChange(dayjs().add(3, 'month'))}
                  sx={{
                    flex: 1,
                    textTransform: 'none',
                    py: 0.5,
                    borderColor: 'rgba(255, 255, 255, 0.2)',
                    color: '#fff',
                    '&.MuiButton-outlined': {
                      borderColor: 'rgba(255, 255, 255, 0.2)',
                      '&:hover': {
                        borderColor: 'rgba(255, 255, 255, 0.3)',
                        backgroundColor: 'rgba(255, 255, 255, 0.05)',
                      },
                    },
                    '&.MuiButton-contained': {
                      background: 'linear-gradient(90deg, #1976d2, #64b5f6)',
                      boxShadow: '0 2px 8px rgba(25, 118, 210, 0.3)',
                      '&:hover': {
                        background: 'linear-gradient(90deg, #1565c0, #42a5f5)',
                      },
                    },
                  }}
                >
                  3个月
                </Button>
                <Button
                  size="small"
                  variant={dayjs(formData.maintenance_expired).isSame(dayjs().add(6, 'month'), 'day') ? "contained" : "outlined"}
                  onClick={() => handleDateChange(dayjs().add(6, 'month'))}
                  sx={{
                    flex: 1,
                    textTransform: 'none',
                    py: 0.5,
                    borderColor: 'rgba(255, 255, 255, 0.2)',
                    color: '#fff',
                    '&.MuiButton-outlined': {
                      borderColor: 'rgba(255, 255, 255, 0.2)',
                      '&:hover': {
                        borderColor: 'rgba(255, 255, 255, 0.3)',
                        backgroundColor: 'rgba(255, 255, 255, 0.05)',
                      },
                    },
                    '&.MuiButton-contained': {
                      background: 'linear-gradient(90deg, #1976d2, #64b5f6)',
                      boxShadow: '0 2px 8px rgba(25, 118, 210, 0.3)',
                      '&:hover': {
                        background: 'linear-gradient(90deg, #1565c0, #42a5f5)',
                      },
                    },
                  }}
                >
                  6个月
                </Button>
                <Button
                  size="small"
                  variant={dayjs(formData.maintenance_expired).isSame(dayjs().add(12, 'month'), 'day') ? "contained" : "outlined"}
                  onClick={() => handleDateChange(dayjs().add(12, 'month'))}
                  sx={{
                    flex: 1,
                    textTransform: 'none',
                    py: 0.5,
                    borderColor: 'rgba(255, 255, 255, 0.2)',
                    color: '#fff',
                    '&.MuiButton-outlined': {
                      borderColor: 'rgba(255, 255, 255, 0.2)',
                      '&:hover': {
                        borderColor: 'rgba(255, 255, 255, 0.3)',
                        backgroundColor: 'rgba(255, 255, 255, 0.05)',
                      },
                    },
                    '&.MuiButton-contained': {
                      background: 'linear-gradient(90deg, #1976d2, #64b5f6)',
                      boxShadow: '0 2px 8px rgba(25, 118, 210, 0.3)',
                      '&:hover': {
                        background: 'linear-gradient(90deg, #1565c0, #42a5f5)',
                      },
                    },
                  }}
                >
                  1年
                </Button>
                <Button
                  size="small"
                  variant={dayjs(formData.maintenance_expired).isSame(dayjs().add(36, 'month'), 'day') ? "contained" : "outlined"}
                  onClick={() => handleDateChange(dayjs().add(36, 'month'))}
                  sx={{
                    flex: 1,
                    textTransform: 'none',
                    py: 0.5,
                    borderColor: 'rgba(255, 255, 255, 0.2)',
                    color: '#fff',
                    '&.MuiButton-outlined': {
                      borderColor: 'rgba(255, 255, 255, 0.2)',
                      '&:hover': {
                        borderColor: 'rgba(255, 255, 255, 0.3)',
                        backgroundColor: 'rgba(255, 255, 255, 0.05)',
                      },
                    },
                    '&.MuiButton-contained': {
                      background: 'linear-gradient(90deg, #1976d2, #64b5f6)',
                      boxShadow: '0 2px 8px rgba(25, 118, 210, 0.3)',
                      '&:hover': {
                        background: 'linear-gradient(90deg, #1565c0, #42a5f5)',
                      },
                    },
                  }}
                >
                  3年
                </Button>
              </Box>
            </Box>
          </Grid>
          <Grid item xs={12} md={6}>
            <TextField
              required
              fullWidth
              type="number"
              label="授权适配器数量"
              value={formData.adapter_number}
              onChange={handleInputChange('adapter_number')}
              inputProps={{ min: 1 }}
              variant="outlined"
              size="small"
              sx={{
                '& .MuiOutlinedInput-root': {
                  backgroundColor: 'rgba(255, 255, 255, 0.05)',
                  backdropFilter: 'blur(10px)',
                  borderRadius: '8px',
                  '& fieldset': {
                    borderColor: 'rgba(255, 255, 255, 0.2)',
                  },
                  '&:hover fieldset': {
                    borderColor: 'rgba(255, 255, 255, 0.3)',
                  },
                  '&.Mui-focused fieldset': {
                    borderColor: '#64b5f6',
                  },
                },
                '& .MuiInputLabel-root': {
                  color: 'rgba(255, 255, 255, 0.7)',
                },
                '& .MuiOutlinedInput-input': {
                  color: '#fff',
                },
              }}
            />
          </Grid>
        </Grid>

        <Box sx={{ flex: 1, display: 'flex', flexDirection: 'column', minHeight: 0 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
            <Typography variant="subtitle1" sx={{ mb: 0 }}>
              功能模块
            </Typography>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <Typography variant="body2" color="textSecondary">
                批量设置授权时间：
              </Typography>
              <Box sx={{ display: 'flex', gap: 1 }}>
                <Button
                  size="small"
                  variant="outlined"
                  onClick={() => {
                    const newDate = dayjs().add(3, 'month').format('YYYY-MM-DD');
                    const updatedModules = { ...selectedModules };
                    Object.keys(updatedModules).forEach(code => {
                      if (updatedModules[code].selected) {
                        updatedModules[code].expireDate = newDate;
                      }
                    });
                    setSelectedModules(updatedModules);
                  }}
                  sx={{ textTransform: 'none', py: 0.5 }}
                >
                  3个月
                </Button>
                <Button
                  size="small"
                  variant="outlined"
                  onClick={() => {
                    const newDate = dayjs().add(6, 'month').format('YYYY-MM-DD');
                    const updatedModules = { ...selectedModules };
                    Object.keys(updatedModules).forEach(code => {
                      if (updatedModules[code].selected) {
                        updatedModules[code].expireDate = newDate;
                      }
                    });
                    setSelectedModules(updatedModules);
                  }}
                  sx={{ textTransform: 'none', py: 0.5 }}
                >
                  6个月
                </Button>
                <Button
                  size="small"
                  variant="outlined"
                  onClick={() => {
                    const newDate = dayjs().add(12, 'month').format('YYYY-MM-DD');
                    const updatedModules = { ...selectedModules };
                    Object.keys(updatedModules).forEach(code => {
                      if (updatedModules[code].selected) {
                        updatedModules[code].expireDate = newDate;
                      }
                    });
                    setSelectedModules(updatedModules);
                  }}
                  sx={{ textTransform: 'none', py: 0.5 }}
                >
                  1年
                </Button>
                <Button
                  size="small"
                  variant="outlined"
                  onClick={() => {
                    const newDate = dayjs().add(36, 'month').format('YYYY-MM-DD');
                    const updatedModules = { ...selectedModules };
                    Object.keys(updatedModules).forEach(code => {
                      if (updatedModules[code].selected) {
                        updatedModules[code].expireDate = newDate;
                      }
                    });
                    setSelectedModules(updatedModules);
                  }}
                  sx={{ textTransform: 'none', py: 0.5 }}
                >
                  3年
                </Button>
              </Box>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={isAllModulesSelected()}
                    onChange={handleSelectAllModules}
                    color="primary"
                    size="small"
                  />
                }
                label="全选"
              />
            </Box>
          </Box>

          <Box sx={{ 
            flex: 1, 
            overflow: 'auto',
            '& .MuiGrid-container': {
              margin: 0,
              width: '100%',
            }
          }}>
            <Grid container spacing={1}>
              {defaultModules.map((module) => (
                <Grid item xs={12} sm={6} md={4} lg={3} key={module.code}>
                  <Card variant="outlined" sx={{ height: '100%' }}>
                    <CardContent sx={{ p: 1, '&:last-child': { pb: 1 } }}>
                      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                        <Button
                          variant={selectedModules[module.code]?.selected ? "contained" : "outlined"}
                          onClick={() => handleModuleChange(module.code)}
                          fullWidth
                          size="small"
                          sx={{
                            textTransform: 'none',
                            height: '32px',
                            borderRadius: '4px',
                          }}
                        >
                          {module.code}
                        </Button>
                        {selectedModules[module.code]?.selected && (
                          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                            <LocalizationProvider dateAdapter={AdapterDayjs}>
                              <DatePicker
                                label="模块授权过期时间"
                                value={dayjs(selectedModules[module.code].expireDate)}
                                onChange={(date) => handleModuleDateChange(module.code, date)}
                                sx={{ width: '100%' }}
                                slotProps={{
                                  textField: {
                                    variant: 'outlined',
                                    size: 'small',
                                  },
                                }}
                              />
                            </LocalizationProvider>
                            <Box sx={{ display: 'flex', gap: 0.5 }}>
                              <Button
                                size="small"
                                variant={dayjs(selectedModules[module.code].expireDate).isSame(dayjs().add(3, 'month'), 'day') ? "contained" : "outlined"}
                                onClick={() => handleModuleDateChange(module.code, dayjs().add(3, 'month'))}
                                sx={{ flex: 1, textTransform: 'none', fontSize: '0.75rem', py: 0.5 }}
                              >
                                3月
                              </Button>
                              <Button
                                size="small"
                                variant={dayjs(selectedModules[module.code].expireDate).isSame(dayjs().add(6, 'month'), 'day') ? "contained" : "outlined"}
                                onClick={() => handleModuleDateChange(module.code, dayjs().add(6, 'month'))}
                                sx={{ flex: 1, textTransform: 'none', fontSize: '0.75rem', py: 0.5 }}
                              >
                                6月
                              </Button>
                              <Button
                                size="small"
                                variant={dayjs(selectedModules[module.code].expireDate).isSame(dayjs().add(12, 'month'), 'day') ? "contained" : "outlined"}
                                onClick={() => handleModuleDateChange(module.code, dayjs().add(12, 'month'))}
                                sx={{ flex: 1, textTransform: 'none', fontSize: '0.75rem', py: 0.5 }}
                              >
                                1年
                              </Button>
                              <Button
                                size="small"
                                variant={dayjs(selectedModules[module.code].expireDate).isSame(dayjs().add(36, 'month'), 'day') ? "contained" : "outlined"}
                                onClick={() => handleModuleDateChange(module.code, dayjs().add(36, 'month'))}
                                sx={{ flex: 1, textTransform: 'none', fontSize: '0.75rem', py: 0.5 }}
                              >
                                3年
                              </Button>
                            </Box>
                          </Box>
                        )}
                      </Box>
                    </CardContent>
                  </Card>
                </Grid>
              ))}
            </Grid>
          </Box>
        </Box>
      </Box>

      <Snackbar 
        open={error !== null} 
        autoHideDuration={6000} 
        onClose={() => setError(null)}
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
      >
        <Alert onClose={() => setError(null)} severity="error" sx={{ width: '100%' }}>
          {error}
        </Alert>
      </Snackbar>
    </Paper>
  );
} 