import React, { useState } from 'react';
import { LocalizationProvider } from '@mui/x-date-pickers';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { Box, Tabs, Tab, Container, useTheme } from '@mui/material';
import LicenseForm from './components/LicenseForm';
import LicenseHistory from './components/LicenseHistory';

function App() {
  const [currentTab, setCurrentTab] = useState(0);
  const theme = useTheme();

  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setCurrentTab(newValue);
  };

  return (
    <LocalizationProvider dateAdapter={AdapterDayjs}>
      <Box sx={{ 
        width: '100vw',
        minHeight: '100vh',
        background: 'linear-gradient(135deg, #1a237e 0%, #0d47a1 100%)',
        display: 'flex',
        flexDirection: 'column',
        position: 'relative',
        '&::before': {
          content: '""',
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: `radial-gradient(circle at 50% 50%, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 25%, transparent 50%)`,
          pointerEvents: 'none',
        },
      }}>
        <Box sx={{ 
          flex: 1,
          display: 'flex',
          flexDirection: 'column',
          p: 3,
          gap: 3,
          maxWidth: '100%',
          position: 'relative',
          zIndex: 1,
        }}>
          <Box sx={{ 
            backgroundColor: 'rgba(255, 255, 255, 0.1)',
            backdropFilter: 'blur(10px)',
            borderRadius: '12px',
            boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
            border: '1px solid rgba(255, 255, 255, 0.1)',
          }}>
            <Tabs
              value={currentTab}
              onChange={handleTabChange}
              sx={{
                px: 2,
                '& .MuiTabs-indicator': {
                  backgroundColor: '#64b5f6',
                  height: '3px',
                  borderRadius: '3px',
                },
                '& .MuiTab-root': {
                  color: 'rgba(255, 255, 255, 0.7)',
                  '&.Mui-selected': {
                    color: '#fff',
                  },
                },
              }}
            >
              <Tab 
                label="生成License" 
                sx={{
                  textTransform: 'none',
                  fontSize: '1rem',
                  fontWeight: 500,
                  minHeight: '48px',
                }}
              />
              <Tab 
                label="历史记录" 
                sx={{
                  textTransform: 'none',
                  fontSize: '1rem',
                  fontWeight: 500,
                  minHeight: '48px',
                }}
              />
            </Tabs>
          </Box>
          
          <Box sx={{ flex: 1, minHeight: 0, width: '100%' }}>
            {currentTab === 0 ? <LicenseForm /> : <LicenseHistory />}
          </Box>
        </Box>
      </Box>
    </LocalizationProvider>
  );
}

export default App;
