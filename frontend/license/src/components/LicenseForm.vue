<!-- LicenseForm.vue -->
<template>
  <div class="license-form">
    <v-card class="form-card">
      <v-form @submit.prevent="handleSubmit">
        <!-- 顶部生成按钮 -->
        <div class="button-container">
          <v-btn
            type="submit"
            color="primary"
            :loading="loading"
            size="large"
            class="generate-button"
          >
            {{ loading ? "" : "生成License" }}
            <template v-if="loading">
              <v-progress-circular indeterminate size="24" />
            </template>
          </v-btn>
        </div>

        <!-- 表单内容 -->
        <v-container>
          <v-row>
            <!-- 客户名称 -->
            <v-col cols="12" md="6">
              <v-text-field
                v-model="formData.name"
                label="客户名称"
                required
                variant="outlined"
              />
            </v-col>

            <!-- 机器码 -->
            <v-col cols="12" md="6">
              <v-text-field
                v-model="formData.custom_code"
                label="机器码"
                required
                variant="outlined"
              />
            </v-col>

            <!-- License类型 -->
            <v-col cols="12" md="6">
              <v-select
                v-model="formData.maintenance_type"
                :items="['测试版', '正式版']"
                label="License类型"
                required
                variant="outlined"
                @update:model-value="handleLicenseTypeChange"
              />
            </v-col>

            <!-- 维保过期时间 -->
            <v-col cols="12" md="6">
              <div class="date-picker-container">
                <VueDatePicker
                  v-model="formData.maintenance_expired"
                  :format="'YYYY-MM-DD'"
                  :enable-time-picker="false"
                  locale="zh"
                  auto-apply
                  @update:model-value="handleMaintenanceDateChange"
                />
                <div class="quick-date-buttons">
                  <v-btn
                    v-for="(period, index) in datePeriods"
                    :key="index"
                    size="small"
                    :variant="
                      isMaintenanceDateSelected(period.months)
                        ? 'elevated'
                        : 'outlined'
                    "
                    @click="setMaintenanceDate(period.months)"
                  >
                    {{ period.label }}
                  </v-btn>
                </div>
              </div>
            </v-col>

            <!-- 授权适配器数量 -->
            <v-col cols="12" md="6">
              <v-text-field
                v-model.number="formData.adapter_number"
                type="number"
                label="授权适配器数量"
                required
                variant="outlined"
                :min="1"
              />
            </v-col>

            <!-- 功能模块 -->
            <v-col cols="12">
              <div class="modules-header">
                <h3>选择授权功能模块</h3>
                <div class="modules-actions">
                  <span class="batch-time-label">批量设置授权时间：</span>
                  <div class="quick-date-buttons">
                    <v-btn
                      v-for="(period, index) in datePeriods"
                      :key="index"
                      size="small"
                      :variant="
                        isAllModulesDateSelected(period.months)
                          ? 'elevated'
                          : 'outlined'
                      "
                      @click="setBatchModulesDate(period.months)"
                    >
                      {{ period.label }}
                    </v-btn>
                  </div>
                  <v-checkbox
                    v-model="selectAll"
                    label="全选"
                    @update:model-value="handleSelectAllModules"
                  />
                </div>
              </div>

              <v-row>
                <v-col
                  v-for="module in defaultModules"
                  :key="module.code"
                  cols="12"
                  sm="6"
                  md="4"
                  lg="3"
                >
                  <v-card variant="outlined" class="module-card">
                    <v-card-text>
                      <div class="module-content">
                        <v-btn
                          :variant="
                            isModuleSelected(module.code)
                              ? 'elevated'
                              : 'outlined'
                          "
                          block
                          @click="handleModuleChange(module.code)"
                        >
                          {{ module.code }}
                        </v-btn>
                        <template v-if="isModuleSelected(module.code)">
                          <VueDatePicker
                            v-model="selectedModules[module.code].expireDate"
                            :format="'YYYY-MM-DD'"
                            :enable-time-picker="false"
                            locale="zh"
                            auto-apply
                            @update:model-value="(date: string) => handleModuleDateChange(module.code, date)"
                          />
                          <div class="quick-date-buttons small">
                            <v-btn
                              v-for="(period, index) in datePeriods"
                              :key="index"
                              size="x-small"
                              :variant="
                                isModuleDateSelected(module.code, period.months)
                                  ? 'elevated'
                                  : 'outlined'
                              "
                              @click="setModuleDate(module.code, period.months)"
                            >
                              {{ period.shortLabel }}
                            </v-btn>
                          </div>
                        </template>
                      </div>
                    </v-card-text>
                  </v-card>
                </v-col>
              </v-row>
            </v-col>
          </v-row>
        </v-container>
      </v-form>
    </v-card>

    <!-- 错误提示 -->
    <v-snackbar
      v-model="showError"
      color="error"
      :timeout="6000"
      location="top"
    >
      {{ error }}
      <template v-slot:actions>
        <v-btn color="white" variant="text" @click="showError = false">
          关闭
        </v-btn>
      </template>
    </v-snackbar>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from "vue";
import dayjs from "dayjs";
import VueDatePicker from "@vuepic/vue-datepicker";
import "@vuepic/vue-datepicker/dist/main.css";

// 接口定义
interface FunctionModule {
  code: string;
  privileged_expired: string;
  path: string;
}

interface LicenseFormData {
  name: string;
  custom_code: string;
  maintenance_type: string;
  maintenance_expired: string;
  adapter_number: number;
  function_module: FunctionModule[];
}

interface SelectedModule {
  selected: boolean;
  expireDate: string;
}
const BACKEND_IP = process.env.BACKEND_IP || "***************";
const API_BASE_URL = `http://${BACKEND_IP}:8000`;
const defaultModules = [
  { code: "监控中心", path: "/dashboard" },
  { code: "风险中心", path: "/risk" },
  { code: "资产台账", path: "/device" },
  { code: "互联网服务映射", path: "/exposedsurface" },
  { code: "漏洞管理", path: "/vulnerability" },
  { code: "业务系统", path: "/business" },
  { code: "数据接入", path: "/governancedata" },
  { code: "报表管理", path: "/reports" },
];

const datePeriods = [
  { months: 3, label: "3个月", shortLabel: "3月" },
  { months: 6, label: "6个月", shortLabel: "6月" },
  { months: 12, label: "1年", shortLabel: "1年" },
  { months: 36, label: "3年", shortLabel: "3年" },
];

// 获取默认过期时间
const getDefaultExpireDate = (isTestVersion = true) => {
  return dayjs()
    .add(isTestVersion ? 3 : 12, "month")
    .format("YYYY-MM-DD");
};

// 状态定义
const loading = ref(false);
const error = ref<string | null>(null);
const showError = ref(false);
const selectAll = ref(true);

// 表单数据
const formData = reactive<LicenseFormData>({
  name: "",
  custom_code: "",
  maintenance_type: "测试版",
  maintenance_expired: getDefaultExpireDate(true),
  adapter_number: 10,
  function_module: [],
});

// 创建默认选中的模块状态
const createDefaultSelectedModules = () => {
  const modules: Record<string, SelectedModule> = {};
  defaultModules.forEach((module) => {
    modules[module.code] = {
      selected: true,
      expireDate: getDefaultExpireDate(true),
    };
  });
  return modules;
};

const selectedModules = reactive(createDefaultSelectedModules());

// 计算属性
const isAllModulesSelected = computed(() => {
  return defaultModules.every(
    (module) => selectedModules[module.code]?.selected
  );
});

// 方法定义
const handleLicenseTypeChange = (value: string) => {
  const isTestVersion = value === "测试版";
  const newExpireDate = getDefaultExpireDate(isTestVersion);
  formData.maintenance_expired = newExpireDate;

  // 更新所有选中模块的过期时间
  Object.keys(selectedModules).forEach((code) => {
    if (selectedModules[code].selected) {
      selectedModules[code].expireDate = newExpireDate;
    }
  });
};

const handleMaintenanceDateChange = (date: string) => {
  formData.maintenance_expired = date;

  // 更新所有选中模块的过期时间
  Object.keys(selectedModules).forEach((code) => {
    if (selectedModules[code].selected) {
      selectedModules[code].expireDate = date;
    }
  });
};

const handleModuleChange = (moduleCode: string) => {
  const currentModule = selectedModules[moduleCode];
  const isTestVersion = formData.maintenance_type === "测试版";

  selectedModules[moduleCode] = {
    selected: !currentModule.selected,
    expireDate: !currentModule.selected
      ? getDefaultExpireDate(isTestVersion)
      : currentModule.expireDate,
  };
};

const handleModuleDateChange = (moduleCode: string, date: string) => {
  if (selectedModules[moduleCode]) {
    selectedModules[moduleCode].expireDate = date;
  }
};

const handleSelectAllModules = (checked: boolean) => {
  const isTestVersion = formData.maintenance_type === "测试版";
  defaultModules.forEach((module) => {
    selectedModules[module.code] = {
      selected: checked,
      expireDate: checked ? getDefaultExpireDate(isTestVersion) : "",
    };
  });
};

const isModuleSelected = (moduleCode: string): boolean => {
  return selectedModules[moduleCode]?.selected || false;
};

const isModuleDateSelected = (moduleCode: string, months: number): boolean => {
  if (!selectedModules[moduleCode]?.selected) return false;
  return dayjs(selectedModules[moduleCode].expireDate).isSame(
    dayjs().add(months, "month"),
    "day"
  );
};

const isMaintenanceDateSelected = (months: number): boolean => {
  return dayjs(formData.maintenance_expired).isSame(
    dayjs().add(months, "month"),
    "day"
  );
};

const isAllModulesDateSelected = (months: number): boolean => {
  return Object.entries(selectedModules).every(([_, value]) =>
    value.selected
      ? dayjs(value.expireDate).isSame(dayjs().add(months, "month"), "day")
      : true
  );
};

const setMaintenanceDate = (months: number) => {
  const newDate = dayjs().add(months, "month").format("YYYY-MM-DD");
  formData.maintenance_expired = newDate;
};

const setModuleDate = (moduleCode: string, months: number) => {
  const newDate = dayjs().add(months, "month").format("YYYY-MM-DD");
  if (selectedModules[moduleCode]) {
    selectedModules[moduleCode].expireDate = newDate;
  }
};

const setBatchModulesDate = (months: number) => {
  const newDate = dayjs().add(months, "month").format("YYYY-MM-DD");
  Object.keys(selectedModules).forEach((code) => {
    if (selectedModules[code].selected) {
      selectedModules[code].expireDate = newDate;
    }
  });
};

const handleSubmit = async () => {
  loading.value = true;
  error.value = null;
  showError.value = false;

  try {
    // 转换选中的模块为提交格式
    const functionModules = Object.entries(selectedModules)
      .filter(([_, value]) => value.selected)
      .map(([code, value]) => ({
        code,
        privileged_expired: value.expireDate,
        path: defaultModules.find((m) => m.code === code)?.path || "",
      }));

    const submitData = {
      ...formData,
      function_module: functionModules,
    };

    const response = await fetch(`${API_BASE_URL}/api/generate-license`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(submitData),
    });

    if (response.ok) {
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = "license.txt";
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } else {
      const errorData = await response.text();
      console.error("Failed to generate license:", errorData);
      error.value = "生成License失败，请重试";
      showError.value = true;
    }
  } catch (err) {
    console.error("Error generating license:", err);
    error.value = "生成License失败，请重试";
    showError.value = true;
  } finally {
    loading.value = false;
  }
};
</script>

<style scoped>
.license-form {
  width: 100%;
  height: 100%;
  padding: 24px;
}

.form-card {
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.button-container {
  display: flex;
  justify-content: flex-end;
  padding: 16px;
}

.generate-button {
  min-width: 200px;
  height: 48px;
  border-radius: 24px;
  text-transform: none;
  font-size: 1rem;
}

.date-picker-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.quick-date-buttons {
  display: flex;
  gap: 8px;
}

.quick-date-buttons.small {
  gap: 4px;
}

.modules-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.modules-actions {
  display: flex;
  align-items: center;
  gap: 16px;
}

.batch-time-label {
  color: rgba(0, 0, 0, 0.6);
  font-size: 0.875rem;
}

.module-card {
  height: 100%;
}

.module-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

:deep(.dp__input) {
  padding: 8px 12px;
  border: 1px solid rgba(0, 0, 0, 0.23);
  border-radius: 4px;
  font-size: 1rem;
  width: 100%;
}

:deep(.dp__input:hover) {
  border-color: rgba(0, 0, 0, 0.87);
}

:deep(.dp__input:focus) {
  border-color: #1976d2;
  border-width: 2px;
  outline: none;
}
</style>
